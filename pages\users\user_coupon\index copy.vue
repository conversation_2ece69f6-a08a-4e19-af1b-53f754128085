<template>
	<view :data-theme="theme">
		<view class="navbar acea-row row-around">
			<view class="item acea-row row-center-wrapper" :class="{ on: navOn === 'usable' }" @click="onNav('usable')">未使用</view>
			<view class="item acea-row row-center-wrapper" :class="{ on: navOn === 'unusable' }" @click="onNav('unusable')">已使用/过期</view>
		</view>
		<view class="coupon-box">
			<view class='coupon-list' v-if="couponsList.length">
				<view class='item acea-row row-center-wrapper' v-for='(item,index) in couponsList' :key="index" @click="goCouponsProList(item)">
					<view class='money' :class="item.validStr==='unusable'||item.validStr==='overdue'||item.validStr==='notStart' ? 'moneyGray' : 'main_bg'">
						<view class="logo">￥<text class='num'>{{item.money?Number(item.money):''}}</text></view>
						<view class="pic-num">满{{ Number(item.minPrice) }}元可用</view>
						<!-- <view class='bnt' :class="item.validStr==='unusable'||item.validStr==='overdue'||item.validStr==='notStart'?'gray':'bg_color'">{{item.validStr | validStrFilter}}</view> -->
					</view>
					<view class='text'>
						<view class='condition line2'>
							<span class="line-title" :class="item.validStr==='unusable'||item.validStr==='overdue'||item.validStr==='notStart' ? 'bg-color-huic' : 'bg-color-check'" v-if="item.category === 1">商家</span>
							<span class="line-title" :class="item.validStr==='unusable'||item.validStr==='overdue'||item.validStr==='notStart' ? 'bg-color-huic' : 'bg-color-check'"  v-else-if="item.category === 2">商品</span>
							<span class="line-title" :class="item.validStr==='unusable'||item.validStr==='overdue'||item.validStr==='notStart' ? 'bg-color-huic' : 'bg-color-check'" v-else-if="item.category === 3">平台</span>
							<span >{{item.name}}</span>
						</view>
						<view class='data acea-row row-between-wrapper'>
							<view>{{item.startTime.slice(0,10)}}~{{item.endTime.slice(0,10)}}</view>
							<view class='bnt' :class="item.validStr==='unusable'||item.validStr==='overdue'||item.validStr==='notStart'?'gray':'bg_color'">{{item.validStr | validStrFilter}}</view>
						</view>
					</view>
				</view>
			</view>
			<view class='loadingicon acea-row row-center-wrapper' v-if="couponsList.length > 0 || loading">
			     <text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
			</view>
			<view class="no-coupons" v-if="!couponsList.length && !loading">
				<view class='noCommodity' v-if="!couponsList.length && !loading">
					<view class='pictrue'>
						<image src='../../../pages/aastatictoT/static/images/noCoupon.png'></image>
					</view>
					<view class="text">暂无优惠卷哦~</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getUserCoupons
	} from '@/api/api.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		filters: {
		    validStrFilter(status) {
		      const statusMap = {
		        'usable': '立即使用',
		        'unusable': '已用',
				'overdue': '过期',
				'notStart': '未开始'
		      }
		      return statusMap[status]
		    }
		},
		data() {
			return {
				couponsList: [],
				loading: false,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 20,
				navOn: 'usable',
				theme:app.globalData.theme,
			};
		},
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getUseCoupons();
					}
				},
				deep: true
			}
		},
		onLoad() {
			if (this.isLogin) {
				this.getUseCoupons();
			} else {
				toLogin();
			}
		},
		methods: {
			goCouponsProList(item){
				if(this.navOn === 'usable'){
					uni.navigateTo({
						url: '/pages/goods/coupon_goods_list/index?userCouponId=' + item.id + '&minPrice=' + item.minPrice + '&money=' + item.money
					})
				}
			},
			onNav: function(type) {
				this.navOn = type;
				this.couponsList = [];
				this.page = 1;
				this.loadend = false;
				this.getUseCoupons();
			},
			/**
			 * 获取领取优惠券列表
			 */
			getUseCoupons: function() {
				let that = this;
				if(this.loadend) return false;
				if(this.loading) return false;
				this.loading = true;
				this.loadTitle = '';
				getUserCoupons({ page: that.page, limit: that.limit, type: that.navOn}).then(res => {
					let list = res.data ? res.data.list : [];
					let loadend = list.length < that.limit;

					let couponsList = that.$util.SplitArray(list, that.couponsList);
					that.$set(that,'couponsList',couponsList);

					that.loadend = loadend;
					that.page = that.page + 1;
					that.loading = false;
					that.loadTitle = loadend ? '我也是有底线的' : '加载更多';
				}).catch(err=>{
					  that.loading = false;
					  that.loadTitle = '加载更多';
				  });
			}
		},
		/**
		  * 页面上拉触底事件的处理函数
		  */
		 onReachBottom: function () {
		   this.getUseCoupons();
		 }
	}
</script>

<style lang="scss" scoped>
	.coupon-box{
		width: 100%;
		position: absolute;
		padding-bottom: 10rpx;
		padding-bottom: calc(10rpx + constant(safe-area-inset-bottom) / 3);
		padding-bottom: calc(10rpx + env(safe-area-inset-bottom) / 3);
	}
	.navbar {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 106rpx;
		background-color: #FFFFFF;
		z-index: 9;
	
		.item {
			border-top: 5rpx solid transparent;
			border-bottom: 5rpx solid transparent;
			font-size: 30rpx;
			color: #999999;
			&.on{
				@include tab_border_bottom(theme);
				@include main_color(theme);
			}
		}
	}
	
	
	.money {
		display: flex;
		flex-direction: column;
		justify-content: center;
		font-size: 20rpx !important;
		.logo{
			font-size: 39rpx;
		}
	}
	.bg_color{
		@include main_bg_color(theme);
	}
	.pic-num {
		color: #ffffff;
		font-size: 24rpx;
	}
	.coupon-list {
		margin-top: 122rpx;
	}
	.coupon-list .item .text{
		height: 100%;
	}
	.coupon-list .item .text .condition{
		padding: 24rpx 0;
		height: 96rpx;
		margin-bottom: 8px;
	}
	.coupon-list .item .text .data {
		border-top: 1px solid #f0f0f0;
	}
	.condition .line-title {
		width: 90rpx;
		height: 40rpx !important;
		line-height: 40rpx !important;
		padding: 2rpx 10rpx;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		opacity: 1;
		border-radius: 20rpx;
		margin-right: 12rpx;
	}
	.bg-color-check {
		@include main_color(theme);
		@include coupons_border_color(theme);
		font-size: 18rpx !important;
	}
	.main_bg{
		@include main_bg_color(theme);
	}
	
	.no-coupons {
		width: 100%;
		height: 100%;
		position: fixed;
		display: flex;
		align-items: center;
		justify-content: center;
		top: -100rpx;

		.noCommodity {
			margin: 0 !important;
			padding: 0 !important;
		}
	}
</style>
