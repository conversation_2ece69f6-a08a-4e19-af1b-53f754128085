<template>
	<view :data-theme="theme">
		<tui-skeleton v-if="showSkeleton"></tui-skeleton>
		<view class="product-con tui-skeleton" :style="{visibility: showSkeleton ? 'hidden' : 'visible'}">
			<!-- 顶部导航栏 -->
			<view class='navbar' :class="opacity>0.6?'bgwhite':''">
				<view class='navbarH' :style='"height:"+navH+"rpx;"'>
					<view class='navbarCon acea-row' :style="{ paddingRight: navbarRight + 'px' }">
						<!-- #ifdef MP -->
						<view class="select_nav flex justify-center align-center" id="home"
							:style="{ top: homeTop + 'rpx' }">
							<text v-if="returnShow" class="iconfont icon-fanhui2 px-20" @tap="returns"></text>
							<text v-if="returnShow" class="nav_line"></text>
							<text class="iconfont icon-gengduo5 px-20" @tap="showNav"></text>
						</view>
						<!-- #endif -->
						<!-- #ifdef H5 -->
						<view id="home" class="home acea-row row-center-wrapper iconfont icon-xiangzuo h5_back"
							:class="opacity>0.5?'on':''" :style="{ top: homeTop + 'rpx' }" 
							@tap="returns">
						</view>
						<!-- #endif -->
						<!-- #ifdef H5 || APP-PLUS -->
						<view class="right_select" :style="{ top: homeTop + 'rpx' }" @tap="showNav">
							<text class="iconfont icon-gengduo2"></text>
						</view>
						<!-- #endif -->
					</view>
				</view>
			</view>

			<!-- 弹窗导航列表 -->
			<view class="dialog_nav" v-show="currentPage" :style="{ top: navH + 'rpx' }">
				<view class="dialog_nav_item" :class="item.after" v-for="(item,index) in selectNavList" :key="index"
					@click="linkPage(item.url)">
					<text class="iconfont" :class="item.icon"></text>
					<text class="pl-20">{{item.name}}</text>
				</view>
			</view>

			<!-- 商品主要内容 -->
			<view class="main-content">
				<!-- 商品轮播图 -->
				<view class="product-swiper">
					<view v-if="isGlassesProduct" class="glasses-try-on" @click="virtualWear">
						<image src="/static/images/glasses.png" class="glasses-img" mode="aspectFit"></image>
					</view>
					<productConSwiper class="tui-skeleton-rect" :imgUrls="sliderImage" :videoline="videoLink"
						@videoPause="videoPause"></productConSwiper>
				</view>

				<!-- 商品信息卡片 -->
				<view class="product-info-card">
					<!-- 渐变背景区域：包含标签页切换、价格、优惠券、标题、描述 -->
					<view class="gradient-section">
						<!-- 标签页切换 -->
						<view class="tab-header">
							<view class="tab-left">
								<view class="tab-item" :class="currentTab === 'info' ? 'active' : ''" @click="switchTab('info')">
									商品信息
								</view>
								<view class="tab-item" :class="currentTab === 'comment' ? 'active' : ''" @click="switchTab('comment')">
									相关评论
								</view>
							</view>
							<view class="tab-right">
								<view class="share-poster-btn" @click="sharePosters">
									<image src="/static/images/share.png" class="share-icon"></image>
								</view>
							</view>
						</view>

						<!-- 商品信息内容 -->
						<view v-if="currentTab === 'info'" class="info-content">
							<!-- 价格区域 -->
							<view class="price-section">
								<!-- 普通价格显示（type=0或无营销活动） -->
								<view v-if="!marketingActivity || marketingActivity.type == '0'" class="price-row">
									<view class="price-left">
										<view class="current-price">
											¥<text class="price-integer">{{(attr.productSelect.price).toString().split('.')[0]}}</text><text class="price-decimal" v-if="(attr.productSelect.price).toString().includes('.')">{{(attr.productSelect.price).toString().substring((attr.productSelect.price).toString().indexOf('.'))}}</text>
										</view>
										<view class="original-price" v-if="attr.productSelect.otPrice || true">
											¥{{attr.productSelect.otPrice}}
										</view>
									</view>
									<view class="stock-info">
										已售{{Math.floor(productInfo.sales) + Math.floor(productInfo.ficti)}}件
									</view>
								</view>
								
								<!-- 营销活动价格显示（type=1,2,3） -->
								<view v-if="marketingActivity && (marketingActivity.type == '1' || marketingActivity.type == '2' || marketingActivity.type == '3')" class="activity-price-section">
									<view class="activity-bg">
										<view class="activity-main-layout">
											<view class="activity-left-content">
												<view class="activity-header">
													<view class="discount-badge">
														<!-- 根据活动类型显示不同的文字 -->
														<text v-if="marketingActivity.type == '1'" style="font-weight: bold;color: #ffffff;font-size: 35rpx;">限时折扣</text>
														<text v-if="marketingActivity.type == '2'" style="font-weight: bold;color: #ffffff;font-size: 35rpx;">限时秒杀</text>
														<text v-if="marketingActivity.type == '3'" style="font-weight: bold;color: #ffffff;font-size: 35rpx;">新人活动</text>
														<!-- 只有折扣活动才显示几折 -->
														<view v-if="marketingActivity.type == '1'" class="discount-value" style="background-image: url('/static/images/discountbg.png');">
															{{Math.round(marketingActivity.discount * 10)}}折
														</view>
													</view>
													<view class="activity-stock-info">
														已售{{Math.floor(productInfo.sales) + Math.floor(productInfo.ficti)}}件
													</view>
												</view>
												<view class="activity-price-row">
													<!-- 根据活动类型显示对应的价格 -->
													<view class="activity-current-price">
														¥<text class="activity-price-integer">{{getActivityPrice().toString().split('.')[0]}}</text><text class="activity-price-decimal" v-if="getActivityPrice().toString().includes('.')">{{getActivityPrice().toString().substring(getActivityPrice().toString().indexOf('.'))}}</text>
													</view>
													<view class="activity-original-price" v-if="attr.productSelect.otPrice || true">
														¥{{attr.productSelect.otPrice}}
													</view>
												</view>
											</view>
											<view class="activity-countdown">
												<view class="countdown-label">
													{{marketingActivity.isOpen ? '距活动结束' : '距活动开启'}}
												</view>
												<view class="countdown-time">
													还剩 {{formatCountdown(countdownTime)}}
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 商品佣金区域 -->
							<view class="brokerage-section" v-if="productInfo.brokerage && parseFloat(productInfo.brokerage) > 0.00">
								<view class="brokerage-content">
									<text class="brokerage-symbol">¥</text><text class="brokerage-integer">{{productInfo.brokerage.toString().split('.')[0]}}</text><text class="brokerage-decimal" v-if="productInfo.brokerage.toString().includes('.')">{{productInfo.brokerage.toString().substring(productInfo.brokerage.toString().indexOf('.'))}}</text>
								</view>
							</view>

							<!-- 优惠券区域 -->
							<view class="coupon-section" v-if="productCouponList.length > 0">
								<view
									v-for="(coupon, index) in productCouponList.slice(0, 2)"
									:key="index"
									class="coupon-item"
								>
									{{ coupon.text }}
								</view>
								<view class="receive-btn" @click="couponTap">
									领券
									<text class="iconfont icon-jiantou" style="font-size: 22rpx;"></text>
								</view>
							</view>

							<!-- 商品标题 -->
							<view class="product-title">
								{{productInfo.name}}
							</view>

							<!-- 商品描述 -->
							<view class="product-desc">
								{{productInfo.intro}}
							</view>
						</view>

						<!-- 相关评论内容 -->
						<view v-if="currentTab === 'comment'" class="comment-content">
							<view class="comment-summary">
								<view class="praise-rate">
									好评率 {{replyChance || 0}}%
								</view>
								<navigator class="view-all" :url='"/pages/goods/goods_comment_list/index?productId="+id'>
									查看全部评论
									<text class="iconfont icon-jiantou"></text>
								</navigator>
							</view>
							<view class="comment-list">
								<block v-if="replyCount">
									<userEvaluation :reply="reply"></userEvaluation>
								</block>
								<view v-else class="no-comment">
									暂无评论
								</view>
							</view>
						</view>
					</view>

					<!-- 其他模块区域：选择规格、服务保障、配送信息、评论、详情 -->
					<view v-if="currentTab === 'info'" class="other-modules">
						<!-- 选择规格、服务保障、配送信息模块 -->
						<view class="info-module">
							<!-- 选择规格 -->
							<view class="spec-section" @click="selecAttr">
								<view class="spec-label">选择</view>
								<view class="spec-value">{{attrValue}}</view>
								<view class="spec-more">
									查看其他规格
									<text class="iconfont icon-jiantou" style="font-size: 22rpx;"></text>
								</view>
							</view>

							<!-- 服务保障 -->
							<view class="service-section">
								<view class="service-item">
									<view class="service-label">服务</view>
									<view class="service-value">7天无理由退货(定制类不支持)</view>
									<view class="contact-service" @click="kefuClick">
										联系客服
										<text class="iconfont icon-jiantou" style="font-size: 22rpx;"></text>
									</view>
								</view>
							</view>

							<!-- 配送信息 -->
							<view class="delivery-section">
								<view class="delivery-item">
									<view class="delivery-label">配送</view>
									<view class="delivery-value">{{deliveryText}}</view>
								</view>
							</view>
						</view>



						<!-- 评论模块 -->
						<view class="info-module comment-info-module">
							<view class="comment-header-section">
								<view class="comment-header-row">
									<view class="comment-title">评论 ({{replyCount}})</view>
									<navigator class="view-all-btn" :url='"/pages/goods/goods_comment_list/index?productId="+id'>
										<text class="iconfont icon-jiantou"></text>
									</navigator>
								</view>
							</view>

							<!-- 评论内容 -->
							<view class="comment-item-wrapper" v-if="reply && reply.length > 0">
								<view class="comment-item">
									<!-- 头像和用户信息行 -->
									<view class="user-header-row">
										<view class="user-avatar">
											<image :src="reply[0].avatar || '/static/images/mermoren.png'" mode="aspectFill"></image>
										</view>
										<view class="user-info-right">
											<view class="user-first-line">
												<view class="username">{{reply[0].nickname}}</view>
												<view class="user-specs">规格：{{reply[0].sku}}</view>
											</view>
											<view class="comment-date">{{reply[0].add_time}}</view>
										</view>
									</view>

									<!-- 星标评分 -->
									<view class="star-rating">
										<text class="star" v-for="n in 5" :key="n" :class="n <= (reply[0].product_score || 3) ? 'active' : ''">★</text>
									</view>

									<!-- 评论内容 -->
									<view class="comment-text">
										{{reply[0].comment }}
									</view>

									<!-- 商家回复 -->
									<view class="merchant-reply" v-if="reply[0].merchantReplyContent">
										<view class="reply-label">商家回复：</view>
										<view class="reply-content">{{reply[0].merchantReplyContent }}</view>
									</view>
								</view>
							</view>

							<!-- 无评论状态 -->
							<view class="no-comment-wrapper" v-else>
								<view class="no-comment-text">暂无评论</view>
							</view>
						</view>

						<!-- 详情模块 -->
						<view class="info-module detail-info-module">
							<view class="detail-header-section">
								<view class="detail-header-row">
									<view class="detail-title">详情</view>
								</view>
							</view>

							<!-- 详情内容 -->
							<view class="detail-content">
								<jyf-parser :html="description" ref="article" :tag-style="tagStyle"></jyf-parser>
							</view>
						</view>
					</view>


				</view>
			</view>

			<!-- 底部操作栏 -->
			<view class='footer acea-row row-between-wrapper'>
				<!-- #ifdef MP -->
				<button hover-class='none' class='item tui-skeleton-rect' @click="kefuClick"
					v-if="chatConfig.telephone_service_switch === 'true'">
					<view class='iconfont icon-kefu'></view>
					<view>客服</view>
				</button>
				<button open-type="contact" hover-class='none' class='item tui-skeleton-rect' v-else>
					<view class='iconfont icon-kefu'></view>
					<view>客服</view>
				</button>
				<!-- #endif -->
				<!-- #ifndef MP -->
				<view class="item tui-skeleton-rect" @click="kefuClick">
					<view class="iconfont icon-kefu"></view>
					<view>客服</view>
				</view>
				<!-- #endif -->
				<block v-if="type === 'normal'">
					<view @click="setCollect" class='item tui-skeleton-rect'>
						<view class='iconfont icon-shoucang1' v-if="userCollect"></view>
						<view class='iconfont icon-shoucang' v-else></view>
						<view>收藏</view>
					</view>
					<navigator open-type='switchTab' class="animated item tui-skeleton-rect"
						:class="animated==true?'bounceIn':''" url='/pages/order_addcart/order_addcart'
						hover-class="none">
						<view class='iconfont icon-gouwuche1'>
							<text v-if="Math.floor(CartCount)>0" class='num bg_color'>{{CartCount}}</text>
						</view>
						<view>购物车</view>
					</navigator>
					<view class="bnt acea-row" v-if="attr.productSelect.stock <= 0">
						<form report-submit="true"><button class="joinCart bnts bg-color-hui tui-skeleton-rect"
								form-type="submit">加入购物车</button></form>
						<form report-submit="true"><button class="bnts bg-color-hui" form-type="submit">已售罄</button>
						</form>
					</view>
					<view class="bnt acea-row tui-skeleton-rect" v-else>
						<form @submit="joinCart" report-submit="true"><button class="joinCart bnts"
								form-type="submit">加入购物车</button></form>
						<form @submit="goBuy" report-submit="true"><button class="buy bnts"
								form-type="submit">立即购买</button>
						</form>
					</view>
				</block>
				<view class="bnt bntVideo acea-row"
					v-if="attr.productSelect.stock <= 0 && type === 'video'">
					<form report-submit="true"><button class="bnts bg-color-hui" form-type="submit">已售罄</button>
					</form>
				</view>
				<view class="bnt bntVideo acea-row"
					v-if="attr.productSelect.stock > 0 && type === 'video'">
					<form @submit="goBuy" report-submit="true"><button class="buy bnts" form-type="submit">立即购买</button>
					</form>
				</view>
			</view>

			<!-- 组件 -->
			<glassesProductWindow v-if="isGlassesProduct"
				:merId="productInfo.merId"
				:attr="attr"
				@closeAttr="onMyEvent" 
				@changeAttr="ChangeAttr"
				@attrVal="attrVal"
				@setCustomData="setCustomData"
				@getImg="showImg"
				@joinCart="joinCart"
				@goBuy="goBuy">
			</glassesProductWindow>
			<productWindow v-else 
				id='product-window'
				:attr="attr"
				:isShow='1'
				:iSplus='1'
				@myevent="onMyEvent"
				@ChangeAttr="ChangeAttr"
				@ChangeCartNum="ChangeCartNum"
				@attrVal="attrVal"
				@iptCartNum="iptCartNum"
				@getImg="showImg">
			</productWindow>
			
			<couponListWindow :coupon='coupon' :typeNum="couponDeaultType[0].useType"
				@ChangCouponsClone="ChangCouponsClone" @ChangCoupons="ChangCoupons"
				@ChangCouponsUseState="ChangCouponsUseState" @tabCouponType="tabCouponType"></couponListWindow>

			<!-- 分享按钮 -->
			<view class="generate-posters" :class="posters ? 'on' : ''">
				<view class="generateCon acea-row row-middle">
					<!-- 微信好友 -->
					<!-- #ifndef MP -->
					<button class="share-item" hover-class="none" v-if="weixinStatus === true" @click="H5ShareBox = true">
						<view class="share-icon">
							<image src="/static/images/icon_wechat.png" mode="aspectFit"></image>
						</view>
						<view class="share-text">微信好友</view>
					</button>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<button class="share-item" open-type="share" hover-class="none">
						<view class="share-icon">
							<image src="/static/images/icon_wechat.png" mode="aspectFit"></image>
						</view>
						<view class="share-text">微信好友</view>
					</button>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<view class="share-item" @click="appShare('WXSceneSession')">
						<view class="share-icon">
							<image src="/static/images/icon_wechat.png" mode="aspectFit"></image>
						</view>
						<view class="share-text">微信好友</view>
					</view>
					<!-- #endif -->

					<!-- 朋友圈 -->
					<!-- #ifdef APP-PLUS -->
					<view class="share-item" @click="appShare('WXSenceTimeline')">
						<view class="share-icon">
							<image src="/static/images/icon_circle.png" mode="aspectFit"></image>
						</view>
						<view class="share-text">朋友圈</view>
					</view>
					<!-- #endif -->
					<!-- #ifndef APP-PLUS -->
					<view class="share-item" @click="getpreviewImage">
						<view class="share-icon">
							<image src="/static/images/icon_circle.png" mode="aspectFit"></image>
						</view>
						<view class="share-text">朋友圈</view>
					</view>
					<!-- #endif -->

					<!-- 保存海报 -->
					<view class="share-item" @click="savePosterPath">
						<view class="share-icon">
							<image src="/static/images/icon_Poster.png" mode="aspectFit"></image>
						</view>
						<view class="share-text">保存海报</view>
					</view>

					<!-- 取消 -->
					<view class="share-item" @click="posterImageClose">
						<view class="share-icon">
							<image src="/static/images/icon_cancel.png" mode="aspectFit"></image>
						</view>
						<view class="share-text">取消</view>
					</view>
				</view>
			</view>

			<!-- 查看规格商品图 -->
			<cus-previewImg ref="cusPreviewImg" :list="skuArr" @changeSwitch="changeSwitch" @shareFriend="listenerActionSheet"/>
		    
			<!-- 海报展示 -->
			<view class='poster-pop' v-if="canvasStatus">
				<image :src='imagePath'></image>
			</view>
			<view class="canvas" v-else>
				<canvas :style="{width: canvasDisplayWidth + 'px', height: canvasDisplayHeight + 'px'}" :width="canvasWidth" :height="canvasHeight" canvas-id="firstCanvas"></canvas>
				<canvas canvas-id="qrcode" :style="{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}" />
			</view>

			<!-- 发送给朋友图片 -->
			<view class="share-box" v-if="H5ShareBox">
				<image src="/static/images/share-info.png" @click="H5ShareBox = false"></image>
			</view>

			<!-- 保障服务弹窗 -->
			<tui-drawer mode="bottom" :visible="assureDrawer" @close="closeAssure">
				<view class="ensure">
					<view @click="closeAssure" class="title">保障服务<text class="iconfont icon-guanbi5"></text></view>
					<view class="list">
						<view class="item acea-row" v-for="(item,index) in guaranteeList" :key="index">
							<view class="pictrue">
								<text class="iconfont icon-gou2"></text>
							</view>
							<view class="text">
								<view class="name">{{item.name}}</view>
								<view>{{item.content}}</view>
							</view>
						</view>
					</view>
					<view class="bnt" @click="assureDrawer = false">完成</view>
				</view>
			</tui-drawer>

			<!-- 分享弹窗mask -->
			<view class="mask" v-if="posters" @click="closePosters"></view>
			<!-- 海报弹窗mask -->
			<view class="mask" v-if="canvasStatus"></view>
			<!-- 导航列表弹窗mask -->
			<view class="mask_transparent" v-if="currentPage" @touchmove="hideNav" @click="hideNav()"></view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'
	import store from '@/store';
	import {
		HTTP_H5_URL
	} from '@/config/app.js';
	import {
		spread
	} from "@/api/user";
	import {
		getProductDetail,
		collectAdd,
		collectCancel,
		postCartAdd,
		getReplyList,
		getReplyConfig,
		getProductGood,
		getReplyProduct,
		getCategoryList
	} from '@/api/product.js';
	import {
		getCoupons
	} from '@/api/api.js';
	import {
		merCustomerApi
	} from '@/api/merchant.js';
	import {
		getCartCounts,
		getCouponList
	} from '@/api/order.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		imageBase64
	} from "@/api/public";
	import productConSwiper from '../components/productConSwiper/index.vue';
	import couponListWindow from '@/components/couponListWindow';
	import productWindow from '@/components/productWindow';
	import glassesProductWindow from '@/components/glassesProductWindow';
	import userEvaluation from '@/components/userEvaluation';
	import cusPreviewImg from '@/components/cus-previewImg/cus-previewImg.vue'
	import merHome from '@/components/merHome/index.vue'
	import {
		silenceBindingSpread
	} from "@/utils";
	import parser from "@/components/jyf-parser/jyf-parser";
	import {
		computeUser
	} from "@/api/user.js";
	import tuiDrawer from '@/components/base/tui-drawer.vue';
	import tuiSkeleton from '@/components/base/tui-skeleton.vue';
	import easyLoadimage from '@/components/base/easy-loadimage.vue';
	// #ifdef MP
	import {
		base64src
	} from '@/utils/base64src.js'
	import {
		mpQrcode
	} from '@/api/api.js';
	// #endif
	let app = getApp();
	import {
		setThemeColor
	} from '@/utils/setTheme.js'
	import {
		Debounce
	} from '@/utils/validate.js'
	import {
		chatConfig
	} from '@/utils/consumerType.js'
	import { autoFollow } from '@/libs/merchant.js';
	export default {
		components: {
			productConSwiper,
			couponListWindow,
			productWindow,
			glassesProductWindow,
			userEvaluation,
			cusPreviewImg,
			merHome,
			"jyf-parser": parser,
			tuiDrawer,
			tuiSkeleton,
			easyLoadimage
		},
		data() {
			let that = this;
			return {
				showSkeleton: true, //骨架屏显示隐藏
				isNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
				//属性是否打开
				coupon: {
					coupon: false,
					type: 2,
					list: [],
					count: []
				},
				attrTxt: '请选择', //属性页面提示
				attrValue: '', //已选属性
				animated: false, //购物车动画
				id: 0, //商品id
				replyCount: 0, //总评论数量
				reply: [], //评论列表
				productInfo: {}, //商品详情
				productValue: [], //系统属性
				couponList: [], //优惠券
				cart_num: 1, //购买数量
				isOpen: false, //是否打开属性组件
				actionSheetHidden: true,
				storeImage: '', //海报产品图
				PromotionCode: '', //二维码图片
				posterbackgd: '/static/images/posterbackgd.png',
				sharePacket: {
					isState: true, //默认不显示
					touchstart: false
				}, //分销商详细
				circular: false,
				autoplay: false,
				interval: 3000,
				duration: 500,
				clientHeight: "",
				systemStore: {}, //门店信息
				good_list: [],
				replyChance: 0,
				CartCount: 0,
				isDown: true,
				posters: false,
				weixinStatus: false,
				attr: {
					cartAttr: false,
					productAttr: [],
					productSelect: {}
				},
				description: '',
				navActive: 0,
				H5ShareBox: false, //公众号分享图片
				activityH5: [],
				retunTop: true, //顶部返回
				navH: "",
				navList: [],
				opacity: 0,
				scrollY: 0,
				topArr: [],
				toView: '',
				height: 0,
				heightArr: [],
				lock: false,
				scrollTop: 0,
				tagStyle: {
					img: 'width:calc(100% - 48rpx);display:block;',
					table: 'width:calc(100% - 48rpx)',
					video: 'width:calc(100% - 48rpx)'
				},
				sliderImage: [],
				videoLink: '',
				qrcodeSize: 600,
				canvasStatus: false, //是否显示海报
				imagePath: '', //海报路径
				// Canvas相关变量
				canvasWidth: 375,
				canvasHeight: 540,
				canvasDisplayWidth: 375,
				canvasDisplayHeight: 540,
				imgTop: '',
				errT: '',
				homeTop: 20,
				navbarRight: 0,
				userCollect: false,
				options: null,
				returnShow: true, //判断顶部返回是否出现
				type: "", //视频号普通商品类型
				theme: app.globalData.theme,
				indicatorBg: '',
				shareStatus: true,
				skuArr: [],
				currentPage: false,
				selectSku: {},
				selectNavList: [{
						name: '首页',
						icon: 'icon-shouye8',
						url: '/pages/index/index',
						after: 'dialog_after'
					},
					{
						name: '搜索',
						icon: 'icon-sousuo6',
						url: '/pages/goods/goods_search/index',
						after: 'dialog_after'
					},
					{
						name: '购物车',
						icon: 'icon-gouwuche7',
						url: '/pages/order_addcart/order_addcart',
						after: 'dialog_after'
					},
					{
						name: '我的收藏',
						icon: 'icon-shoucang3',
						url: '/pages/users/user_goods_collection/index',
						after: 'dialog_after'
					},
					{
						name: '个人中心',
						icon: 'icon-gerenzhongxin1',
						url: '/pages/user/index'
					},
				],
				defaultCoupon: [],
				couponDeaultType: [{
					useType: 1
				}],
				guaranteeList: [],
				assureDrawer: false,
				merchantInfo: {},
				isShowTypeId: false,
				serviceConfig: {},
				glassesSubCategoryIdList: [],
				isGlassesProduct: false,
				currentTab: 'info', // 新增：当前选中的标签页，默认为商品信息
				marketingActivity: null, // 营销活动信息
				countdownTime: 0, // 倒计时剩余时间（秒）
				countdownTimer: null, // 倒计时定时器
				// 优惠券分页参数
				couponPage: 1,
				couponLimit: 2,
				productCouponList: [], // 商品优惠券列表
				typeList: [] // 配送类型列表：1配送 2自提 3快递
			};
		},
		computed: {
			...mapGetters(['isLogin', 'uid', 'chatUrl', 'globalData', 'productCategoryTree']),
			// 配送信息文本
			deliveryText() {
				if (!this.typeList || this.typeList.length === 0) {
					return '快递到家'; // 默认显示
				}
				
				const deliveryTypes = [];
				this.typeList.forEach(type => {
					switch(type) {
						case 1:
							deliveryTypes.push('配送');
							break;
						case 2:
							deliveryTypes.push('自提');
							break;
						case 3:
							deliveryTypes.push('快递');
							break;
					}
				});
				
				return deliveryTypes.length > 0 ? deliveryTypes.join('、') : '快递到家';
			}
		},
		watch: {
			globalData: {
				handler: function(newV, oldV) {
					this.$set(this, 'navH', this.globalData.navHeight);
				},
				deep: true
			},
			isLogin: {
				handler: function(newV, oldV) {
					let that = this;
					if (newV == true) {
						that.getCartCount();
						that.getCouponList();
						// #ifdef MP
						that.getQrcode();
						// #endif
						setTimeout(() => {
							that.defaultCoupon = that.coupon.list;
						}, 1000);
					}
				},
				deep: true
			},
			productInfo: {
				handler: function() {
					if (this.glassesSubCategoryIdList.length > 0){
						this.glassesSubCategoryIdList.forEach((item) => {
							if (this.productInfo.categoryId == item) {
								this.isGlassesProduct = true;
							}
						})
					}
					this.$nextTick(() => {});
				},
				immediate: true
			},
			productCategoryTree: {
				handler: function(categoryList) {
					if (categoryList) {
						let glassesSubCategoryIds = [];
						for (var i = 0; i < categoryList.length; i++) {
							if (categoryList[i].name.trim() == "眼镜") {
								let childList = categoryList[i].childList;
								for (var j = 0; j < childList.length; j++) {
									if (childList[j].name.trim() == "光学眼镜架") {
										let subChildList = childList[j].childList;
										for (var k = 0; k < subChildList.length; k++) {
											glassesSubCategoryIds.push(subChildList[k].id)
										}
									}
								}
							}
						}
						this.glassesSubCategoryIdList = glassesSubCategoryIds;
						if (this.productInfo && this.productInfo.categoryId){
							this.glassesSubCategoryIdList.forEach((item) => {
								if (this.productInfo.categoryId == item) {
									this.isGlassesProduct = true;
								}
							})
						}
					}
				},
				immediate: true
			}
		},
		mounted() {
			if (!this.productCategoryTree) {
				getCategoryList().then(res => {
					this.$store.commit('PRODUCTCATEGORYTREE', res.data)
				})
			}
		},
		onLoad(options) {
			this.options = options;
			let that = this;
			var pages = getCurrentPages();
			that.returnShow = pages.length === 1 ? false : true;
			if (pages.length <= 1) {
				that.retunTop = false
			}
			that.navH = this.globalData.navHeight;
			
			// #ifdef H5
			//computeUser();
			// #endif
			that.$set(that, 'theme', that.$Cache.get('theme')); //用户从分享卡片进入的场景下获取主题色配置
			uni.getSystemInfo({
				success: function(res) {
					that.height = res.windowHeight
					//res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；
					// #ifndef APP-PLUS || H5 || MP-ALIPAY
					that.navbarRight = res.windowWidth - uni.getMenuButtonBoundingClientRect().left;
					// #endif
				},
			});
			if (!options.scene && !options.id) {
				this.showSkeleton = false;
				this.$util.Tips({
					title: '缺少参数无法查看商品'
				}, {
					url: '/pages/index/index'
				});
				return;
			}
			
			if (options.spread) {
				app.globalData.spread = options.spread;
				silenceBindingSpread({spread: app.globalData.spread}, this.isLogin);
			}
			if (options.mid) {
				app.globalData.mid = options.mid;
				autoFollow(app.globalData.mid).then(() => {
					this.$store.commit('CURRENT_MERID', app.globalData.mid);
				}).catch(e => {});
			}

			// 设置商品类型
			options.type == undefined || options.type == null ? that.type = 'normal' : that.type = options.type;
			that.$store.commit("PRODUCT_TYPE", that.type);

			if (options.hasOwnProperty('id') || options.scene) {
				if (options.scene) {
					// 小程序扫码进入
					let value = this.$util.getUrlParams(decodeURIComponent(options.scene));
					let scene = this.$util.formatMpQrCodeData(value);
					this.id = scene.id;
					if (scene.spread) {
						app.globalData.spread = scene.spread;
						silenceBindingSpread({spread: app.globalData.spread}, this.isLogin);
					}
					if (scene.mid) {
						app.globalData.mid = scene.mid;
						autoFollow(app.globalData.mid).then(() => {
							this.$store.commit('CURRENT_MERID', app.globalData.mid);
						}).catch(e => {});
					}
				} else {
					this.id = options.id;
				}
			}
			this.getGoodsDetails();
			if (this.isLogin) {
				this.getCouponList();
			}
			this.getProductReplyList();
			this.getProductReplyCount();

			this.indicatorBg = setThemeColor();	
			uni.$on('backgobuy', this.backgobuy);
		},
		onUnload() {
			uni.$off('backgobuy', this.backgobuy);
			// 清除倒计时定时器
			if (this.countdownTimer) {
				clearInterval(this.countdownTimer);
				this.countdownTimer = null;
			}
		},
		onReady() {
			this.isNodes++;
			this.$nextTick(function() {
				// #ifdef MP
				const menuButton = uni.getMenuButtonBoundingClientRect();
				const query = uni.createSelectorQuery().in(this);
				query
					.select('#home')
					.boundingClientRect(data => {
						this.homeTop = menuButton.top * 2 + menuButton.height - data.height;
					})
					.exec();
				// #endif
				// #ifdef APP-PLUS
				//this.homeTop = 0;
				// #endif
			});
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		},
		/**
		 * 用户点击右上角分享
		 */
		// #ifdef MP
		onShareAppMessage: function() {
			this.$set(this, 'actionSheetHidden', !this.actionSheetHidden);
			let spread = this.uid ? this.uid : 0;
			return {
				title: this.productInfo.name || '',
				imageUrl: this.productInfo.image || '',
				path: `/pages/goods/goods_details/index?id=${this.id}&spread=${spread}&mid=${this.productInfo.merId || ''}`
			}
		},
		// #endif
		methods: {
			// 标签页切换
			switchTab(tab) {
				this.currentTab = tab;
			},
			virtualWear() {
				if (this.isLogin === false) {
					toLogin();
				} else {
					let skuList = [];
					for (let key in this.productValue) {
						if (this.productValue[key].wearImage) {
							skuList.push({
								sku: this.productValue[key].sku,
								wearImage: this.productValue[key].wearImage,
								spec: this.productValue[key].spec,
							});
						}
					}
					
					if (skuList.length > 0) {
						uni.navigateTo({
							url: '/pages/goods/glasses_virtual_wear/index?id=' +  this.id + '&skuList=' + JSON.stringify(skuList) + '&mid=' + this.productInfo.merId
						});
					} else {
						this.$util.Tips({
							title: '未找到试戴素材'
						});
					}
				}
			},
			
			backgobuy(sku) {
				let values = sku.split(',');
				for (let i = 0; i < values.length; i++) {
					this.$set(this.attr.productAttr[i], "index", values[i]);
				}
				this.ChangeAttr(sku);

				this.$set(this.attr, 'cartAttr', true);
				this.$set(this, 'isOpen', true);
			},

			// 获取客服配置信息
			getMerCustomer(id){
				merCustomerApi(id).then(res => {
					this.serviceConfig = res.data
				})
			},
			// 关闭保障服务弹窗
			closeAssure() {
				this.assureDrawer = false;
			},
			// 联系客服
			kefuClick() {
				chatConfig(this.serviceConfig)
			},
			goActivity: function(e) {
				let item = e;
				if (item.type === "1") {
					uni.navigateTo({
						url: `/pages/activity/goods_seckill_details/index?id=${item.id}`
					});
				} else if (item.type === "2") {
					uni.navigateTo({
						url: `/pages/activity/goods_bargain_details/index?id=${item.id}&startBargainUid=${this.uid}`
					});
				} else {
					uni.navigateTo({
						url: `/pages/activity/goods_combination_details/index?id=${item.id}`
					});
				}
			},
			/**
			 * 购物车手动填写
			 * 
			 */
			iptCartNum: function(e) {
				this.$set(this.attr.productSelect, 'cart_num', e ? e : 1);
			},
			// 后退
			returns: function() {
				uni.navigateBack()
			},
			showNav() {
				this.currentPage = !this.currentPage;
			},
			tap: function(index) {
				var id = "past" + index;
				var index = index;
				var that = this;
				this.$set(this, 'toView', id);
				this.$set(this, 'navActive', index);
				this.$set(this, 'lock', true);
				this.$set(this, 'scrollTop', index > 0 ? that.topArr[index] - (this.globalData.navHeight / 2) : that
					.topArr[index]);
			},
			scroll: function(e) {
				var that = this,
					scrollY = e.detail.scrollTop;
				var opacity = scrollY / 200;
				opacity = opacity > 1 ? 1 : opacity;
				that.$set(that, 'opacity', opacity);
				that.$set(that, 'scrollY', scrollY);
				if (that.lock) {
					that.$set(that, 'lock', false)
					return;
				}
				for (var i = 0; i < that.topArr.length; i++) {
					if (scrollY < that.topArr[i] - (this.globalData.navHeight / 2) + that.heightArr[i]) {
						that.$set(that, 'navActive', i)
						break
					}
				}
				that.$set(that.sharePacket, 'touchstart', true); //滑动屏幕时让分享气泡缩回
				uni.$emit('scroll');
			},
			/*
			 *去商品详情页 
			 */
			goDetail(item) {
				if (!item.activityH5) {
					uni.redirectTo({
						url: '/pages/goods/goods_details/index?id=' + item.id
					})
					return
				}
				if (item.activityH5.length == 0) {
					uni.redirectTo({
						url: '/pages/goods/goods_details/index?id=' + item.id
					})
					return
				}
				// 砍价
				if (item.activityH5 && item.activityH5.type == 2) {
					uni.redirectTo({
						url: `/pages/activity/goods_bargain_details/index?id=${item.activityH5.id}&bargain=${this.uid}`
					})
					return
				}
				// 拼团
				if (item.activityH5 && item.activityH5.type == 3) {
					uni.redirectTo({
						url: `/pages/activity/goods_combination_details/index?id=${item.activityH5.id}`
					})
					return
				}
				// 秒杀
				if (item.activityH5 && item.activityH5.type == 1) {
					uni.redirectTo({
						url: `/pages/activity/goods_seckill_details/index?id=${item.activityH5.id}`
					})
					return
				}
			},
			// 微信登录回调
			onLoadFun: function(e) {
				this.getCouponList();
				this.getCartCount();
			},
			ChangCouponsClone: function() {
				this.$set(this.coupon, 'coupon', false)
			},
			/**
			 * 购物车数量加和数量减
			 * 
			 */
			ChangeCartNum: function(changeValue) {
				//changeValue:是否 加|减
				//获取当前变动属性
				let productSelect = this.productValue[this.attrValue];
				//如果没有属性,赋值给商品默认库存
				if (productSelect === undefined && !this.attr.productAttr.length)
					productSelect = this.attr.productSelect;
				//无属性值即库存为0；不存在加减；
				if (productSelect === undefined) return;
				let stock = productSelect.stock || 0;
				let num = this.attr.productSelect;
				if (changeValue) {
					num.cart_num++;
					if (num.cart_num > stock) {
						this.$set(this.attr.productSelect, "cart_num", stock);
						this.$set(this, "cart_num", stock);
					}
				} else {
					num.cart_num--;
					if (num.cart_num < 1) {
						this.$set(this.attr.productSelect, "cart_num", 1);
						this.$set(this, "cart_num", 1);
					}
				}
			},
			attrVal(val) {
				this.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val
					.indexn]);
			},
			/**
			 * 属性变动赋值
			 * 
			 */
			ChangeAttr: function(res) {
				let productSelect = this.productValue[res];
				this.$set(this, "selectSku", productSelect);
				if (productSelect) {
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", productSelect.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', productSelect.otPrice);
					this.$set(this, "attrValue", res);
					this.$set(this, "attrTxt", "已选择");
				} else {
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", 0);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", this.productInfo.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', this.productInfo.otPrice);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				}
			},
			
			setCustomData: function(res) {
				this.$set(this.attr, "customData", res);
			},

			/**
			 * 领取完毕移除当前页面领取过的优惠券展示
			 */
			ChangCoupons: function(e) {
				let coupon = e;
				let couponList = this.$util.ArrayRemove(this.couponList, 'id', coupon.id);
				this.$set(this, 'couponList', couponList);
				this.getCouponList();
			},

			setClientHeight: function() {
				let that = this;
				if (!that.good_list.length) return;
				let view = uni.createSelectorQuery().in(this).select("#list0");
				view.fields({
					size: true,
				}, data => {
					that.$set(that, 'clientHeight', data.height + 20)
				}).exec();
			},
			/**
			 * 优品推荐
			 * 
			 */
			getGoods() {
				getProductGood().then(res => {
					let good_list = res.data.list || [];
					let count = Math.ceil(good_list.length / 6);
					let goodArray = new Array();
					for (let i = 0; i < count; i++) {
						let list = good_list.slice(i * 6, i * 6 + 6);
						if (list.length) goodArray.push({
							list: list
						});
					}
					this.$set(this, 'good_list', goodArray);
					let navList = ['商品', '评价',  '详情'];
					// if (goodArray.length) {
					// 	navList.splice(2, 0, '店铺')
					// }
					this.$set(this, 'navList', navList);
					this.$nextTick(() => {
						if (good_list.length) {
							// #ifndef APP-PLUS
							this.setClientHeight();
							// #endif
							// #ifdef APP-PLUS
							setTimeout(() => {
								this.setClientHeight();
							}, 1000)
							// #endif
						};
					})
				});
			},
			/**
			 * 获取产品详情
			 * 
			 */
			getGoodsDetails: function() {
				let that = this;
				getProductDetail(that.id, that.type,1).then(res => {
					let data = res.data;
					let productInfo = data.productInfo;
					// 字符串数组转数组；
					let arrayImg = productInfo.sliderImage;
					let sliderImage = JSON.parse(arrayImg);
					if (that.getFileType(sliderImage[0]) == 'video') {
						//判断轮播图第一张是否是视频，如果是，就赋值给videoLink，并且将其在轮播图中删除
						this.$set(this, 'videoLink', sliderImage[0]);
						sliderImage.splice(0, 1);
					}
					that.$set(that, 'sliderImage', sliderImage);
					that.$set(that, 'productInfo', productInfo);
					that.$set(that, 'merchantInfo', data.merchantInfo);
					
					// 处理配送类型列表
					if (data.typeList && Array.isArray(data.typeList)) {
						that.$set(that, 'typeList', data.typeList);
					} else {
						that.$set(that, 'typeList', []);
					}
					
					// 处理营销活动数据
					if (data.marketingActivity) {
						that.$set(that, 'marketingActivity', data.marketingActivity);
						// 启动倒计时
						that.startCountdown();
					} else {
						that.$set(that, 'marketingActivity', null);
					}
					that.$set(that, 'description', productInfo.content);
					that.$set(that, 'userCollect', data.userCollect);
					that.$set(that.attr, 'productAttr', data.productAttr);
					that.$set(that, 'productValue', data.productValue);
					that.$set(that, 'guaranteeList', data.guaranteeList ? data.guaranteeList : []);
					that.getMerCustomer(productInfo.merId)
					for (let key in data.productValue) {
						let obj = data.productValue[key];
						that.skuArr.push(obj)
					}
					this.$set(this, "selectSku", that.skuArr[0]);
					// that.$set(that.sharePacket, 'priceName', data.priceName);
					// that.$set(that.sharePacket, 'isState', (data.priceName != "0" && data.priceName!==null) ? false : true);
					// that.$set(that, 'activityH5', data.activityAllH5 ? data.activityAllH5 : []);
					uni.setNavigationBarTitle({
						title: productInfo.name.substring(0, 7) + "..."
					})

					let productAttr = this.attr.productAttr.map(item => {
						return {
							attrName: item.attrName,
							attrValues: item.attrValues.split(','),
							id: item.id,
							isDel: item.isDel,
							productId: item.productId,
							type: item.type
						}
					});
					this.$set(this.attr, 'productAttr', productAttr);

					var navList = ['商品', '评价','详情'];
					// if (this.merchantInfo) {
					// 	navList.splice(2, 0, '推荐')
					// }
					that.$set(that, 'navList', navList);
					if (that.isLogin) {
						that.getCartCount();
						//#ifdef H5
						that.make(that.uid);
						that.ShareInfo();
						this.getImageBase64(this.productInfo.image);
						// #endif
						// #ifdef MP
						that.getQrcode();
						// #endif
					};
					setTimeout(function() {
						that.infoScroll();
					}, 500);
					// #ifdef MP
					that.imgTop = data.productInfo.image
					// #endif
					// #ifndef H5
					that.downloadFilestoreImage();
					// #endif
					that.DefaultSelect();

					// 确保底部操作栏显示 - 强制设置必要的数据
					if (!that.type || that.type === '') {
						that.type = 'normal';
					}

					// 确保 attr.productSelect 有基本的属性
					if (!that.attr.productSelect || Object.keys(that.attr.productSelect).length === 0) {
						that.$set(that.attr, 'productSelect', {
							name: that.productInfo.name || '',
							image: that.productInfo.image || '',
							price: that.productInfo.price || 0,
							stock: that.productInfo.stock || 0,
							unique: that.productInfo.id || '',
							cart_num: 1,
							vipPrice: that.productInfo.vipPrice || 0,
							otPrice: that.productInfo.otPrice || 0
						});
					}

					this.showSkeleton = false;

					// 获取优惠券列表
					if (that.isLogin) {
						that.getCouponList();
					}

					setTimeout(() => {
						this.defaultCoupon = this.coupon.list;
					}, 1000);
					
					setTimeout(() => {
						if (!this.merchantInfo.isSwitch || !this.productInfo.isShow) {
							if (!this.merchantInfo.isSwitch) {
								this.$util.Tips({
									title: '该商品已经下架'
								}, {
									url: '/pages/index/index'
								});
							}
						}
					}, 1000);
				}).catch(err => {
					this.showSkeleton = false;

					//状态异常返回上级页面
					if (this.returnShow) {
						this.$util.Tips({
							title: err.toString()
						}, {
							tab: 3,
							url: 1
						});
					} else {
						this.$util.Tips({
							title: err.toString()
						}, {
							url: '/pages/index/index'
						});
					}
				})
			},
			getProductReplyList: function() {
				getReplyProduct(this.id).then(res => {
					this.reply = res.data.productReply ? [res.data.productReply] : [];
				})
			},
			getProductReplyCount: function() {
				let that = this;
				getReplyConfig(that.id).then(res => {
					that.$set(that, 'replyChance', res.data.replyChance * 100);
					that.$set(that, 'replyCount', res.data.sumCount);
				});
			},
			infoScroll: function() {
				var that = this,
					topArr = [],
					heightArr = [];
				for (var i = 0; i < that.navList.length; i++) { //productList
					//获取元素所在位置
					var query = uni.createSelectorQuery().in(this);
					var idView = "#past" + i;
					if ((!this.replyCount && !that.merchantInfo && i == 1) || (this.replyCount && !that.merchantInfo && i == 2) || (!this.replyCount && that.good_list.length && i == 2)) {
						idView = "#past" + 3;
					}
					if (!this.replyCount && that.merchantInfo && i == 1) {
						idView = "#past" + 2;
					}
					query.select(idView).boundingClientRect();
					query.exec(function(res) {
						var top = res[0].top;
						var height = res[0].height;
						topArr.push(top);
						heightArr.push(height);
						that.$set(that, 'topArr', topArr);
						that.$set(that, 'heightArr', heightArr);
					});
				};
			},
			/**
			 * 默认选中属性
			 * 
			 */
			DefaultSelect: function() {
				let productAttr = this.attr.productAttr;
				let value = [];
				for (let key in this.productValue) {
					if (this.productValue[key].stock > 0) {
						value = this.attr.productAttr.length ? key.split(",") : [];
						break;
					}
				}
				for (let i = 0; i < value.length; i++) {
					this.$set(productAttr[i], "index", value[i]);
				}

				//sort();排序函数:数字-英文-汉字；
				let productSelect = this.productValue[value.join(",")];
				if (productSelect && productAttr.length) {
					this.$set(this.attr.productSelect, "name", this.productInfo.name);
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", productSelect
					.vipPrice); //attr.productSelect.otPrice
					this.$set(this.attr.productSelect, 'otPrice', productSelect.otPrice);
					this.$set(this, "attrValue", value.join(","));
					this.$set(this, "attrTxt", "已选择");
				} else if (!productSelect && productAttr.length) {
					this.$set(this.attr.productSelect, "name", this.productInfo.name);
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", 0);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", this.productInfo.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', this.productInfo.otPrice);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				} else if (!productSelect && !productAttr.length) {
					this.$set(this.attr.productSelect, "name", this.productInfo.name);
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", this.productInfo.stock);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id || "");
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", this.productInfo.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', this.productInfo.otPrice);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				}
			},
			/**
			 * 获取优惠券
			 *
			 */
			getCouponList() {
				let that = this;
				// 调用新的优惠券接口
				let data = {
					category: 0,
					publisher: 0,
					merId: 0,
					page: that.couponPage,
					limit: that.couponLimit,
					productId: that.id
				};

				getCouponList(data).then(res => {
					if (res.code === 200 && res.data && res.data.list) {
						let couponList = res.data.list;
						// 处理优惠券数据，格式化显示文本
						that.productCouponList = couponList.map(coupon => {
							let text = '';
							if (coupon.couponType === 1) {
								// 满减券
								if (coupon.minPrice > 0) {
									text = `满${coupon.minPrice}减${coupon.money}`;
								} else {
									text = `立减${coupon.money}元`;
								}
							} else if (coupon.couponType === 2) {
								// 折扣券
								if (coupon.minPrice > 0) {
									text = `满${coupon.minPrice}享${coupon.discount}折`;
								} else {
									text = `${coupon.discount}折券`;
								}
							} else {
								text = coupon.name || '优惠券';
							}

							return {
								...coupon,
								text: text,
								type: `type${coupon.couponType}`,
								isReceived: coupon.isUse || false
							};
						});

						// 保持原有的coupon.list数据结构用于弹窗组件
						that.$set(that.coupon, 'list', res.data.list);
					} else {
						that.productCouponList = [];
						that.$set(that.coupon, 'list', []);
					}
				}).catch(err => {
					console.error('获取优惠券列表失败:', err);
					that.productCouponList = [];
					that.$set(that.coupon, 'list', []);
				});
			},
			async getCouponType() {
				//在onLoad只调用一次，获取默认的类型作为打开优惠券列表的参数，不会随着切换变化
				let dataList = await getCoupons({
					productId: this.id
				});
				if (dataList.length) {
					this.couponDeaultType = dataList.data;
					this.$set(this.coupon, 'type', dataList);
				}

			},
			tabCouponType(type) {
				this.$set(this.coupon, 'type', type);
				this.getCouponList();
			},

			ChangCouponsUseState(index) {
				let that = this;
				that.coupon.list[index].isUse = true;
				that.$set(that.coupon, 'list', that.coupon.list);
				that.$set(that.coupon, 'coupon', false);
			},
			/** 
			 * 
			 * 收藏商品
			 */
			setCollect: Debounce(function() {
				let that = this;
				if (this.isLogin === false) {
					toLogin();
				} else {
					if (this.userCollect) {
						collectCancel({ids: this.productInfo.id}).then(res => {
							that.$set(that, 'userCollect', !that.userCollect);
							that.$util.Tips({
								title: "取消收藏"
							});
						})
					} else {
						collectAdd(this.productInfo.id).then(res => {
							that.$set(that, 'userCollect', !that.userCollect);
							that.$util.Tips({
								title: "收藏成功"
							});
						})
					}
				}
			}),
			/**
			 * 打开属性插件
			 */
			selecAttr: function() {
				this.$set(this.attr, 'cartAttr', true);
				this.$set(this, 'isOpen', true);
			},
			/**
			 * 打开优惠券插件
			 */
			couponTap: function() {
				let that = this;
				if (that.isLogin === false) {
					toLogin();
				} else {
					that.getCouponList(); //打开弹框默认请求商品券
					that.$set(that.coupon, 'coupon', true);
				}
			},
			onMyEvent: function() {
				this.$set(this.attr, 'cartAttr', false);
				this.$set(this, 'isOpen', false);
			},
			/**
			 * 打开属性加入购物车
			 * 
			 */
			joinCart: Debounce(function(e) {
				//是否登录
				if (this.isLogin === false) {
					toLogin();
				} else {
					this.goCat(1);
				}
			}),
			/*
			 * 加入购物车
			 */
			goCat: function(num) {
				let that = this;
					
				if ((!that.attr.productSelect || that.attr.productSelect.stock === 0) && that.isOpen === true) {
					return that.$util.Tips({
						title: "产品库存不足，请选择其它"
					});
				}
				if (that.isGlassesProduct && that.isOpen === true) {
					if (!that.attr.customData || !that.attr.customData.lens) {
						return that.$util.Tips({
							title: "请按步骤选择验光单与镜片"
						});
					}
				}

				// 打开属性弹窗
				that.attr.cartAttr = !that.isOpen ? true : false;

				if (that.attr.cartAttr === true && that.isOpen === false) {
					return (that.isOpen = true);
				}
	
				if (num === 1) {
					let params = {
						productId: parseFloat(that.id),
						cartNum: parseFloat(that.attr.productSelect.cart_num),
						isNew: false,
						productAttrUnique: that.attr.productSelect !== undefined ?
							that.attr.productSelect.unique : that.productInfo.id,
						customData: that.attr.customData !== undefined ? JSON.stringify(that.attr.customData) : "",
						storeId: 1
					};
					postCartAdd(params).then(function(res) {
						that.isOpen = false;
						that.attr.cartAttr = false;

						that.$util.Tips({
							title: "添加购物车成功",
							success: () => {
								that.getCartCount(true);
							}
						});
					}).catch(res => {
						that.isOpen = false;
						return that.$util.Tips({
							title: res
						});
					});
				} else {
					this.getPreOrder();
				}
			},
			/**
			 * 获取购物车数量
			 * @param boolean 是否展示购物车动画和重置属性
			 */
			getCartCount: function(isAnima) {
				let that = this;
				const isLogin = that.isLogin;
				if (isLogin) {
					getCartCounts(true, 'total').then(res => {
						that.CartCount = res.data.count;
						//加入购物车后重置属性
						if (isAnima) {
							that.animated = true;
							setTimeout(function() {
								that.animated = false;
							}, 500);
						}
					});
				}
			},
			/**
			 * 立即购买
			 */
			goBuy: Debounce(function(e) {
				if (this.isLogin === false) {
					toLogin();
				} else {
					this.goCat(0);
				}
			}),
			/**
			 * 预下单
			 */
			getPreOrder: function() {
				if (this.attr.productSelect.cart_num < 1) {
					uni.showToast({title: '单次可购买商品数量范围为 1~99',icon: 'none'});
				} else {
					this.$Order.getPreOrder(this.type === 'normal' ? 'buyNow' : 'video', [{
						"attrValueId": parseFloat(this.attr.productSelect.unique),
						"productId": parseFloat(this.id),
						"productNum": parseFloat(this.attr.productSelect.cart_num),
						"customData": this.attr.customData !== undefined ? JSON.stringify(this.attr.customData) : ""
					}]);
				}
				this.isOpen = false;
			},
			/**
			 * 分享海报按钮点击
			 */
			sharePosters: function() {
				let that = this;

				// 检查登录状态
				if (this.isLogin === false) {
					toLogin();
					return;
				}

				// 检查二维码是否已生成
				if (!that.PromotionCode) {
					uni.showLoading({
						title: '生成二维码中...',
						mask: true
					});

					// 生成二维码并在完成后调用分享
					that.generateQrcodeAndShare();
				} else {
					// 二维码已存在，直接调用分享
					that.goPoster();
					// 显示分享弹框
					that.posters = true;
				}
			},
			
			/**
			 * 启动营销活动倒计时
			 */
			startCountdown: function() {
				if (!this.marketingActivity) return;
				
				// 清除之前的定时器
				if (this.countdownTimer) {
					clearInterval(this.countdownTimer);
				}
				
				// 计算倒计时时间
				this.calculateCountdownTime();
				
				// 启动定时器
				this.countdownTimer = setInterval(() => {
					if (this.countdownTime > 0) {
						this.countdownTime--;
					} else {
						// 倒计时结束，检查活动状态
						this.handleCountdownEnd();
					}
				}, 1000);
			},
			
			/**
			 * 计算倒计时时间
			 */
			calculateCountdownTime: function() {
				if (!this.marketingActivity) return;
				
				const now = new Date().getTime();
				let targetTime;
				
				if (this.marketingActivity.isOpen) {
					// 活动已开启，倒计时到结束时间
					targetTime = new Date(this.marketingActivity.overTime).getTime();
				} else {
					// 活动未开启，倒计时到开始时间
					targetTime = new Date(this.marketingActivity.beginTime).getTime();
				}
				
				this.countdownTime = Math.max(0, Math.floor((targetTime - now) / 1000));
			},
			
			/**
			 * 格式化倒计时显示
			 */
			formatCountdown: function(seconds) {
				if (seconds <= 0) return '00:00:00';
				
				const hours = Math.floor(seconds / 3600);
				const minutes = Math.floor((seconds % 3600) / 60);
				const secs = seconds % 60;
				
				return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
			},
			
			/**
			 * 倒计时结束处理
			 */
			handleCountdownEnd: function() {
				if (!this.marketingActivity) return;
				
				// 清除定时器
				if (this.countdownTimer) {
					clearInterval(this.countdownTimer);
					this.countdownTimer = null;
				}
				
				if (!this.marketingActivity.isOpen) {
					// 活动开始了
					this.marketingActivity.isOpen = true;
					this.calculateCountdownTime();
					this.startCountdown();
				} else {
					// 活动结束了
					if (this.marketingActivity.type == '1') {
						// 只有限时折扣活动结束时才重新获取商品详情
						console.log('限时折扣活动结束，重新获取商品信息');
						this.marketingActivity = null;
						this.getGoodsDetails();
					} else {
						// 其他活动类型结束时只清空活动数据，不重新获取商品信息
						console.log('活动结束，活动类型：', this.marketingActivity.type);
						this.marketingActivity = null;
					}
				}
			},
			
			/**
			 * 根据活动类型获取对应的价格
			 */
			getActivityPrice: function() {
				if (!this.marketingActivity) {
					return this.attr.productSelect.price;
				}
				
				switch (this.marketingActivity.type) {
					case '1': // 折扣活动
						return this.attr.productSelect.price;
					case '2': // 秒杀活动
						return this.attr.productSelect.price;
					case '3': // 新人专享活动
						return  this.attr.productSelect.price;
					default:
						return this.attr.productSelect.price;
				}
			},

			/**
			 * 生成二维码并分享
			 */
			generateQrcodeAndShare: function() {
				let that = this;

				// 根据平台生成二维码
				// #ifdef MP-WEIXIN
				that.getQrcodeWithCallback(() => {
					uni.hideLoading();
					if (that.PromotionCode) {
						that.goPoster();
						// 显示分享弹框
						that.posters = true;
					} else {
						that.$util.Tips({
							title: '二维码生成失败，请重试'
						});
					}
				});
				// #endif

				// #ifdef H5
				that.makeWithCallback(that.uid, () => {
					uni.hideLoading();
					if (that.PromotionCode) {
						that.goPoster();
						// 显示分享弹框
						that.posters = true;
					} else {
						that.$util.Tips({
							title: '二维码生成失败，请重试'
						});
					}
				});
				// #endif

				// #ifdef APP-PLUS
				that.makeWithCallback(that.uid, () => {
					uni.hideLoading();
					if (that.PromotionCode) {
						that.goPoster();
						// 显示分享弹框
						that.posters = true;
					} else {
						that.$util.Tips({
							title: '二维码生成失败，请重试'
						});
					}
				});
				// #endif
			},

			/**
			 * 分享打开
			 *
			 */
			listenerActionSheet: function() {
				if (this.isLogin === false) {
					toLogin();
				} else {
					// #ifdef H5
					if (this.$wechat.isWeixin() === true) {
						this.weixinStatus = true;
					}
					// #endif
					this.goPoster();
					// 显示分享弹框
					this.posters = true;
				}
			},
			closePosters: function() {
				this.posters = false;
				this.currentPage = false;
			},
			//隐藏海报
			posterImageClose: function() {
				this.canvasStatus = false
				this.posters = false;
			},
			//替换安全域名
			setDomain: function(url) {
				url = url ? url.toString() : '';
				//本地调试打开,生产请注销
				if (url.indexOf("https://") > -1) return url;
				else return url.replace('http://', 'https://');
			},
			//获取海报产品图（解决跨域问题，只适用于小程序）
			downloadFilestoreImage: function() {
				let that = this;
				uni.downloadFile({
					url: that.setDomain(that.productInfo.image),
					success: function(res) {
						that.storeImage = res.tempFilePath;
					},
					fail: function() {
						return that.$util.Tips({
							title: ''
						});
						that.storeImage = '';
					},
				});
			},
			// 小程序关闭分享弹窗；
			goFriend: function() {
				this.posters = false;
			},
			// 小程序二维码
			getQrcode() {
				let that = this;
				let data = {
					pid: that.uid,
					id: that.id,
					mid: this.productInfo.merId, // 当前门店
					path: 'pages/goods/goods_details/index'
				}

				mpQrcode(data).then(res => {
					base64src(res.data.code, Date.now(), res => {
						that.PromotionCode = res;
					});
				}).catch(err => {
					that.errT = err;
				});
			},

			// 小程序二维码（带回调）
			getQrcodeWithCallback(callback) {
				let that = this;
				let data = {
					pid: that.uid,
					id: that.id,
					mid: this.productInfo.merId, // 当前门店
					path: 'pages/goods/goods_details/index'
				}

				mpQrcode(data).then(res => {
					base64src(res.data.code, Date.now(), res => {
						that.PromotionCode = res;
						if (typeof callback === 'function') {
							callback();
						}
					});
				}).catch(err => {
					that.PromotionCode = 'data:image/png;base64,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';
					that.errT = err;
					if (typeof callback === 'function') {
						callback();
					}
				});
			},
			// 生成二维码；
			make(uid) {
				let href = location.href.split('?')[0] + "?id=" + this.id + "&spread=" + this.uid + "&mid=" + this.productInfo.merId;
				uQRCode.make({
					canvasId: 'qrcode',
					text: href,
					size: this.qrcodeSize,
					margin: 10,
					success: res => {
						this.PromotionCode = res;
					},
					complete: () => {},
					fail: res => {
						this.$util.Tips({
							title: '海报二维码加载失败！'
						});
					}
				})
			},

			// 生成二维码（带回调）；
			makeWithCallback(uid, callback) {
				let href = location.href.split('?')[0] + "?id=" + this.id + "&spread=" + this.uid + "&mid=" + this.productInfo.merId;
				uQRCode.make({
					canvasId: 'qrcode',
					text: href,
					size: this.qrcodeSize,
					margin: 10,
					success: res => {
						this.PromotionCode = res;
						if (typeof callback === 'function') {
							callback();
						}
					},
					complete: () => {},
					fail: res => {
						this.$util.Tips({
							title: '海报二维码加载失败！'
						});
						if (typeof callback === 'function') {
							callback();
						}
					}
				})
			},
			getImageBase64: function(images) {
				let that = this;
				imageBase64({
					url: images
				}).then(res => {
					that.imgTop = res.data.code;
				})
			},
			/**
			 * 获取产品分销二维码
			 * @param function successFn 下载完成回调
			 *
			 */
			downloadFilePromotionCode: function(successFn) {
				let that = this;
				getProductCode(that.id)
					.then(res => {
						uni.downloadFile({
							url: that.setDomain(res.data.code),
							success: function(res) {
								that.$set(that, 'isDown', false);
								if (typeof successFn == 'function') successFn && successFn(res
									.tempFilePath);
								else that.$set(that, 'PromotionCode', res.tempFilePath);
							},
							fail: function() {
								that.$set(that, 'isDown', false);
								that.$set(that, 'PromotionCode', '');
							}
						});
					})
					.catch(err => {
						that.$set(that, 'isDown', false);
						that.$set(that, 'PromotionCode', '');
					});
			},
			/**
			 * 生成海报
			 */
			goPoster: function() {
				let that = this;
				if (!that.PromotionCode) {
					uni.hideLoading();
					that.$util.Tips({
						title: '正在加载商品二维码'
					});
					return
				}
				if (!that.imgTop) {
					uni.hideLoading();
					that.$util.Tips({
						title: '无法生成商品海报！'
					});
					return
				}

				uni.showLoading({
					title: '海报生成中',
					mask: true
				});

				// 不在这里显示分享弹框，由调用方决定
				let arrImagesUrlTop = '';
				
				// 初始化canvas尺寸
				uni.getSystemInfo({
					success: function(sysInfo) {
						const screenWidth = sysInfo.screenWidth;
						const targetWidth = Math.floor(screenWidth * 0.8);
						const targetHeight = Math.floor(targetWidth * 1.44);
						
						// canvas绘图尺寸和显示尺寸都使用逻辑尺寸
						that.canvasWidth = targetWidth;
						that.canvasHeight = targetHeight;
						that.canvasDisplayWidth = targetWidth;
						that.canvasDisplayHeight = targetHeight;
					}
				});
				
				uni.downloadFile({
					url: that.imgTop,
					success: (res) => {
						arrImagesUrlTop = res.tempFilePath;
						let arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];
						let name = that.productInfo.name;
						let price = that.productInfo.price;
						setTimeout(() => {
							that.$util.PosterCanvas(arrImages, name, price, that.productInfo
								.otPrice, that.productInfo.intro,
								function(tempFilePath) {
									that.imagePath = tempFilePath;
									that.canvasStatus = true;
									uni.hideLoading();
								});
						}, 500);
					},
					fail: () => {
						uni.hideLoading();
						that.$util.Tips({
							title: '商品图片加载失败'
						});
					}
				});
			},
			// 图片预览；
			getpreviewImage: function() {
				if (this.imagePath) {
					let photoList = [];
					photoList.push(this.imagePath)
					uni.previewImage({
						urls: photoList,
						current: this.imagePath
					});
				} else {
					this.$util.Tips({
						title: '您的海报尚未生成'
					});
				}
			},
			/*
			 * 保存到手机相册
			 */
			// #ifdef MP
			savePosterPath: function() {
				let that = this;
				uni.getSetting({
					success(res) {
						if (!res.authSetting['scope.writePhotosAlbum']) {
							uni.authorize({
								scope: 'scope.writePhotosAlbum',
								success() {
									uni.saveImageToPhotosAlbum({
										filePath: that.imagePath,
										success: function(res) {
											that.posterImageClose();
											that.$util.Tips({
												title: '保存成功',
												icon: 'success'
											});
										},
										fail: function(res) {
											that.$util.Tips({
												title: '保存失败'
											});
										}
									})
								}
							})
						} else {
							uni.saveImageToPhotosAlbum({
								filePath: that.imagePath,
								success: function(res) {
									that.posterImageClose();
									that.$util.Tips({
										title: '保存成功',
										icon: 'success'
									});
								},
								fail: function(res) {
									that.$util.Tips({
										title: '保存失败'
									});
								},
							})
						}
					}
				})
			},
			// #endif
			ShareInfo() {
				let data = this.productInfo;
				let href = location.href;
				if (this.$wechat.isWeixin()) {
					href = href.indexOf("?") === -1 ? href + "?spread=" + this.uid : href + "&spread=" + this.uid;
					let configAppMessage = {
						desc: data.storeInfo,
						title: data.name,
						link: href,
						imgUrl: data.image
					};
					this.$wechat.wechatEvevt([
						"updateAppMessageShareData",
						"updateTimelineShareData",
						"onMenuShareAppMessage",
						"onMenuShareTimeline"
					], configAppMessage).then(res => {
					}).catch(err => {
						console.log(err);
					})
				}
			},
			showShare(status) {
				let that = this;
				that.$set(that.sharePacket, 'touchstart', status);
			},
			hideNav() {
				this.currentPage = false;
			},
			//下拉导航页面跳转
			linkPage(url) {
				if (url == '/pages/index/index' || url == '/pages/order_addcart/order_addcart' || url ==
					'/pages/user/index') {
					uni.switchTab({
						url
					})
				} else {
					uni.navigateTo({
						url
					})
				}
				this.currentPage = false
			},
			//点击sku图片打开轮播图
			showImg(index) {
				this.$refs.cusPreviewImg.open(this.selectSku.sku)
			},
			//滑动轮播图选择商品
			changeSwitch(e) {
				let productSelect = this.skuArr[e];
				this.$set(this, 'selectSku', productSelect);
				var skuList = productSelect.sku.split(',');
				skuList.forEach((i, index) => {
					this.$set(this.attr.productAttr[index], 'index', skuList[index]);
				})
				if (productSelect) {
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "vipPrice", productSelect.vipPrice);
					this.$set(this, "attrTxt", "已选择");
					this.$set(this, "attrValue", productSelect.sku)
				}
			},
			getFileType(fileName) {
				// 后缀获取
				let suffix = '';
				// 获取类型结果
				let result = '';
				try {
					const flieArr = fileName.split('.');
					suffix = flieArr[flieArr.length - 1];
				} catch (err) {
					suffix = '';
				}
				// fileName无后缀返回 false
				if (!suffix) {
					return false;
				}
				suffix = suffix.toLocaleLowerCase();
				// 图片格式
				const imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif'];
				// 进行图片匹配
				result = imglist.find(item => item === suffix);
				if (result) {
					return 'image';
				}
				// 匹配 视频
				const videolist = ['mp4', 'm2v', 'mkv', 'rmvb', 'wmv', 'avi', 'flv', 'mov', 'm4v'];
				result = videolist.find(item => item === suffix);
				if (result) {
					return 'video';
				}
				// 其他 文件类型
				return 'other';
			},
			videoPause() {

			},
			// #ifdef APP-PLUS
			appShare(scene) {
				let that = this
				let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
				let curRoute = routes[routes.length - 1].$page.fullPath // 获取当前页面路由，也就是最后一个打开的页面路由
				uni.share({
					provider: "weixin",
					scene: scene,
					type: 0,
					href: `${HTTP_H5_URL}${curRoute}&spread=${that.uid}`,
					title: that.productInfo.name,
					summary: that.productInfo.storeInfo,
					imageUrl: that.productInfo.image,
					success: function(res) {
						that.posters = false;
					},
					fail: function(err) {
						uni.showToast({
							title: '分享失败',
							icon: 'none',
							duration: 2000
						})
						that.posters = false;
					}
				});
			},
			// #endif
		},
	}
</script>

<style scoped lang="scss">
	// 新布局样式
	.main-content {
		background: #f5f5f5;
		margin-top: 100rpx;
		padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
		min-height: calc(100vh - 200rpx);
	}

	.product-swiper {
		height: 750rpx;
		position: relative;
		background: #fff;
	}

	.glasses-try-on {
		position: absolute;
		right: -13rpx;
		top: 70%;
		transform: translateY(-50%);
		z-index: 100;

		.glasses-img {
			width: 120rpx;
			height: 120rpx;
		}
	}

	.product-info-card {
		border-radius: 24rpx 24rpx 0 0;
		margin-top: -24rpx;
		position: relative;
		z-index: 10;
		overflow: hidden;
	}

	// 渐变背景区域：包含标签页切换、价格、优惠券、标题、描述
	.gradient-section {
		background: linear-gradient(180deg, #ffffff 24%, #f5f5f5);
	}

	// 其他模块区域：选择规格、服务保障、配送信息、评论、详情
	.other-modules {
		background: #f5f5f5;
		padding: 0 30rpx;

		.info-module {
			background: #ffffff;
			border-radius: 25rpx;
			margin-top: 24rpx;
			padding: 0 24rpx;
		}

		.spec-section {
			display: flex;
			align-items: center;
			padding: 20rpx 0;

			.spec-label {
				font-size: 26rpx;
				color: #999999;
				margin-right: 20rpx;
				min-width: 60rpx;
			}

			.spec-value {
				font-size: 26rpx;
				color: #222222;
				flex: 1;
			}

			.spec-more {
				font-size: 24rpx;
				color: #999999;
			}
		}

		.service-section {
			.service-item {
				display: flex;
				align-items: center;
				padding: 20rpx 0;

				.service-label {
					font-size: 26rpx;
					color: #999999;
					margin-right: 20rpx;
					min-width: 60rpx;
				}

				.service-value {
					font-size: 26rpx;
					color: #222222;
					flex: 1;
				}

				.contact-service {
					font-size: 24rpx;
					color: #999999;
				}
			}
		}

		.delivery-section {
			.delivery-item {
				display: flex;
				align-items: center;
				padding: 20rpx 0;

				.delivery-label {
					font-size: 26rpx;
					color: #999999;
					margin-right: 20rpx;
					min-width: 60rpx;
				}

				.delivery-value {
					font-size: 26rpx;
					color: #222222;
				}
			}
		}

		// 商品信息中的评论模块样式
		.comment-info-module {
			.comment-header-section {
				padding: 20rpx 0 0 0;

				.comment-header-row {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.comment-title {
						font-size: 26rpx;
						color: #333333;
						font-weight: 500;
					}

					.view-all-btn {
						.iconfont {
							font-size: 24rpx;
							color: #999999;
						}
					}
				}
			}

			.comment-item-wrapper {
				padding: 24rpx 0;

				.comment-item {
					.user-header-row {
						display: flex;
						align-items: flex-start;
						margin-bottom: 16rpx;

						.user-avatar {
							width: 80rpx;
							height: 80rpx;
							margin-right: 24rpx;
							flex-shrink: 0;

							image {
								width: 100%;
								height: 100%;
								border-radius: 50%;
							}
						}

						.user-info-right {
							flex: 1;
							height: 80rpx;
							display: flex;
							flex-direction: column;
							justify-content: center;

							.user-first-line {
								display: flex;
								align-items: center;
								margin-bottom: 8rpx;

								.username {
									font-size: 28rpx;
									color: #333333;
									margin-right: 16rpx;
								}

								.user-specs {
									font-size: 24rpx;
									color: #999999;
								}
							}

							.comment-date {
								font-size: 24rpx;
								color: #999999;
							}
						}
					}

					.star-rating {
						margin-bottom: 16rpx;

						.star {
							font-size: 24rpx;
							color: #E5E5E5;
							margin-right: 4rpx;

							&.active {
								color: #FFB800;
							}
						}
					}

					.comment-text {
						font-size: 28rpx;
						color: #333333;
						line-height: 1.6;
						margin-bottom: 16rpx;
					}

					.merchant-reply {
						background: #F8F8F8;
						border-radius: 20rpx;
						padding: 16rpx;

						.reply-label {
							font-size: 24rpx;
							color: #666666;
							margin-bottom: 8rpx;
						}

						.reply-content {
							font-size: 26rpx;
							color: #333333;
							line-height: 1.5;
						}
					}
				}
			}

			.no-comment-wrapper {
				padding: 60rpx 0;
				text-align: center;

				.no-comment-text {
					font-size: 28rpx;
					color: #999999;
				}
			}
		}
	}

	.tab-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 30rpx 20rpx;
		position: relative;

		.tab-left {
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;

			.tab-item:nth-child(1){
				margin-left: 80rpx;
			}

			.tab-item {
				padding: 12rpx 24rpx;
				font-size: 28rpx;
				color: #222222;
				position: relative;
				margin: 0 10rpx;
				border-radius: 30rpx;
				background: #F5F5F5;

				&.active {
					color: #fff;
					background: #222222;
					font-weight: 500;
				}
			}
		}

		.tab-right {
			display: flex;
			align-items: center;
			padding-left: 20rpx;

			.share-poster-btn {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #F5F5F5;
				border-radius: 50%;
				transition: all 0.2s;

				&:active {
					background: #E5E5E5;
				}

				.share-icon {
					width: 100%;
					height: 100%;
				}
			}
		}
	}

	.info-content {
		padding: 30rpx 30rpx 0 30rpx;

		.price-section {
			margin-bottom: 24rpx;

			.price-row {
				display: flex;
				align-items: baseline;
				justify-content: space-between;
			}

			.price-left {
				display: flex;
				align-items: baseline;
			}

			.current-price {
				color: #FF2222;
				font-size: 24rpx;
				margin-right: 16rpx;
				display: flex;
				align-items: baseline;

				.price-integer {
					font-size: 40rpx;
					font-weight: 600;
					margin-left: 4rpx;
				}

				.price-decimal {
					font-size: 24rpx;
					font-weight: 600;
				}
			}

			.original-price {
				color: #999999;
				font-size: 24rpx;
				text-decoration: line-through;
			}

			.stock-info {
				color: #999999;
				font-size: 24rpx;
			}
			
			// 营销活动价格样式
			.activity-price-section {
				position: relative;
				border-radius: 12rpx;
				overflow: hidden;
				
				.activity-bg {
					background-image: url('data:image/png;base64,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');
					background-size: 100% 100%;
					background-repeat: no-repeat;
					padding: 10rpx 20rpx;
					position: relative;
					
					.activity-main-layout {
						display: flex;
						align-items: stretch;
						justify-content: space-between;
						min-height: 100rpx;
						
						.activity-left-content {
							flex: 1;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							
							.activity-header {
								display: flex;
								align-items: center;
								
								.discount-badge {
									display: flex;
									align-items: center;
									margin-right: 24rpx;
									
									.discount-text {
										width: 100rpx;
										height: 40rpx;
									}
									
									.discount-value {
										background-size: 100% 100%;
										background-repeat: no-repeat;
										width: 80rpx;
										height: 40rpx;
										display: flex;
										align-items: center;
										justify-content: center;
										color: #222;
										font-size: 24rpx;
										font-weight: 600;
										margin-left: 12rpx;
									}
									
									// 当没有折扣标识时，调整间距
									&:not(:has(.discount-value)) {
										margin-right: 24rpx;
									}
								}
								
								.activity-stock-info {
									color: #fff;
									font-size: 24rpx;
									opacity: 0.9;
								}
							}
							
							.activity-price-row {
								display: flex;
								align-items: baseline;
								
								.activity-current-price {
									color: #fff;
									font-size: 24rpx;
									margin-right: 16rpx;
									display: flex;
									align-items: baseline;
									
									.activity-price-integer {
										font-size: 40rpx;
										font-weight: 600;
										margin-left: 4rpx;
									}
									
									.activity-price-decimal {
										font-size: 24rpx;
										font-weight: 600;
									}
								}
								
								.activity-original-price {
									color: #fff;
									font-size: 24rpx;
									text-decoration: line-through;
									opacity: 0.8;
								}
							}
						}
						
						.activity-countdown {
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							padding: 0 24rpx;
							
							.countdown-label {
								font-size: 27rpx;
								color: #222222;
								margin-bottom: 8rpx;
								font-family: PingFang SC, PingFang SC-Medium;
								font-weight: 500;
							}
							
							.countdown-time {
								font-size: 24rpx;
								color: #ff2222;
								font-weight: 600;
								padding: 4rpx 12rpx;
								border-radius: 8rpx;
								font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-Medium;
								font-weight: 500;
							}
						}
					}
				}
			}
		}

		.coupon-section {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;

			.coupon-item {
				background: #fff;
				color: #FF2222;
				border: 1px solid #FF2222;
				border-radius: 6rpx;
				padding: 6rpx 12rpx;
				font-size: 22rpx;
				margin-right: 12rpx;
			}

			.receive-btn {
				color: #FF2222;
				font-family: PingFang SC, PingFang SC-Regular;
				font-size: 26rpx;
				margin-left: auto;
			}
		}
		
		.product-title {
			font-size: 34rpx;
			color: #222222;
			line-height: 1.4;
			margin-bottom: 16rpx;
			font-family: "PingFang SC", "PingFang SC-Medium", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif;
			font-weight: 500;
		}

		.product-desc {
			font-size: 30rpx;
			color: #666666;
		}
	}

	.comment-content {
		padding: 30rpx;
		
		.comment-summary {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			.praise-rate {
				font-size: 28rpx;
				color: #333;
			}
			
			.view-all {
				font-size: 26rpx;
				color: #666;
				display: flex;
				align-items: center;
				
				.iconfont {
					font-size: 20rpx;
					margin-left: 8rpx;
				}
			}
		}
		
		.comment-list {
			.no-comment {
				text-align: center;
				color: #999;
				font-size: 28rpx;
				padding: 60rpx 0;
			}
		}
	}

	// 保持原有样式
	.superior {

		/deep/.name,
		/deep/.icon-jiantou {
			color: #333 !important;
		}

		/deep/.store {
			padding: 0 !important;
		}
		/deep/image,.easy-loadimage,image,uni-image {
			width: 86rpx;
			height: 86rpx;
			border-radius: 6px;
			opacity: 1
		}
	}

	.ensure {
		width: 100%;
		background-color: #fff;
		padding-bottom: 15rpx;
		padding-bottom: calc(15rpx+ constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);

		.title {
			font-size: 32rpx;
			color: #282828;
			text-align: center;
			margin: 38rpx 0 36rpx 0;
			position: relative;

			.iconfont {
				position: absolute;
				right: 30rpx;
				top: 0;
				font-size: 36rpx;
			}
		}

		.list {
			max-height: 750rpx;
			margin: 0 30rpx;
			overflow-x: hidden;
			overflow-y: auto;

			.item {
				margin-bottom: 52rpx;

				.pictrue {
					width: 36rpx;
					height: 36rpx;
					border-radius: 50%;
					margin-right: 30rpx;

					/deep/image,.easy-loadimage,image,uni-image {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}

				.text {
					width: 618rpx;
					color: #999999;
					font-size: 28rpx;

					.name {
						color: #333333;
						font-weight: bold;
						margin-bottom: 20rpx;
					}
				}
			}
		}

		.bnt {
			width: 690rpx;
			height: 86rpx;
			text-align: center;
			line-height: 86rpx;
			border-radius: 43rpx;
			@include main_bg_color(theme);
			font-size: 30rpx;
			color: #fff;
			margin: 0 auto;
		}
	}

	.ensure.on {
		transform: translate3d(0, 0, 0);
	}

	/deep/ .tui-drawer-container_bottom {
		border-radius: 16rpx 16rpx 0 0;
	}

	.product-con {
		min-height: 100vh;
	}

	.x-money {
		font-size: 28rpx;
		font-weight: 700;
		@include price_color(theme);

	}

	.bg-color-hui {
		background: #bbb !important;
		border-radius: 0 25px 25px 0;
	}

	.select_nav {
		height: 60rpx !important;
		border-radius: 33rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0, 0, 0, 0.07);
		color: #000;
		position: fixed;
		font-size: 18px;
		line-height: 62rpx;
		z-index: 1000;
		left: 14rpx;
	}

	.px-20 {
		padding: 0 20rpx 0;
	}

	.nav_line {
		content: '';
		display: inline-block;
		width: 1px;
		height: 34rpx;
		background: #b3b3b3;
		position: absolute;
		left: 0;
		right: 0;
		margin: auto;
	}

	.bgwhite {
		background: #fff;
	}

	.input {
		display: flex;
		align-items: center;
		/* #ifdef MP */
		width: 300rpx;
		/* #endif */
		/* #ifndef MP */
		width: 460rpx;
		/* #endif */
		height: 58rpx;
		padding: 0 0 0 30rpx;
		border: 1px solid rgba(0, 0, 0, 0.07);
		border-radius: 33rpx;
		color: #666;
		font-size: 26rpx;
		position: fixed;
		left: 0;
		right: 0;
		margin: auto;
		background: rgba(255, 255, 255, 0.3);

		.iconfont {
			margin-right: 20rpx;
			font-size: 26rpx;
			color: #666666;
		}
	}

	.container_detail {
		/* #ifdef MP */
		margin-top: 32rpx;
		/* #endif */
	}

	.tab_nav {
		height: 60rpx;
		margin-left: 184rpx;
		position: fixed;
	}

	.right_select {
		width: 50rpx;
		height: 50rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 50%;
		position: fixed;
		right: 20rpx;
		text-align: center;
		line-height: 50rpx;
		/* #ifdef APP-PLUS*/
		top: 62rpx !important;
		/* #endif */
	}

	.dialog_nav {
		position: absolute;
		/* #ifdef MP */
		left: 14rpx;
		/* #endif */
		/* #ifdef H5 || APP-PLUS*/
		right: 14rpx;
		/* #endif */
		width: 240rpx;
		background: #FFFFFF;
		box-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);
		z-index: 310;
		border-radius: 14rpx;

		&::before {
			content: '';
			width: 0;
			height: 0;
			position: absolute;
			/* #ifdef MP */
			left: 0;
			right: 0;
			margin: auto;
			/* #endif */
			/* #ifdef H5 || APP-PLUS */
			right: 8px;
			/* #endif */
			top: -9px;
			border-bottom: 10px solid #F5F5F5;
			border-left: 10px solid transparent;
			/*transparent 表示透明*/
			border-right: 10px solid transparent;
		}
	}

	.dialog_nav_item {
		width: 100%;
		height: 84rpx;
		line-height: 84rpx;
		padding: 0 20rpx 0;
		box-sizing: border-box;
		border-bottom: #eee;
		font-size: 28rpx;
		color: #333;
		position: relative;

		.iconfont {
			font-size: 32rpx;
		}
	}

	.dialog_after {
		::after {
			content: '';
			position: absolute;
			width: 172rpx;
			height: 1px;
			background-color: #EEEEEE;
			bottom: 0;
			right: 0;
		}
	}

	.pl-20 {
		padding-left: 20rpx;
	}

	.activity {
		padding: 0 20rpx;
		@include coupons_border_color(theme);
		@include main_color(theme);
		font-size: 24rpx;
		line-height: 32rpx;
		position: relative;
		margin-left: 4rpx;
	}

	.product-con .wrapper .coupon .activity:before {
		content: ' ';
		position: absolute;
		width: 7rpx;
		height: 10rpx;
		border-radius: 0 7rpx 7rpx 0;
		@include coupons_border_color(theme);
		background-color: #fff !important;
		bottom: 50%;
		left: -3rpx;
		margin-bottom: -6rpx;
		@include white_left_border;
	}

	.product-con .wrapper .coupon .activity:after {
		content: ' ';
		position: absolute;
		width: 7rpx;
		height: 10rpx;
		border-radius: 7rpx 0 0 7rpx;
		@include coupons_border_color(theme);
		background-color: #fff;
		right: -3rpx;
		bottom: 50%;
		margin-bottom: -6rpx;
		// border-right-color: #fff;
		@include white_right_border;
	}

	.justify-center {
		justify-content: center;
	}

	.align-center {
		align-items: center;
	}

	.align-baseline {
		align-items: baseline;
	}

	.bg_color {
		@include main_bg_color(theme);
	}

	.vip_icon {
		width: 44rpx;
		height: 28rpx;
	}

	.pl-2 {
		padding-left: 20rpx;
	}

	.vip_money {
		background: #FFE7B9;
		border-radius: 4px;
		font-size: 22rpx;
		color: #333;
		line-height: 28rpx;
		text-align: center;
		padding: 0 6rpx;
		box-sizing: border-box;
		margin-left: -4rpx;
	}

	.theme_price {
		@include price_color(theme);
		margin-top: 5rpx;
	}

	.activityName {
		line-height: 44rpx;
	}

	.userEvaluation {
		i {
			display: inline-block;
		}
	}

	.bntVideo {
		width: auto !important;

		.buy {
			border-radius: 50rpx !important;
		}
	}

	.attribute {
		.line1 {
			width: 600rpx;
		}
	}

	.chat-btn {
		background-color: antiquewhite !important;
	}

	.activity_pin {
		width: auto;
		height: 44rpx;
		line-height: 44rpx;
		// background: linear-gradient(90deg, rgba(233, 51, 35, 1) 0%, rgba(250, 101, 20, 1) 100%);
		@include linear-gradient(theme);
		opacity: 1;
		border-radius: 22rpx;
		padding: 0 15rpx;
		// margin-left: 19rpx;
	}

	.activity_miao {
		width: auto;
		height: 44rpx;
		line-height: 44rpx;
		padding: 0 15rpx;
		// background: linear-gradient(90deg, rgba(250, 102, 24, 1) 0%, rgba(254, 161, 15, 1) 100%);
		@include linear-gradient(theme);
		opacity: 1;
		border-radius: 22rpx;
		margin-left: 19rpx;
	}

	.iconfonts {
		color: #fff !important;
		font-size: 28rpx;
	}

	.activity_title {
		font-size: 24rpx;
		color: #fff;
	}

	.activity_kan {
		width: auto;
		height: 44rpx;
		line-height: 44rpx;
		padding: 0 15rpx;
		@include linear-gradient(theme);
		opacity: 1;
		border-radius: 22rpx;
		margin-left: 19rpx;
	}

	.mask {
		z-index: 300 !important;
	}

	.head-bar {
		background: #fff;
	}

	.generate-posters {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 0%;
		z-index: 388;
		transform: translate3d(0, 100%, 0);
		transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
	


		.generateCon {
			height: 160rpx;
			padding: 30rpx 20rpx;
			padding-bottom: calc(30rpx + constant(safe-area-inset-bottom) / 2);
			padding-bottom: calc(30rpx + env(safe-area-inset-bottom) / 2);
		}

		.share-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: none;
			border: none;
			padding: 0;
			margin: 0;

			.share-icon {
				width: 60rpx;
				height: 60rpx;
				margin-bottom: 12rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 48rpx;
					height: 48rpx;
				}

				.iconfont {
					font-size: 48rpx;
					color: #333;
				}

				.icon-weixin3 {
					color: #1AAD19;
				}

				.icon-pengyouquan {
					color: #1AAD19;
				}

				.icon-haibao {
					color: #5391f1;
				}

				.icon-guanbi5 {
					color: #999;
				}
			}

			.share-text {
				font-size: 24rpx;
				color: #ffffff;
				text-align: center;
			}
		}
	}

	.generate-posters.on {
		bottom: 8%;
		transform: translate3d(0, 0, 0);
	}

	.product-con .footer {
		position: fixed;
		bottom: 0;
		width: 100%;
		box-sizing: border-box;
		background-color: #fff;
		z-index: 277;
		border-top: 1rpx solid #f0f0f0;
		padding: 15rpx 30rpx;
		padding-bottom: calc(15rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);
	}

	.product-con .footer .item {
		font-size: 18rpx;
		color: #666;
	}

	.product-con .footer .item .iconfont {
		text-align: center;
		font-size: 40rpx;
	}

	.product-con .footer .item .iconfont.icon-shoucang1 {
		@include main_color(theme);
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 {
		font-size: 40rpx;
		position: relative;
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 .num {
		color: #fff;
		position: absolute;
		font-size: 18rpx;
		padding: 2rpx 8rpx 3rpx;
		border-radius: 200rpx;
		top: -10rpx;
		right: -10rpx;
	}

	.product-con .footer .bnt {
		width: 444rpx;
		height: 76rpx;
	}

	.product-con .footer .bnt .bnts {
		width: 202rpx;
		text-align: center;
		line-height: 76rpx;
		color: #fff;
		font-size: 28rpx;
		margin: 0 10rpx;
	}

	.product-con .footer .bnt .joinCart {
		border-radius: 20rpx;
		background: #FF8125;
		// @include left_color(theme);
	}

	.product-con .footer .bnt .buy {
		border-radius: 20rpx;
		background: #BDFD5B;
		color: #222222;
		// @include main_bg_color(theme);
	}

	.product-con .store-info {
		margin-top: 20rpx;
		background-color: #fff;
	}

	.product-con .store-info .title {
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #282828;
		height: 80rpx;
		line-height: 80rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.product-con .store-info .info {
		padding: 0 30rpx;
		height: 126rpx;
	}

	.product-con .store-info .info .picTxt {
		width: 615rpx;
	}

	.product-con .store-info .info .picTxt .pictrue {
		width: 76rpx;
		height: 76rpx;
	}

	.product-con .store-info .info .picTxt .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.product-con .store-info .info .picTxt .text {
		width: 522rpx;
	}

	.product-con .store-info .info .picTxt .text .name {
		font-size: 30rpx;
		color: #282828;
	}

	.product-con .store-info .info .picTxt .text .address {
		font-size: 24rpx;
		color: #666;
		margin-top: 3rpx;
	}

	.product-con .store-info .info .picTxt .text .address .iconfont {
		color: #707070;
		font-size: 18rpx;
		margin-left: 10rpx;
	}

	.product-con .store-info .info .picTxt .text .address .addressTxt {
		max-width: 480rpx;
	}

	.product-con .store-info .info .iconfont {
		font-size: 40rpx;
	}

	.product-con .superior {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 24rpx;
		padding-bottom: 4rpx;
		border-top-left-radius: 14rpx;
		border-top-right-radius: 14rpx;
		.title {
			height: 98rpx;
			image {
				width: 20rpx;
				height: 20rpx;
			}
			.titleTxt {
				margin: 0 10rpx;
				font-size: 30rpx;
				color: #333333;
			}
		}
	}
	.product-con .slider-banner {
		background-color: #fff;
		width: 100%;
		margin: 0 auto;
		position: relative;
		padding: 0 14rpx;
		padding-bottom: 24rpx;
		border-bottom-left-radius: 14rpx;
		border-bottom-right-radius: 14rpx;
		.list {
			width: 100%;
			.item {
				width: 33.33%;
				font-size: 26rpx;
				padding: 0 10rpx;
				padding-top: 20rpx;
				.pictrue {
					position: relative;
					height: 204rpx;
					border-radius: 6rpx;
					overflow: hidden;
					/deep/image,.easy-loadimage,image,uni-image {
						width: 100% !important;
						height: 100% !important;
						border-radius: 6rpx;
					}
				}
				.name {
					color: #282828;
					margin-top: 10rpx;
				}
			}
			.item:nth-of-type(3n) {
				margin-right: 0;
			}
		}
	}
	.product-con .footer-height {
		height: 126rpx;
		height: calc(126rpx + constant(safe-area-inset-bottom) / 2);
		height: calc(126rpx + env(safe-area-inset-bottom) / 2);
	}

	button {
		padding: 0;
		margin: 0;
		line-height: normal;
		background-color: #fff;
	}

	button::after {
		border: 0;
	}

	action-sheet-item {
		padding: 0;
		height: 240rpx;
		align-items: center;
		display: flex;
	}

	.contact {
		font-size: 16px;
		width: 50%;
		background-color: #fff;
		padding: 8rpx 0;
		border-radius: 0;
		margin: 0;
		line-height: 2;
	}

	.contact::after {
		border: none;
	}

	.action-sheet {
		font-size: 17px;
		line-height: 1.8;
		width: 50%;
		position: absolute;
		top: 0;
		right: 0;
		padding: 25rpx 0;
	}

	.canvas {
		position: fixed;
		top: -9999px;
		left: -9999px;
	}

	.poster-pop {
		position: fixed;
		width: 80vw;
		height: calc(80vw * 1.44);
		top: 48%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 399;
	}

	.poster-pop image {
		width: 100%;
		height: 100%;
		display: block;
	}

	.poster-pop .close {
		width: 46rpx;
		height: 75rpx;
		position: fixed;
		right: 0;
		top: -73rpx;
		display: block;
	}

	.poster-pop .save-poster {
		background-color: #df2d0a;
		font-size: ：22rpx;
		color: #fff;
		text-align: center;
		height: 76rpx;
		line-height: 76rpx;
		width: 100%;
	}

	.poster-pop .keep {
		color: #fff;
		text-align: center;
		font-size: 25rpx;
		margin-top: 10rpx;
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 1);
	}

	.pro-wrapper .iconn {
		background-image: url('data:image/png;base64,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');
		width: 100rpx;
		height: 100rpx;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		margin: 0 auto;
	}

	.pro-wrapper .iconn.iconn1 {
		background-image: url('data:image/png;base64,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');
	}

	.pictrue_log {
		width: 80upx;
		height: 40upx;
		border-radius: 10upx 0 12upx 0;
		line-height: 40upx;
		font-size: 24upx;
	}

	.pictrue_log_class {
		z-index: 3;
		background: -webkit-gradient(linear, left top, right top, from(rgba(246, 122, 56, 1)), to(rgba(241, 27, 9, 1)));
		background: linear-gradient(90deg, rgba(246, 122, 56, 1) 0%, rgba(241, 27, 9, 1) 100%);
		opacity: 1;
		position: absolute;
		top: 0;
		left: 0;
		color: #fff;
		text-align: center;

	}

	.tab_nav .header {
		/* #ifndef MP */
		width: 94%;
		/* #endif */
		/* #ifdef MP || APP-PLUS */
		width: 100%;
		/* #endif */
		height: 60rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #050505;
		margin: auto;
		background-color: #fff;
	}

	.icon-xiangzuo {
		/* #ifdef H5 */
		top: 20rpx !important;
		/* #endif */
	}

	.navbar .header .item {
		position: relative;
		margin: 0 25rpx;
	}

	.navbar .header .item.on:before {
		position: absolute;
		width: 60rpx;
		height: 5rpx;
		background-repeat: no-repeat;
		content: "";
		@include linear-gradient(theme);
		bottom: -10rpx;
		left: 46.5%;
		margin-left: -28rpx;
	}

	.navbar {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
		width: 100%;
	}

	.navbar .navbarH {
		position: relative;
	}

	.navbar .navbarH .navbarCon {
		position: absolute;
		bottom: 0;
		height: 100rpx;
		width: 100%;
	}

	.h5_back {
		color: #000;
		position: fixed;
		left: 20rpx;
		font-size: 26rpx;
		text-align: center;
		width: 50rpx;
		height: 50rpx;
		line-height: 50rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 50%;
	}

	.share-box {
		z-index: 1000;
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.mask_transparent {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: transparent;
		z-index: 300;
	}

	.px-12 {
		padding-left: 12rpx;
		padding-right: 12rpx;
	}

	.font-44 {
		font-size: 44rpx;
	}

	.font_color {
		@include main_color(theme);
	}

	.attrImg {
		width: 66rpx;
		height: 66rpx;
		border-radius: 6rpx;
		display: block;
		margin-right: 14rpx;
	}

	.switchTxt {
		height: 60rpx;
		flex: 1;
		line-height: 60rpx;
		box-sizing: border-box;
		background: #EEEEEE;
		padding-right: 0 24rpx 0;
		border-radius: 8rpx;
		text-align: center;
	}
	.text-666 {
		color: #666;
	}
	.text-333 {
		color: #333 !important;
	}
	.icon-gou2 {
		@include main_color(theme)
	}
	.product-bg {
		/* #ifdef APP-PLUS */
		margin-top: 52rpx !important;
		/* #endif */
	}
	.share {
		align-items: center;
	}
	.share-btn {
		display: flex;
		font-size: 28rpx;
		align-items: center;
		justify-content: center;
		.share-txt {
			margin-right: 5rpx;
			@include second_color(theme);
		}
		.share-icon {
			margin: 0 !important;
			font-size: 32rpx !important;
			@include second_color(theme);
		}
	}

	.glasses-try-on {
		position: fixed;
		right: -13rpx;
		top: 70%;
		transform: translateY(-50%);
		z-index: 100;

		.glasses-img {
			width: 120rpx;
			height: 120rpx;
		}
	}

	// 详情模块样式
	.detail-info-module {
		.detail-header-section {
			padding: 20rpx 0;
			border-bottom: 1px solid #f0f0f0;

			.detail-header-row {
				display: flex;
				align-items: center;

				.detail-title {
					font-size: 28rpx;
					color: #333333;
					font-weight: 500;
				}
			}
		}

		.detail-content {
			padding: 20rpx 0 20rpx 30rpx;

			/deep/ img {
				max-width: 100% !important;
				height: auto !important;
			}

			/deep/ p {
				margin: 0;
				padding: 8rpx 0;
			}

			/deep/ div {
				max-width: 100%;
			}
		}
	}

	// 商品佣金区域
	.brokerage-section {
		margin-bottom: 20rpx;
		
		.brokerage-content {
			background-image: url('data:image/png;base64,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');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			height: 40rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			padding: 0 24rpx;
			width: 150rpx;
			padding-left: 57rpx;
			
			.brokerage-symbol,
			.brokerage-integer,
			.brokerage-decimal {
				color: #ffffff;
			}
			
			.brokerage-symbol {
				font-size: 23rpx;
				font-weight: 500;
			}
			
			.brokerage-integer {
				font-size: 30rpx;
				font-weight: bold;
			}
			
			.brokerage-decimal {
				font-size: 24rpx;
				font-weight: 500;
			}
		}
	}
</style>