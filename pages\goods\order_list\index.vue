<template>
	<view :data-theme="theme">
		<view class="order-list-page">
			<!-- #ifndef APP-PLUS || MP -->
			<nav-bar :navTitle='navTitle' @getNavH='getNavH'></nav-bar>
			<!-- #endif -->
			<!-- 搜索栏 -->
			<view class="search-wrapper" :style="'top:' + searchTop + 'rpx'">
				<view class="search-input">
					<text class="iconfont icon-sousuo"></text>
					<input type="text" v-model="searchKeyword" placeholder="搜索订单" @confirm="handleSearch" />
				</view>
				<view class="search-btn" @click="handleSearch">搜索</view>
			</view>

			<!-- 状态tab -->
			<view class="nav-wrapper" :style="'top:' + tabTop + 'rpx'">
				<scroll-view class="scroll-view_x" scroll-x style="white-space: nowrap;" show-scrollbar="false">
					<view class="tab-item" :class='{active: orderStatus==="all"}' @click="statusClick('all')">
						<text>全部订单</text>
					</view>
					<view class="tab-item" :class='{active: orderStatus==="unpaid"}' @click="statusClick('unpaid')">
						<text>待付款</text>
					</view>
					<view class="tab-item" :class='{active: orderStatus==="unshipped"}' @click="statusClick('unshipped')">
						<text>待发货</text>
					</view>
					<view class="tab-item" :class='{active: orderStatus==="pickup"}' @click="statusClick('pickup')">
						<text>待收货/待核销</text>
					</view>
					<view class="tab-item" :class='{active: orderStatus==="evaluate"}' @click="statusClick('evaluate')">
						<text>待评价</text>
					</view>
					<view class="tab-item" :class='{active: orderStatus==="aftersale"}' @click="statusClick('aftersale')">
						<text>售后中</text>
					</view>
				</scroll-view>
			</view>

			<!-- 订单列表 -->
			<view class='order-list' :style="'margin-top:' + listTop + 'rpx'">
				<!-- 加载中状态 - 骨架屏 -->
				<view class="skeleton-container" v-if="loading && orderList.length === 0">
					<view class="skeleton-card" v-for="n in 3" :key="'skeleton-' + n">
						<!-- 订单头部骨架 -->
						<view class="skeleton-header">
							<view class="skeleton-left">
								<view class="skeleton-tag"></view>
								<view class="skeleton-merchant"></view>
							</view>
							<view class="skeleton-status"></view>
						</view>

						<!-- 商品信息骨架 -->
						<view class="skeleton-content">
							<view class="skeleton-image"></view>
							<view class="skeleton-info">
								<view class="skeleton-name"></view>
								<view class="skeleton-spec"></view>
								<view class="skeleton-quantity"></view>
							</view>
							<view class="skeleton-price"></view>
						</view>

						<!-- 订单底部骨架 -->
						<view class="skeleton-footer">
							<view class="skeleton-time"></view>
							<view class="skeleton-total"></view>
						</view>

						<!-- 操作按钮骨架 -->
						<view class="skeleton-actions">
							<view class="skeleton-btn"></view>
							<view class="skeleton-btn"></view>
						</view>
					</view>
				</view>

				<view class='order-card' v-for="(item,index) in filteredOrderList" :key="index">
					<!-- 订单头部 -->
					<view class='order-header'>
						<view class="header-left">
							<text class="delivery-tag" :class="item.deliveryType">{{ item.deliveryName }}</text>
							<text class="merchant-name">{{ item.merchantName }}</text>
						</view>
						<view v-if="item.statusText" class='order-status' :class="item.statusClass">{{ item.statusText }}</view>
					</view>
					
					<!-- 商品信息 -->
					<view @click='goOrderDetails(item.orderNo)' class='order-content'>
						<!-- 单个商品 -->
						<view v-if="item.products.length === 1" class='single-product'>
							<view class='product-image'>
								<image :src="item.products[0].image" mode="aspectFill"></image>
							</view>
							<view class='product-info'>
								<view class="product-name">{{ item.products[0].name }}</view>
								<view class="product-spec">规格: {{ item.products[0].spec }}</view>
								<view class="product-quantity">数量: {{ item.products[0].quantity }}</view>
							</view>
							<view class='product-price'>
								¥{{ item.products[0].price }}
							</view>
						</view>
						
						<!-- 多个商品 -->
						<view v-else class="multi-products">
							<scroll-view scroll-x="true" class="products-scroll">
								<image v-for="(product, i) in item.products" :key="i" :src="product.image" class="product-thumb" mode="aspectFill" />
								<view class="more-indicator" v-if="item.products.length > 4">
									<text>{{ item.products.length }}+</text>
								</view>
							</scroll-view>
						</view>
					</view>

					<!-- 订单底部信息 -->
					<view class='order-footer'>
						<view class="order-time">{{ item.orderTime }}</view>
						<view class="order-total">
							<text style="margin-right: 10rpx;">共{{ item.totalQuantity }}件商品 实付: </text>
							<text class='total-price'>¥{{ item.totalPrice }}</text>
						</view>
					</view>
					
					<!-- 操作按钮 -->
					<view class='order-actions'>
						<!-- 已取消/已完成订单 -->
						<template v-if="item.status === 9 || item.status === 6">
							<view class='action-btn secondary' @click='deleteOrder(item.orderNo, index)'>删除订单</view>
							<view class='action-btn primary' @click='orderAgain(item.orderNo)'>再来一单</view>
						</template>
						
						<!-- 待付款订单 -->
						<template v-if="item.status === 0 && !item.paid">
							<view class='action-btn primary countdown-btn' @click='goPay(item)'>
								<text>待支付 {{ formatCountdown(item.countdown) }}</text>
							</view>
						</template>
						
						<!-- 待收货订单 -->
						<template v-if="item.status === 4">
							<!-- <view class='action-btn primary' @click='showPickupCode(item)'>查看物流</view> -->
						</template>
						
						<!-- 售后中订单 -->
						<template v-if="item.status === 7">
							<view class='action-btn secondary' @click='viewAftersaleDetails(item)'>查看详情</view>
							<view class='action-btn primary' @click='contactService(item)'>联系客服</view>
						</template>
						
						<!-- 待发货订单 - 没有按钮 -->
					</view>
				</view>

				<!-- 空状态 -->
				<!-- <view class='empty-state' v-if="filteredOrderList.length === 0">
					<image src='/pages/aastatictoT/static/images/noOrdernow.png' class="empty-image"></image> -->

				<view class='empty-state' v-if="filteredOrderList.length === 0 && !loading">
					<image src='/static/images/noOrdernow.png' class="empty-image"></image>
					<view class="empty-text">暂无订单信息~</view>
				</view>

				<!-- 加载更多状态 -->
				<view class="load-more" v-if="filteredOrderList.length > 0">
					<view class="load-more-loading" v-if="loading">
						<view class="load-more-spinner"></view>
						<text class="load-more-text">{{ loadTitle }}</text>
					</view>
					<view class="load-more-end" v-else-if="loadend">
						<text class="load-more-text">{{ loadTitle }}</text>
					</view>
					<view class="load-more-normal" v-else>
						<text class="load-more-text">{{ loadTitle }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import navBar from '@/components/navBar';
	// 导入订单相关接口
	import {
		getOrderList,
		orderCancel,
		orderDel,
		orderRefundList
	} from '@/api/order.js';
	import { Debounce } from '@/utils/validate.js';
	
	export default {
		components: {
			navBar
		},
		data() {
			return {
				navTitle: '订单列表',
				theme: 'default',
				navH: 0,
				searchKeyword: '',
				orderStatus: 'all',
				countdownTimer: null,
				// 接口调用相关参数
				loading: false,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 20,
				isShow: false,
				// 真实订单数据列表
				orderList: []
			};
		},
		computed: {
			filteredOrderList() {
				if (this.orderStatus === 'all') {
					return this.orderList;
				}
				return this.orderList.filter(order => order.pageStatus === this.orderStatus);
			},
			searchTop() {
				return 0; // 搜索框固定在顶部
			},
			tabTop() {
				return 75; // 搜索框高度88rpx
			},
			listTop() {
				return 168; // 搜索框88rpx + 状态tab 80rpx
			}
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		},
		onLoad: function(options) {
			// #ifdef MP
			const app = getApp();
			this.navH = app.globalData.navH || 44;
			// #endif
			// #ifdef H5
			this.navH = 44;
			// #endif
			// #ifdef APP-PLUS
			this.navH = 0;
			// #endif
			
			// 解析传入的订单状态参数
			if (options.status) {
				this.orderStatus = this.convertStatusFromParam(options.status);
			}
			
			this.startCountdown();
			// 调用接口获取订单列表，但暂时不使用返回的数据
			this.callOrderListApi();
		},
		onShow() {
			this.startCountdown();
			// 每次显示页面时调用接口
			this.callOrderListApi();
		},
		onHide() {
			this.stopCountdown();
		},
		onUnload() {
			this.stopCountdown();
		},
		// 下拉刷新
		onPullDownRefresh() {
			console.log('下拉刷新');
			this.page = 1;
			this.loadend = false;
			this.callOrderListApi();
			// 模拟刷新完成
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		// 上拉加载更多
		onReachBottom() {
			console.log('上拉加载更多');
			if (!this.loadend) {
				this.page = this.page + 1;
				this.callOrderListApi();
			}
		},
		methods: {
			formatCountdown(seconds) {
				if (seconds <= 0) return '';
				const h = Math.floor(seconds / 3600);
				const m = Math.floor((seconds % 3600) / 60);
				const s = seconds % 60;
				return `${this.padZero(h)}:${this.padZero(m)}:${this.padZero(s)}`;
			},
			padZero(num) {
				return num < 10 ? '0' + num : num;
			},
			startCountdown() {
				this.stopCountdown();
				this.countdownTimer = setInterval(() => {
					this.orderList.forEach(order => {
						if (order.status === 0 && !order.paid && order.countdown > 0) {
							order.countdown--;
						}
					});
				}, 1000);
			},
			stopCountdown() {
				if (this.countdownTimer) {
					clearInterval(this.countdownTimer);
					this.countdownTimer = null;
				}
			},
			handleSearch() {
				console.log('搜索订单:', this.searchKeyword);
				// 搜索时重置页码并调用接口
				this.resetPageData();
				this.callOrderListApi();
			},
			statusClick(status) {
				this.orderStatus = status;
				// 状态切换时调用接口
				this.resetPageData();
				this.callOrderListApi();
			},
			goOrderDetails(orderNo) {
				console.log('查看订单详情:', orderNo);
				// 找到对应的订单数据
				const orderItem = this.orderList.find(item => item.orderNo === orderNo);

				if (orderItem) {
					// 如果是售后订单，跳转到售后详情页面
					if (orderItem.status === 7 || orderItem.pageStatus === 'aftersale') {
						this.viewAftersaleDetails(orderItem);
						return;
					}

					// 其他状态跳转到普通订单详情页面
					let status = 'unpaid'; // 默认状态

					// 将API状态转换为详情页状态
					switch(orderItem.status) {
						case 0:
							status = 'unpaid';
							break;
						case 9:
							status = 'cancelled';
							break;
						case 1:
						case 2:
							status = 'unshipped';
							break;
						case 3:
							status = 'pickup';
							break;
						case 6:
							status = 'completed';
							break;
						case 5:
							status = 'evaluate';
							break;
						default:
							status = 'unpaid';
							break;
					}

					// 跳转到新的订单详情页面
					uni.navigateTo({
						url: `/pages/goods/order_details/detail?orderNo=${orderNo}&status=${status}`
					});
				}
			},
			goPay: Debounce(function(item) {
				console.log('去支付:', item.orderNo);

				// 检查倒计时是否结束
				if (item.countdown <= 0) {
					uni.showToast({
						title: '支付时间已过期',
						icon: 'none'
					});
					return;
				}

				// 跳转到支付页面，传递订单号和支付金额
				uni.navigateTo({
					url: `/pages/goods/order_payment/index?orderNo=${item.orderNo}&payPrice=${item.payPrice}`
				});
			}, 300),
			deleteOrder(orderNo, index) {
				uni.showModal({
					content: '确定删除该订单？',
					success: (res) => {
						if (res.confirm) {
							console.log('删除订单:', orderNo);
							// 调用删除订单接口
							this.callDeleteOrderApi(orderNo);
						}
					}
				});
			},
			orderAgain(orderNo) {
				uni.showModal({
					content: '确认要再来一单吗？',
					success: (res) => {
						if (res.confirm) {
							console.log('再来一单:', orderNo);
						}
					}
				});
			},
			showPickupCode(item) {
				console.log('查看提货码:', item.orderNo);
			},
			viewAftersaleDetails(item) {
				console.log('查看售后详情:', item.orderNo);
				// 构建跳转参数
				let url = `/pages/goods/aftersale_details/index?`;
				const params = [];

				// 添加订单号参数
				if (item.orderNo) {
					params.push(`orderNo=${item.orderNo}`);
				}

				// 添加退款订单号参数（如果有）
				if (item.refundOrderNo) {
					params.push(`refundOrderNo=${item.refundOrderNo}`);
				}

				// 添加售后类型参数
				if (item.statusText) {
					params.push(`aftersaleType=${encodeURIComponent(item.statusText)}`);
				}

				// 添加是否为退款订单标识
				params.push(`isRefund=${!!item.refundOrderNo}`);

				url += params.join('&');

				uni.navigateTo({
					url: url
				});
			},
			contactService(item) {
				console.log('联系客服:', item.orderNo);
			},
			getNavH(h) {
				// #ifndef MP
				this.navH = h;
				// #endif
			},
			// 调用订单列表接口（暂时不使用返回数据）
			callOrderListApi() {
				console.log('调用订单列表接口');
				if (this.loading) return;
				this.loading = true;
				this.loadTitle = "加载中...";

				// 如果是售后状态，使用退款订单列表接口
				if (this.orderStatus === 'aftersale') {
					this.callRefundOrderListApi();
					return;
				}

				const apiStatus = this.convertStatusToApi(this.orderStatus);
				getOrderList({
					status: apiStatus,
					page: this.page,
					limit: this.limit,
					keyword: this.searchKeyword // 添加搜索关键词
				}).then(res => {
					console.log('订单列表接口返回数据：', res);

					if (res.code === 200 && res.data) {
						const apiData = res.data;

						// 处理返回的数据
						if (this.page === 1) {
							// 首页数据，直接替换
							this.orderList = this.transformOrderData(apiData.list || []);
						} else {
							// 分页数据，追加到现有数据
							const newData = this.transformOrderData(apiData.list || []);
							this.orderList = [...this.orderList, ...newData];
						}

						// 判断是否还有更多数据
						this.loadend = this.page >= apiData.totalPage;
						this.loadTitle = this.loadend ? "我也是有底线的" : "加载更多";
					} else {
						uni.showToast({
							title: res.message || '获取订单列表失败',
							icon: 'none'
						});
					}

					this.loading = false;
					this.isShow = true;
					uni.stopPullDownRefresh();
				}).catch(err => {
					console.log('订单列表接口调用失败：', err);
					this.loading = false;
					this.loadTitle = "加载失败，点击重试";
					uni.stopPullDownRefresh();
					uni.showToast({
						title: '网络异常，请重试',
						icon: 'none'
					});
				});
			},

			// 调用退款订单列表接口
			callRefundOrderListApi() {
				console.log('调用退款订单列表接口');
				orderRefundList({
					page: this.page,
					limit: this.limit
				}).then(res => {
					console.log('退款订单列表接口返回数据：', res);

					if (res.code === 200 && res.data) {
						const apiData = res.data;

						// 处理返回的数据
						if (this.page === 1) {
							// 首页数据，直接替换
							this.orderList = this.transformRefundOrderData(apiData.list || []);
						} else {
							// 分页数据，追加到现有数据
							const newData = this.transformRefundOrderData(apiData.list || []);
							this.orderList = [...this.orderList, ...newData];
						}

						// 判断是否还有更多数据
						this.loadend = this.page >= apiData.totalPage;
						this.loadTitle = this.loadend ? "我也是有底线的" : "加载更多";
					} else {
						uni.showToast({
							title: res.message || '获取退款订单列表失败',
							icon: 'none'
						});
					}

					this.loading = false;
					this.isShow = true;
					uni.stopPullDownRefresh();
				}).catch(err => {
					console.log('退款订单列表接口调用失败：', err);
					this.loading = false;
					this.loadTitle = "加载失败，点击重试";
					uni.stopPullDownRefresh();
					uni.showToast({
						title: '网络异常，请重试',
						icon: 'none'
					});
				});
			},

			// 重置页面数据
			resetPageData() {
				this.page = 1;
				this.loadend = false;
				this.loading = false;
				this.loadTitle = "加载更多";
			},
			
			// 调用取消订单接口（暂时不使用返回数据）
			callCancelOrderApi(orderNo) {
				console.log('调用取消订单接口：', orderNo);
				orderCancel(orderNo).then(res => {
					console.log('取消订单接口返回数据：', res);
					// 暂时不处理返回的数据
				}).catch(err => {
					console.log('取消订单接口调用失败：', err);
				});
			},
			
			// 调用删除订单接口
			callDeleteOrderApi(orderNo) {
				console.log('调用删除订单接口：', orderNo);
				orderDel(orderNo).then(res => {
					console.log('删除订单接口返回数据：', res);

					// 显示删除结果消息
					if (res.code === 200) {
						uni.showToast({
							title: res.message || '删除成功',
							icon: 'success',
							duration: 2000
						});

						// 删除成功后刷新列表
						this.refreshOrderList();
					} else {
						uni.showToast({
							title: res.message || '删除失败',
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(err => {
					console.log('删除订单接口调用失败：', err);
					uni.showToast({
						title: err || '删除失败',
						icon: 'none',
						duration: 2000
					});
				});
			},

			// 刷新订单列表
			refreshOrderList() {
				this.page = 1;
				this.orderList = [];
				this.loadend = false;
				this.callOrderListApi();
			},
			
			// 状态转换：将页面状态转换为接口状态参数
			convertStatusToApi(status) {
				const statusMap = {
					'all': -1,
					'unpaid': 0,
					'unshipped': 1,
					'pickup': 3,
					'evaluate': 5,
					'aftersale': 7,
					'cancelled': 9
				};
				return statusMap[status] !== undefined ? statusMap[status] : -1;
			},
			
			// 状态转换：将接口参数转换为页面状态
			convertStatusFromParam(paramStatus) {
				const statusMap = {
					'-1': 'all',
					'0': 'unpaid',
					'1': 'unshipped',
					'2': 'unshipped', // 部分发货也归为待发货
					'3': 'pickup',
					'5': 'evaluate',
					'6': 'completed',
					'7': 'aftersale',
					'9': 'cancelled'
				};
				return statusMap[paramStatus] || 'all';
			},
			
			// 转换API数据为页面需要的格式
			transformOrderData(apiList) {
				return apiList.map(item => {
					// 优先检查退款状态，如果 refundStatus 为 3（已退款），则显示已退款状态
					let statusInfo;

					if (item.refundStatus === 3) {
						statusInfo = {
							pageStatus: 'refunded',
							statusText: '已退款',
							statusClass: 'refunded'
						};
					} else {
						// 获取基础状态信息
						statusInfo = this.getStatusInfo(item.status);

						// 如果是状态5（待评价），需要根据商品的isReply字段来判断实际状态
						if (item.status === 5) {
							const evaluateStatusInfo = this.getEvaluateStatusInfo(item.orderInfoList || []);
							if (evaluateStatusInfo) {
								statusInfo = evaluateStatusInfo;
							}
							// 如果返回null，表示部分评价部分未评价，保持原状态但归类到待评价
							// 不需要特殊处理，保持原来的状态信息即可
						}
					}

					// 处理商品列表
					const products = (item.orderInfoList || []).map(product => ({
						name: product.productName,
						image: product.image,
						spec: product.sku,
						quantity: product.payNum,
						price: product.payPrice,
						isReply: product.isReply // 保存评价状态
					}));

					// 计算配送方式（这里可能需要根据实际业务逻辑调整）
					const deliveryInfo = this.getDeliveryInfo(item.shippingType);

					// 格式化订单时间
					const orderTime = this.formatOrderTime(item.createTime);

					// 计算支付倒计时（仅待支付订单需要）
					let countdown = 0;
					if (item.status === 0 && !item.paid) {
						// 计算支付倒计时，假设30分钟内支付
						const createTime = new Date(item.createTime).getTime();
						const now = new Date().getTime();
						const diffMinutes = Math.floor((now - createTime) / (1000 * 60));
						countdown = Math.max(0, (30 * 60) - (diffMinutes * 60)); // 剩余秒数
					}

					return {
						id: item.id,
						orderNo: item.orderNo,
						status: item.status, // API原始状态
						pageStatus: statusInfo.pageStatus, // 页面使用的状态
						statusText: statusInfo.statusText,
						statusClass: statusInfo.statusClass,
						deliveryType: deliveryInfo.type,
						deliveryName: deliveryInfo.name,
						merchantName: item.storesName,
						orderTime: orderTime,
						totalQuantity: item.totalNum,
						totalPrice: item.totalPrice,
						payPrice: item.payPrice,
						paid: item.paid,
						products: products,
						countdown: countdown,
						refundStatus: item.refundStatus // 保存原始退款状态
					};
				});
			},

			// 转换退款订单API数据为页面需要的格式
			transformRefundOrderData(apiList) {
				return apiList.map(item => {
					// 处理退款商品列表
					const products = (item.responseList || []).map(product => ({
						name: product.productName,
						image: product.image,
						spec: product.sku,
						quantity: product.applyRefundNum,
						price: product.refundPrice
					}));

					// 获取退款状态信息
					const statusInfo = this.getRefundStatusInfo(item.refundStatus);

					// 处理配送方式
					const deliveryInfo = this.getDeliveryInfo(item.shippingType);

					// 格式化订单时间
					const orderTime = this.formatOrderTime(item.createTime);

					// 计算总数量
					const totalQuantity = (item.responseList || []).reduce((sum, product) => {
						return sum + (product.applyRefundNum || 0);
					}, 0);

					return {
						id: item.refundOrderNo, // 使用退款订单号作为ID
						orderNo: item.orderNo, // 原订单号
						refundOrderNo: item.refundOrderNo, // 退款订单号
						status: 7, // 固定为售后状态
						pageStatus: 'aftersale',
						statusText: statusInfo.statusText,
						statusClass: 'aftersale',
						deliveryType: deliveryInfo.type,
						deliveryName: deliveryInfo.name,
						merchantName: item.merName,
						orderTime: orderTime,
						totalQuantity: totalQuantity,
						totalPrice: item.refundPrice,
						payPrice: item.refundPrice,
						paid: true, // 退款订单默认已支付
						products: products,
						countdown: 0, // 退款订单无倒计时
						refundStatus: item.refundStatus // 保存原始退款状态
					};
				});
			},

			// 获取退款状态信息
			getRefundStatusInfo(refundStatus) {
				const statusMap = {
					0: { statusText: '待审核' },
					1: { statusText: '审核未通过' },
					2: { statusText: '退货中' },
					3: { statusText: '已退款' }
				};

				return statusMap[refundStatus] || { statusText: '售后中' };
			},

			// 获取订单状态信息
			getStatusInfo(apiStatus) {
				const statusMap = {
					0: { pageStatus: 'unpaid', statusText: '待付款', statusClass: 'unpaid' },
					1: { pageStatus: 'unshipped', statusText: '待发货', statusClass: 'unshipped' },
					2: { pageStatus: 'unshipped', statusText: '部分发货', statusClass: 'unshipped' },
					3: { pageStatus: 'pickup', statusText: '待核销', statusClass: 'pickup' },
					4: { pageStatus: 'pickup', statusText: '待收货', statusClass: 'pickup' },
					5: { pageStatus: 'evaluate', statusText: '待评价', statusClass: 'evaluate' },
					6: { pageStatus: 'completed', statusText: '已完成', statusClass: 'completed' },
					7: { pageStatus: 'aftersale', statusText: '售后中', statusClass: 'aftersale' },
					9: { pageStatus: 'cancelled', statusText: '已取消', statusClass: 'cancelled' }
				};

				return statusMap[apiStatus] || {
					pageStatus: 'unpaid',
					statusText: '未知状态',
					statusClass: 'unpaid'
				};
			},

			// 根据商品的isReply字段获取评价状态信息
			getEvaluateStatusInfo(orderInfoList) {
				if (!orderInfoList || orderInfoList.length === 0) {
					return null;
				}

				// 统计已评价和未评价的商品数量
				const repliedCount = orderInfoList.filter(product => product.isReply === true).length;
				const notRepliedCount = orderInfoList.filter(product => product.isReply === false).length;
				const totalCount = orderInfoList.length;

				// 如果所有商品都已评价，显示"已评价"
				if (repliedCount === totalCount) {
					return {
						pageStatus: 'completed',
						statusText: '已评价',
						statusClass: 'completed'
					};
				}
				// 如果所有商品都未评价，显示"待评价"
				else if (notRepliedCount === totalCount) {
					return {
						pageStatus: 'evaluate',
						statusText: '待评价',
						statusClass: 'evaluate'
					};
				}
				// 如果部分评价部分未评价，返回特殊状态：不显示状态文本，但归类到待评价
				else {
					return {
						pageStatus: 'evaluate', // 归类到待评价标签页
						statusText: '', // 不显示状态文本
						statusClass: 'evaluate'
					};
				}
			},
			
			// 获取配送方式信息（根据实际业务逻辑调整）
			getDeliveryInfo(type) {
				const deliveryMap = {
					1: { type: 'express', name: '快递' },
					2: { type: 'pickup', name: '自提' },
					3: { type: 'delivery', name: '配送' }
				};
				
				return deliveryMap[type] || { type: 'express', name: '快递' };
			},
			
			// 格式化订单时间
			formatOrderTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				
				return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
			}
		}
	}
</script>

<style scoped lang="scss">
	.order-list-page {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}

	.search-wrapper {
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		border-radius: 20rpx;
		width: 90%;
		margin: 0 auto;
		height: 75rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		z-index: 99;
		box-sizing: border-box;
		border: 4rpx solid #222222;
		background: #F5F5F5;

		.search-input {
			flex: 1;
			display: flex;
			align-items: center;
			background-color: #f7f7f7;
			border-radius: 30rpx;
			height: 60rpx;

			.iconfont {
				color: #999;
				margin-right: 10rpx;
			}

			input {
				flex: 1;
				font-size: 26rpx;
			}
		}

		.search-btn {
			margin-left: 20rpx;
			font-size: 25rpx;
			background-color: #BDFD5B;
			color: #222222;
			padding: 10rpx 30rpx;
			border-radius: 10rpx;
		}
	}
	
	.nav-wrapper {
		position: fixed;
		left: 0;
		width: 100%;
		height: 90rpx;
		z-index: 99;
		box-sizing: border-box;
		background: #F5F5F5;
	}

	.scroll-view_x {
		height: 80rpx;
		padding: 0 30rpx;
		
		.tab-item {
			font-size: 28rpx;
			color: #666;
			padding: 25rpx 30rpx;
			display: inline-block;
			position: relative;
			white-space: nowrap;
			
			&:last-child {
				margin-right: 30rpx;
			}
			
			&.active {
				color: #333;
				font-weight: 600;
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 55rpx;
					height: 15rpx;
					background-color: #BDFD5B;
					border-radius: 2rpx;
				}
			}
		}
	}

	.order-list {
		padding: 20rpx 30rpx 30rpx;
	}

	.order-card {
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
	}

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;

		.header-left {
			display: flex;
			align-items: center;
			
			.delivery-tag {
				font-size: 22rpx;
				color: #fff;
				padding: 6rpx 12rpx;
				border-radius: 6rpx;
				margin-right: 12rpx;
				
				&.delivery {
					background-color: #FF7F00;
				}
				
				&.pickup {
					background-color: #FF3127;
				}
				
				&.express {
					background-color: #409EFF;
				}
			}
			
			.merchant-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 600;
				margin-right: 8rpx;
			}
			
			.iconfont {
				font-size: 24rpx;
				color: #999;
			}
		}

		.order-status {
			font-size: 26rpx;
			
			&.cancelled {
				color: #999;
			}
			
			&.unpaid {
				color: #FF3127;
			}
			
			&.pickup {
				color: #FF3127;
			}
			
			&.unshipped {
				color: #FF3127;
			}
			
			&.completed {
				color: #999;
			}
			
			&.evaluate {
				color: #FF3127;
			}
			
			&.aftersale {
				color: #FF3127;
			}

			&.refunded {
				color: #FF3127;
			}
		}
	}

	.order-content {
		padding: 24rpx 30rpx 0;
	}

	.single-product {
		display: flex;
		align-items: flex-start;
		
		.product-image {
			width: 160rpx;
			height: 160rpx;
			border-radius: 12rpx;
			overflow: hidden;
			margin-right: 24rpx;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.product-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			height: 160rpx;
			
			.product-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
				line-height: 1.6;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
			}
			
			.product-spec,
			.product-quantity {
				font-size: 24rpx;
				color: #999;
				margin-top: 8rpx;
			}
		}
		
		.product-price {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			align-self: flex-end;
		}
	}

	.multi-products {
		.products-scroll {
			white-space: nowrap;
			
			.product-thumb {
				display: inline-block;
				width: 160rpx;
				height: 160rpx;
				border-radius: 12rpx;
				margin-right: 16rpx;
			}
			
			.more-indicator {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				width: 160rpx;
				height: 160rpx;
				background-color: #f8f8f8;
				border-radius: 12rpx;
				font-size: 24rpx;
				color: #999;
			}
		}
	}

	.order-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		margin-top: 24rpx;

		.order-time {
			font-size: 24rpx;
			color: #999;
		}

		.order-total {
			font-size: 26rpx;
			color: #666;
			
			.total-price {
				color: #E93323;
				font-weight: 600;
				font-size: 28rpx;
			}
		}
	}

	.order-actions {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 0 30rpx 30rpx;
		gap: 20rpx;

		.action-btn {
			padding: 16rpx 32rpx;
			border-radius: 20rpx;
			font-size: 26rpx;
			text-align: center;
			
			&.secondary {
				border: 1rpx solid #ddd;
				color: #666;
				background-color: #fff;
			}
			
			&.primary {
				background-color: #BDFD5B;
				color: #222222;
			}
			
			&.countdown-btn {
				background-color: #BDFD5B;
				padding: 16rpx 24rpx;
			}
		}
	}

	.empty-state {
		text-align: center;
		padding: 120rpx 0;

		.empty-image {
			width: 414rpx;
			height: 305rpx;
			margin: 0 auto;
		}

		.empty-text {
			font-size: 26rpx;
			color: #999;
			margin-top: 20rpx;
		}
	}

	.skeleton-container {
		.skeleton-card {
			background-color: #fff;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			overflow: hidden;

			.skeleton-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 30rpx;

				.skeleton-left {
					display: flex;
					align-items: center;

					.skeleton-tag {
						width: 60rpx;
						height: 30rpx;
						background-color: #f0f0f0;
						border-radius: 6rpx;
						margin-right: 12rpx;
					}

					.skeleton-merchant {
						width: 120rpx;
						height: 28rpx;
						background-color: #f0f0f0;
						border-radius: 4rpx;
					}
				}

				.skeleton-status {
					width: 80rpx;
					height: 26rpx;
					background-color: #f0f0f0;
					border-radius: 4rpx;
				}
			}

			.skeleton-content {
				display: flex;
				align-items: flex-start;
				padding: 24rpx 30rpx 0;

				.skeleton-image {
					width: 160rpx;
					height: 160rpx;
					background-color: #f0f0f0;
					border-radius: 12rpx;
					margin-right: 24rpx;
				}

				.skeleton-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					height: 160rpx;

					.skeleton-name {
						width: 100%;
						height: 28rpx;
						background-color: #f0f0f0;
						border-radius: 4rpx;
						margin-bottom: 12rpx;
					}

					.skeleton-spec {
						width: 80%;
						height: 24rpx;
						background-color: #f0f0f0;
						border-radius: 4rpx;
						margin-bottom: 8rpx;
					}

					.skeleton-quantity {
						width: 60%;
						height: 24rpx;
						background-color: #f0f0f0;
						border-radius: 4rpx;
					}
				}

				.skeleton-price {
					width: 80rpx;
					height: 32rpx;
					background-color: #f0f0f0;
					border-radius: 4rpx;
					align-self: flex-end;
				}
			}

			.skeleton-footer {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 30rpx;
				margin-top: 24rpx;

				.skeleton-time {
					width: 150rpx;
					height: 24rpx;
					background-color: #f0f0f0;
					border-radius: 4rpx;
				}

				.skeleton-total {
					width: 120rpx;
					height: 26rpx;
					background-color: #f0f0f0;
					border-radius: 4rpx;
				}
			}

			.skeleton-actions {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				padding: 0 30rpx 30rpx;
				gap: 20rpx;

				.skeleton-btn {
					width: 120rpx;
					height: 60rpx;
					background-color: #f0f0f0;
					border-radius: 20rpx;
				}
			}
		}
	}

	/* 骨架屏动画效果 */
	.skeleton-container .skeleton-card {
		.skeleton-tag,
		.skeleton-merchant,
		.skeleton-status,
		.skeleton-image,
		.skeleton-name,
		.skeleton-spec,
		.skeleton-quantity,
		.skeleton-price,
		.skeleton-time,
		.skeleton-total,
		.skeleton-btn {
			position: relative;
			overflow: hidden;

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
				animation: skeleton-loading 1.5s infinite;
			}
		}
	}

	@keyframes skeleton-loading {
		0% {
			left: -100%;
		}
		100% {
			left: 100%;
		}
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.load-more {
		padding: 30rpx 0;
		text-align: center;

		.load-more-loading {
			display: flex;
			align-items: center;
			justify-content: center;

			.load-more-spinner {
				width: 30rpx;
				height: 30rpx;
				border: 2rpx solid #f3f3f3;
				border-top: 2rpx solid #BDFD5B;
				border-radius: 50%;
				animation: spin 1s linear infinite;
				margin-right: 15rpx;
			}

			.load-more-text {
				font-size: 24rpx;
				color: #999;
			}
		}

		.load-more-end,
		.load-more-normal {
			.load-more-text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
</style>
