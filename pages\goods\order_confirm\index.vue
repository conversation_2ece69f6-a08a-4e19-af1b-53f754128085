<template>
	<view class="order-comfrim-content">
		<!-- 顶部导航栏 -->
		<!-- 		<view class="order-comfrim-top-nav-box">
			<view :style=" {height:statusBarHeight+'px'}"></view>
			<view class="order-comfrim-top-nav-title" :style="{height:titleBarHeight+'px','line-height':titleBarHeight+'px'}">
				<uni-icons color="#222222" type="back" size="30" @click="goBack"></uni-icons>
				<view class="order-comfrim-top-nav-title-text">订单结算</view>
			</view>
		</view> -->
		<!-- 占位 -->
		<!-- <view :style="{height:headerHeight+'px'}"></view> -->
		<!-- 配送方式结构 -->
		<view class="order-comfrim-main-box">
			<view class="order-comfrim-main">
				<!-- 一种配送方式 -->
				<view class="order-one-mode-box"
					v-if="loadPreOrderObj && loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList.length == 1">
					<!-- 自提结构 -->
					<view class="order-distribution-mode-box"
						v-if="loadPreOrderObj && loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList[0] == 2">
						<view class="took-mode-box">
							<view class="took-shop-name-box">
								<view class="took-shop-identification">自提点</view>
								<view class="took-shop-name">{{loadPreOrderObj.storesInfo.name}}</view>
							</view>
							<view class="took-shop-address-box">
								<view class="took-shop-address-text">
									{{loadPreOrderObj.storesInfo.province}}{{loadPreOrderObj.storesInfo.city}}{{loadPreOrderObj.storesInfo.district}}{{loadPreOrderObj.storesInfo.addressDetail}}
								</view>
								<uni-icons color="#999999" type="right" size="15"></uni-icons>
							</view>
						</view>
						<view class="mode-line"></view>
						<view class="took-person-info-box" @click="updateInfoFn">
							<view class="took-person-info-text" v-if="createOrderObj.pickUpName == ''">请填写自提人信息</view>
							<view class="took-person-info-text" v-else>提货人：{{ createOrderObj.pickUpName }}
								{{createOrderObj.pickUpPhone}}
							</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
					</view>
					<!-- 配送结构 -->
					<view class="order-delivery-mode-box"
						v-else-if="loadPreOrderObj && loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList[0] == 3">
						<view v-if="addressDetailObj == null " class="delivery-not-address-box" @click="addAddressFn">
							<view class="delivery-not-address-text">添加收货地址</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
						<view v-else class="delivery-have-address-box">
							<view class="delivery-have-address-left-box">
								<view class="address-left-text">
									{{addressDetailObj.province}}{{addressDetailObj.city}}{{addressDetailObj.district}}{{addressDetailObj.street}}{{addressDetailObj.detail}}
								</view>
								<view class="address-left-info">{{addressDetailObj.realName}} {{addressDetailObj.phone}}</view>
							</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
					</view>
					<!-- 快递结构 -->
					<view class="order-delivery-mode-box"
						v-else-if="loadPreOrderObj && loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList[0] == 1">
						<view v-if="addressDetailObj == null" class="delivery-not-address-box" @click="addAddressFn">
							<view class="delivery-not-address-text">添加收货地址</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
						<view v-else class="delivery-have-address-box" @click="addAddressFn">
							<view class="delivery-have-address-left-box">
								<view class="address-left-text">
									<view class="address-left-tag">快递配送</view>
									<view style="display: inline;">
										{{addressDetailObj.province}}{{addressDetailObj.city}}{{addressDetailObj.district}}{{addressDetailObj.street}}{{addressDetailObj.detail}}
									</view>
								</view>
								<view class="address-left-info">{{addressDetailObj.realName}} {{addressDetailObj.phone}}</view>
							</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
					</view>
				</view>
				<!-- 两种收货方式-配送、自提或配送、快递或自提、快递 -->
				<view class="order-two-mode-box"
					v-else-if="loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList.length == 2">
					<view class="order-two-mode-header-box">
						<view class="order-two-mode-header-item-box" v-for="item in loadPreOrderObj.shippingTypeList" :key="item"
							@click="chooseTypeFn(item)" :style="{backgroundColor: typeItem == item ? '#bdfd5b' : '#f5f5f5'}">
							{{item == 3 ? '门店配送' : item == 2 ? '门店自提' : '快递到家'}}
						</view>
					</view>
					<view class="order-two-mode-main-box">
						<view v-if="typeItem !== 2" class="order-two-mode-delivery-box">
							<view v-if="addressDetailObj == null" class="order-two-mode-not-address" @click="addAddressFn">
								<view class="two-mode-not-address-text">添加收货地址</view>
								<uni-icons color="#999999" type="right" size="15"></uni-icons>
							</view>
							<view class="order-two-mode-have-address-box" v-else @click="addAddressFn">
								<view class="order-two-mode-have-address-left-box">
									<view class="two-mode-have-address-text">
										{{addressDetailObj.province}}{{addressDetailObj.city}}{{addressDetailObj.district}}{{addressDetailObj.street}}{{addressDetailObj.detail}}
									</view>
									<view class="two-mode-have-person-info">{{addressDetailObj.realName}} {{addressDetailObj.phone}}
									</view>
								</view>
								<uni-icons color="#999999" type="right" size="15"></uni-icons>
							</view>
						</view>
						<view v-else class="order-two-mode-distribution-box" @click="updateInfoFn">
							<view class="two-mode-distribution-left-box">
								<view class="two-mode-distribution-merchant">{{loadPreOrderObj.storesInfo.name}}</view>
								<view class="two-mode-distribution-address">
									{{loadPreOrderObj.storesInfo.province}}{{loadPreOrderObj.storesInfo.city}}{{loadPreOrderObj.storesInfo.district}}{{loadPreOrderObj.storesInfo.addressDetail}}
								</view>
								<view v-if="createOrderObj.pickUpName == ''" class="two-mode-person-info-text">请填写自提人信息</view>
								<view v-else class="two-mode-distribution-info">{{createOrderObj.pickUpName}}
									{{createOrderObj.pickUpPhone}}
								</view>
							</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
					</view>
				</view>
				<!-- 三种收货方式 -->
				<view class="order-three-mode-box"
					v-else-if="loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 3">
					<view class="order-three-mode-header-box">
						<view class="order-three-mode-header-item-box" v-for="item in loadPreOrderObj.shippingTypeList" :key="item"
							@click="chooseTypeFn(item)" :style="{backgroundColor: typeItem == item ? '#bdfd5b' : '#f5f5f5'}">
							{{item == 2 ? '门店自提' : item == 3 ? '门店配送' : '快递到家'}}
						</view>
					</view>
					<view class="order-three-mode-main-box">
						<view v-if="typeItem !== 2" class="order-three-mode-delivery-box">
							<view v-if="addressDetailObj == null" class="order-three-mode-not-address" @click="addAddressFn">
								<view class="three-mode-not-address-text">添加收货地址</view>
								<uni-icons color="#999999" type="right" size="15"></uni-icons>
							</view>
							<view class="order-three-mode-have-address-box" v-else @click="addAddressFn">
								<view class="order-three-mode-have-address-left-box">
									<view class="three-mode-have-address-text">
										{{addressDetailObj.province}}{{addressDetailObj.city}}{{addressDetailObj.district}}{{addressDetailObj.street}}{{addressDetailObj.detail}}
									</view>
									<view class="three-mode-have-person-info">{{addressDetailObj.realName}} {{addressDetailObj.phone}}
									</view>
								</view>
								<uni-icons color="#999999" type="right" size="15"></uni-icons>
							</view>
						</view>
						<view v-else class="order-three-mode-distribution-box" @click="updateInfoFn">
							<view class="three-mode-distribution-left-box">
								<view class="three-mode-distribution-merchant">{{loadPreOrderObj.storesInfo.name}}</view>
								<view class="three-mode-distribution-address">
									{{loadPreOrderObj.storesInfo.province}}{{loadPreOrderObj.storesInfo.city}}{{loadPreOrderObj.storesInfo.district}}{{loadPreOrderObj.storesInfo.addressDetail}}
								</view>
								<view v-if="createOrderObj.pickUpName == ''" class="three-mode-person-info-text">请填写自提人信息</view>
								<view v-else class="three-mode-distribution-info">{{createOrderObj.pickUpName}}
									{{createOrderObj.pickUpPhone}}
								</view>
							</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 商品列表信息 -->
		<view class="order-goods-list-box">
			<view class="order-goods-list-main-box">
				<view class="order-goods-list-header-box">
					<view class="header-left-box">
						<view class="header-merchant-name-box">{{loadPreOrderObj.merchantOrderVoList[0].merName}}</view>
						<view class="header-type-tag"
							v-if="loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 2">自提</view>
						<view class="header-type-tag" style="background-color: #ff5400;"
							v-if="loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 3">配送
						</view>
						<view class="header-type-tag" style="background-color: #4da6fa;"
							v-if="loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 1">快递</view>
					</view>
					<!-- 自提时间 -->
					<uni-icons
						v-if="(loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 2) || typeItem == 2"
						@click="chooseTimeFn('2')" color="#999999" type="right" size="14"></uni-icons>
					<!-- 配送时间 -->
					<uni-icons
						v-if="(loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 3) || typeItem == 3"
						@click="chooseTimeFn('1')" color="#999999" type="right" size="14"></uni-icons>
				</view>
				<view class="order-goods-list-time-box"
					v-if="((loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 2) || typeItem == 2) || ((loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 3) || typeItem == 3)">
					<text
						v-if="(loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 2) || typeItem == 2">自提时间：</text>
					<text
						v-if="(loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 3) || typeItem == 3">配送时间：</text>
					<text
						v-if="(loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 2) || typeItem == 2"
						@click="chooseTimeFn('2')">
						<text v-if="createOrderObj.shippingDate"> {{createOrderObj.shippingDate}} {{createOrderObj.shippingTimeStr}}
						</text>
						<text v-else>请选择自提时间</text>
					</text>
					<text
						v-if="(loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 3) || typeItem == 3"
						@click="chooseTimeFn('1')">
						<text v-if="createOrderObj.shippingDate"> {{createOrderObj.shippingDate}} {{createOrderObj.shippingTimeStr}}
						</text>
						<text v-else>请选择配送时间</text>
					</text>
				</view>
				<view class="order-goods-list-line"></view>
				<view class="order-goods-item-main-box">
					<view class="order-goods-item-content-box"
						v-for="item in loadPreOrderObj.merchantOrderVoList[0].orderInfoList" :key="item.productId">
						<view class="order-goods-item-box">
							<!-- <view class="goods-item-img"></view> -->
							<image class="goods-item-img" :src="item.image" mode="aspectFit"></image>
							<view class="goods-item-info">
								<view class="goods-item-name">{{item.productName}}</view>
								<view class="goods-item-center-box">
									<view class="goods-item-sku">{{item.sku}}</view>
									<view class="goods-item-num">X{{item.payNum}}</view>
								</view>
								<view class="goods-item-price">￥{{item.activityPrice || item.price}}</view>
							</view>
						</view>
						<view class="order-goods-intro">{{item.intro}}</view>
					</view>
				</view>
				<view class="order-goods-list-receipts" @click="viewReceiptsFn">
					<view class="receipts-text">查看验光单信息</view>
					<uni-icons color="#999999" type="right" size="14"></uni-icons>
				</view>
				<view class="order-goods-total-info-box">
					<view class="total-info-text">已选{{loadPreOrderObj.merchantOrderVoList[0].proTotalNum}}件</view>
					<view class="total-info-text">总计：</view>
					<view class="total-info-price">¥{{loadPreOrderObj.merchantOrderVoList[0].proTotalFee}}</view>
				</view>
			</view>
		</view>
		<!-- 价格明细 -->
		<view class="order-goods-price-detail-box">
			<view class="price-detail-main-box">
				<view class="price-detail-header-box">价格明细</view>
				<view class="price-detail-info-box">
					<view class="detail-info-item-box">
						<view class="detail-info-item-title">商品金额</view>
						<view class="detail-info-item-price">￥{{loadPreOrderObj.proTotalFee}}</view>
					</view>
					<view class="detail-info-item-box"
						v-if="!(loadPreOrderObj.shippingTypeList && loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 2) || typeItem == 2">
						<view class="detail-info-item-title">
							{{(loadPreOrderObj.shippingTypeList &&loadPreOrderObj.shippingTypeList.length == 1 && loadPreOrderObj.shippingTypeList[0] == 3)? '配送费' : '运费'}}
						</view>
						<view class="detail-info-item-freight-price">￥{{loadPreOrderObj.freightFee}}</view>
					</view>
					<view class="detail-info-item-box">
						<view class="detail-info-item-title">优惠券</view>
						<view class="detail-info-item-coupon" @click="openCouponFn">
							<view class="" v-if="!loadPreOrderObj.merchantOrderVoList[0].ifCoupon">暂无优惠券</view>
							<view v-else class="">{{loadPreOrderObj.merchantOrderVoList[0].couponName || ''}}</view>
							<uni-icons color="#999999" type="right" size="15"></uni-icons>
						</view>
					</view>
					<view class="detail-info-item-box">
						<view class="detail-info-item-title">合计</view>
						<view class="detail-info-item-price">￥{{loadPreOrderObj.payFee}}</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 订单备注 -->
		<view class="order-goods-remark-box">
			<view class="order-goods-remark-main-box">
				<view class="remark-label">订单备注</view>
				<view class="remark-textarea-box">
					<textarea class="remark-textarea-main" v-model="createOrderObj.orderMerchantRequestList[0].remark"
						maxlength="100" @input="descInput" />
					<view class="remark-textarea-word-count">{{ start }}/100</view>
				</view>
			</view>
		</view>
		<!-- 占位 -->
		<view class="order-goods-bottom-placeholder"></view>
		<view class="order-goods-bottom-box">
			<view class="bottom-amount-statistics">
				<view class="actual-payment-box">
					<text>实付款：</text><text class="actual-payment-num">¥{{loadPreOrderObj.payFee}}</text>
				</view>
				<view class="discount-amount-box">
					<text>优惠券减</text><text class="discount-amount-num">¥{{loadPreOrderObj.couponFee || 0}}</text>
				</view>
			</view>
			<view class="bottom-submit-order-btn" @click="createOrderFn">提交订单</view>
		</view>
		<!-- 提货人信息弹层 -->
		<uni-popup ref="infoPopup" type="bottom" border-radius="15px 15px 0 0" :is-mask-click="false"
			background-color="#fff">
			<view class="took-info-popup-content">
				<view class="info-popup-header-box">
					<view class="info-popup-header-title">提货人信息</view>
					<image class="info-popup-header-icon" src="/static/imgs/home/<USER>" mode="aspectFit"
						@click="closeInfoPopupFn" />
				</view>
				<view class="info-popup-main-box">
					<view class="info-label-box">提货人姓名:</view>
					<input type="text" class="info-input-box" placeholder="请输入提货人姓名" v-model="createOrderObj.pickUpName" />
				</view>
				<view class="info-popup-line"></view>
				<view class="info-popup-main-box">
					<view class="info-label-box">提货人手机号:</view>
					<input type="text" class="info-input-box" placeholder="请输入提货人手机号" v-model="createOrderObj.pickUpPhone" />
				</view>
				<view class="info-popup-bottom-box">
					<view class="info-popup-bottom-btn" @click="confirmInfoFn">确认</view>
				</view>
			</view>
		</uni-popup>
		<!-- 自提时间/配送时间选择弹层 -->
		<uni-popup ref="timePopup" type="bottom" border-radius="15px 15px 0 0" :is-mask-click="false"
			background-color="#f5f5f5">
			<view class="time-popup-content">
				<view class="time-popup-header-box">
					<view class="time-popup-header-title">{{timeType == '2' ? '选择自提时间' : '选择配送时间'}}</view>
					<image class="time-popup-header-icon" src="/static/imgs/home/<USER>" mode="aspectFit"
						@click="closeTimePopupFn" />
				</view>
				<view class="time-popup-main-box">
					<view class="time-popup-main-list-box">
						<scroll-view :scroll-top="0" scroll-y="true" class="time-popup-main-list-scroll-box"
							style="overflow: hidden;">
							<view class="time-list-box">
								<view class="time-list-title">今天({{ weekDay }})</view>
								<radio-group @change="timeRadioChange">
									<label class="time-item-box" v-for="(item, index) in loadPreOrderObj.storesInfo.todayShippingTimeList"
										:key="item">
										<view class="time-item-text" :style="{color: index === timeListCurrent ? '#222222' : '#666666'}">
											<text v-if="index === 0">立即送达</text>{{item}}
										</view>
										<view class="time-item-radio">
											<radio :value="item" :checked="index === timeListCurrent" activeBackgroundColor="#bdfd5b"
												iconColor="#000000" />
										</view>
									</label>
								</radio-group>
							</view>
							<view class="time-list-box"
								v-if="loadPreOrderObj.storesInfo && loadPreOrderObj.storesInfo.tomorrowShippingTimeList && loadPreOrderObj.storesInfo.tomorrowShippingTimeList.length !== 0">
								<view class="time-list-title">明天({{ tomorrowDay }})</view>
								<radio-group @change="timeRadioChange2">
									<label class="time-item-box"
										v-for="(item, index) in loadPreOrderObj.storesInfo.tomorrowShippingTimeList" :key="item">
										<view class="time-item-text" :style="{color: index === timeListCurrent2 ? '#222222' : '#666666'}">
											{{item}}
										</view>
										<view class="time-item-radio">
											<radio :value="item" :checked="index === timeListCurrent2" activeBackgroundColor="#bdfd5b"
												iconColor="#000000" />
										</view>
									</label>
								</radio-group>
							</view>
						</scroll-view>
					</view>
				</view>
				<view class="time-popup-bottom-box">
					<view class="time-popup-bottom-btn" @click="confirmTimeFn">确认</view>
				</view>
			</view>
		</uni-popup>
		<!-- 优惠券弹层 -->
		<uni-popup ref="couponPopup" type="bottom" border-radius="15px 15px 0 0" :is-mask-click="false"
			background-color="#f5f5f5">
			<view class="coupon-popup-content">
				<view class="coupon-popup-header-box">
					<view class="coupon-popup-header-title">优惠券</view>
					<image class="coupon-popup-header-icon" src="/static/imgs/home/<USER>" mode="aspectFit"
						@click="closeCouponPopupFn" />
				</view>
				<view class="coupon-popup-main-box">
					<view class="coupon-popup-list-box">
						<scroll-view :scroll-top="0" scroll-y="true" class="coupon-popup-main-list-scroll-box"
							style="overflow: hidden;">
							<view class="coupon-popup-main-list-box">
								<radio-group @change="couponRadioChange">
									<label class="coupon-item-box" v-for="(item, index) in couponOrderList" :key="item">
										<view class="coupon-item-radio">
											<radio :value="item.id" :checked="index === couponOrderCurrent" activeBackgroundColor="#bdfd5b"
												iconColor="#000000" />
										</view>
										<view class="coupon-item-content">
											<view class="coupon-item-content-left-box">
												<view class="coupon-item-name">{{item.name}}</view>
												<view class="coupon-item-time">有效期至{{item.endTime}}</view>
												<view class="coupon-item-remark">
													备注:{{item.category == 1 ? '商家券' : item.category == 2 ? '商品券' : '平台券'}}</view>
											</view>
											<view class="coupon-item-content-right-box">
												<view class="coupon-item-content-right-left-top"></view>
												<view class="coupon-item-content-right-left-bottom"></view>
												<view class="coupon-item-price-box" v-if="item.couponType == 1">
													<text class="coupon-item-price-symbol">￥</text><text>{{item.money}}</text>
												</view>
												<view class="coupon-item-price-box" v-else>
													<text>{{item.discount}}</text><text class="coupon-item-price-symbol">折</text>
												</view>
												<view class="coupon-item-condition">满{{item.minPrice}}元</view>
												<view class="coupon-item-btn-box">
													<view class="coupon-item-btn">立即使用</view>
												</view>
											</view>
										</view>
									</label>
								</radio-group>
							</view>
						</scroll-view>
					</view>
				</view>
				<view class="coupon-popup-bottom-box">
					<view class="coupon-popup-bottom-btn" @click="confirmCouponFn">确认</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	/**
	 * 加载预下单
	 * 创建订单
	 * 当前订单可用优惠券
	 * 计算订单价格
	 */
	import {
		loadPreOrderApi,
		createOrderApi,
		getCouponOrderListApi,
		computedOrderPriceApi
	} from '@/api/order.js'
	import {
		getAddressDetail
	} from '@/api/user.js' // 获取地址详情
	export default {
		data() {
			return {
				menuButtonInfo: null, // 胶囊按钮信息
				statusBarHeight: 0, // 状态栏高度
				titleBarHeight: 0, // 标题栏高度
				headerHeight: 0, // 高度占位
				preOrderNo: '', // 订单编号
				loadPreOrderObj: {}, // 预下单对象  配送方式: 1快递 2自提 3配送
				addressDetailObj: null, // 地址详情
				couponOrderList: [], // 可用优惠券列表
				computedPriceObj: {
					isUseIntegral: false,
					orderMerchantRequestList: [{
						merId: '',
						userCouponId: ''
					}],
					preOrderNo: '',
					shippingType: '',
					storesId: uni.getStorageSync('storeId')

				}, // 计算订单价格接口请求参数
				createOrderObj: {
					isUseIntegral: false, // 使用使用积分
					// 订单商户对象
					orderMerchantRequestList: [{
						merId: '', // 商户id
						remark: '', // 备注
						userCouponId: '', // 优惠券编号
					}],
					preOrderNo: '', // 预下单订单号
					addressId: '', // 收货地址id
					optometryId: '', // 验光单id
					pickUpName: '', // 自提人姓名
					pickUpPhone: '', // 自提人电话
					shippingDate: '', // 配送/自提日期
					shippingTimeStr: '' // 配送/自提时间段
				}, // 创建订单接口请求参数
				couponOrderCurrent: null, // 优惠券单选配置

				typeItem: 2, // 选择的配送方式
				timeType: '', // 判断当前点击的时间是自提还是配送
				weekDay: '', // 获取今日周几
				tomorrowDay: '', // 获取明日周几
				timeListCurrent: null, // 自提/配送时间单选配置
				timeListCurrent2: null, // 明天的自提/配送时间单选配置
				start: 0, // 备注文本域字数统计
				dataInfo: ''

			};
		},
		onLoad(options) {
			// 获取状态栏高度
			// const info = uni.getSystemInfoSync() // 获取设备信息
			const info = uni.getWindowInfo() // 获取设备信息
			// console.log('info', info);
			this.statusBarHeight = info.statusBarHeight
			// 获取胶囊按钮信息(width, height, top等)
			const menuButton = uni.getMenuButtonBoundingClientRect()
			// console.log('menuButton', menuButton);
			this.menuButtonInfo = menuButton
			// 胶囊按钮相对于导航栏的上边距
			const topDistance = this.menuButtonInfo.top - this.statusBarHeight
			// 计算导航栏高度
			this.titleBarHeight = this.menuButtonInfo.height + topDistance * 2
			this.headerHeight = this.titleBarHeight + this.statusBarHeight

			console.log('页面传递的信息', options);
			this.preOrderNo = options.orderNo
			console.log('传递的订单编号', this.preOrderNo);
			this.loadPreOrder()
		},
		onShow(options) {
			console.log('onShow', uni.getStorageSync('selectedAddress').id);
			this.getAddressDetailFn(0, 'show')
		},
		methods: {
			// 加载预下单
			async loadPreOrder() {
				const res = await loadPreOrderApi(this.preOrderNo)
				console.log('加载预下单', res);
				if (res.code === 200) {
					this.$nextTick(() => {
						this.loadPreOrderObj = res.data.orderInfoVo
						console.log('this.loadPreOrderObj', this.loadPreOrderObj);
						console.log('loadPreOrderObj.shippingTypeList', this.loadPreOrderObj.shippingTypeList);
						console.log('loadPreOrderObj.shippingTypeList.length', this.loadPreOrderObj.shippingTypeList.length);
						this.typeItem = this.loadPreOrderObj.shippingType
						this.getAddressDetailFn(this.loadPreOrderObj.addressId)
					})
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 获取地址详情
			async getAddressDetailFn(id, show) {
				let addressId = id
				if (addressId === 0) {
					addressId = uni.getStorageSync('selectedAddress').id || 0
					// console.log('storage', uni.getStorageSync('selectedAddress'));
					// return
				}
				const res = await getAddressDetail(addressId)
				this.addressDetailObj = null
				if (res.code === 200) {
					console.log('地址详情', res);
					this.addressDetailObj = res.data
					console.log('this.addressDetailObj', this.addressDetailObj);
					this.computedPriceObj.orderMerchantRequestList[0].merId = this.loadPreOrderObj.merchantOrderVoList[0].merId
					this.computedPriceObj.preOrderNo = this.preOrderNo
					this.computedPriceObj.shippingType = this.loadPreOrderObj.shippingType
					this.computedPriceObj.orderMerchantRequestList[0].userCouponId = this.loadPreOrderObj.merchantOrderVoList[0]
						.userCouponId
					if (show === 'show') {
						this.computedOrderPrice()
					}
				} else {
					this.addressDetailObj = null
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击返回上一页
			goBack() {
				console.log('返回上一页');
				uni.navigateBack()
			},
			// 点击打开修改自提信息弹层
			updateInfoFn() {
				console.log('点击打开修改自提信息弹层');
				this.$refs.infoPopup.open()
			},
			// 点击关闭自提信息弹层
			closeInfoPopupFn() {
				this.createOrderObj.pickUpName == ''
				this.createOrderObj.pickUpPhone == ''
				this.$refs.infoPopup.close()
			},
			// 点击确认, 修改自提信息
			confirmInfoFn() {
				let myreg = /^[1][3,4,5,7,8][0-9]{9}$/;
				if (this.createOrderObj.pickUpName == '' || this.createOrderObj.pickUpPhone == '') {
					uni.showToast({
						icon: 'none',
						title: '请填写信息',
						duration: 2000
					});
				} else if (!myreg.test(this.createOrderObj.pickUpPhone)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 2000
					});
				} else {
					console.log('修改自提信息', this.createOrderObj);
					this.$refs.infoPopup.close()
				}
			},
			// 点击添加地址
			addAddressFn() {
				console.log('添加地址');
				uni.navigateTo({
					// url: `/pages/address/user_address/index?type=select`
					url: `/pages/address/user_address_list/index?type=select`
				})
			},
			// 点击选择配送方式
			chooseTypeFn(item) {
				console.log('点击选择配送方式', item);
				this.typeItem = item
				this.computedPriceObj.shippingType = item
				this.computedOrderPrice()
			},
			// 点击选择配送/自提时间, 打开选择时间弹层
			chooseTimeFn(type) {
				this.timeType = type
				this.$refs.timePopup.open()
				let days = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
				let today = new Date();
				let weekday = today.getDay();
				this.weekDay = days[weekday];
				this.tomorrowDay = days[weekday + 1];
				if (type == '1') {
					console.log('选择配送时间');
				} else {
					console.log('自提时间');
				}
			},
			// 点击关闭选择时间弹层
			closeTimePopupFn() {
				this.$refs.timePopup.close()
			},
			// 点击选择配送/自提时间
			timeRadioChange(evt) {
				console.log('选择了今天的时间', evt);
				this.timeListCurrent2 = null
				let now = new Date();
				let year = now.getFullYear();
				let month = now.getMonth() + 1;
				let day = now.getDate();
				console.log('year + "-" + month + "-" + day', year + "-" + month + "-" + day);
				for (let i = 0; i < this.loadPreOrderObj.storesInfo.todayShippingTimeList.length; i++) {

					this.timeListCurrent = i;
					this.createOrderObj.shippingTimeStr = evt.detail.value
					this.createOrderObj.shippingDate = year + "-" + "0" + month + "-" + day
					break;
				}
				console.log('this.createOrderObj', this.createOrderObj);
			},
			// 点击选择配送/自提时间
			timeRadioChange2(evt) {
				console.log('选择了明天的时间', evt);
				this.timeListCurrent = null
				let now = new Date();
				let year = now.getFullYear();
				let month = now.getMonth() + 1;
				let day = now.getDate() + 1;
				console.log('year + "-" + month + "-" + day', year + "-" + month + "-" + day);
				for (let i = 0; i < this.loadPreOrderObj.storesInfo.todayShippingTimeList.length; i++) {

					this.timeListCurrent2 = i;
					this.createOrderObj.shippingTimeStr = evt.detail.value
					this.createOrderObj.shippingDate = year + "-" + "0" + month + "-" + day
					break;
				}
				console.log('this.createOrderObj', this.createOrderObj);
			},

			// 点击确认选择时间
			confirmTimeFn() {
				console.log('点击了确认选择时间');
				if (this.createOrderObj.shippingTimeStr !== '') {
					this.closeTimePopupFn()
				} else {
					uni.showToast({
						icon: 'none',
						title: '请选择时间',
						duration: 2000
					});
				}
			},
			// 点击查看验光单
			viewReceiptsFn() {
				console.log('点击查看验光单');
				uni.navigateTo({
					url: '/pages/optometry/user_optometry_list/index?isCheck=true'
				});
			},
			// 获取当前可用优惠券列表
			async getCouponOrderList() {
				const res = await getCouponOrderListApi({
					merId: this.loadPreOrderObj.merchantOrderVoList[0].merId,
					preOrderNo: this.preOrderNo
				})
				if (res.code === 200) {
					console.log('优惠券列表', res);
					this.couponOrderList = res.data
				}
			},
			// 点击打开选择优惠券弹层
			openCouponFn() {
				if (this.loadPreOrderObj.merchantOrderVoList[0].ifCoupon) {
					console.log('打开选择优惠券弹层');
					this.getCouponOrderList()
					this.$refs.couponPopup.open()
				} else {
					console.log('无可用优惠券');
				}
			},
			// 点击关闭优惠券弹层
			closeCouponPopupFn() {
				this.$refs.couponPopup.close()
			},
			// 点击选择优惠券
			couponRadioChange(evt) {
				console.log('点击选择优惠券evt', evt);
				this.computedPriceObj.orderMerchantRequestList[0].userCouponId = evt.detail.value
				this.computedPriceObj.orderMerchantRequestList[0].merId = this.loadPreOrderObj.merchantOrderVoList[0].merId
				this.computedPriceObj.preOrderNo = this.preOrderNo
				this.computedPriceObj.shippingType = this.loadPreOrderObj.shippingType
				for (let i = 0; i < this.couponOrderList.length; i++) {
					if (String(this.couponOrderList[i].id) === evt.detail.value) {
						this.couponOrderCurrent = i;
						break;
					}
				}
			},
			// 点击确认使用优惠券
			async confirmCouponFn() {
				if (this.computedPriceObj.orderMerchantRequestList[0].userCouponId !== '') {
					console.log('点击确认使用优惠券');
					this.computedOrderPrice()

				} else {
					uni.showToast({
						icon: 'none',
						title: '请选择优惠券',
						duration: 2000
					});
				}
			},
			// 重新计算订单价格
			async computedOrderPrice() {
				const res = await computedOrderPriceApi(this.computedPriceObj)
				if (res.code === 200) {
					console.log('计算订单价格', res);
					this.loadPreOrder()
					this.closeCouponPopupFn()
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 备注文本域输入事件
			descInput() {
				let txtVal = this.createOrderObj.orderMerchantRequestList[0].remark.length;
				this.start = txtVal;
			},
			// 点击提交订单
			async createOrderFn() {
				uni.setStorageSync('shaIMG', this.loadPreOrderObj.merchantOrderVoList[0].orderInfoList[0].image)
				uni.setStorageSync('shaTitle', this.loadPreOrderObj.merchantOrderVoList[0].orderInfoList[0].productName)

				this.createOrderObj.addressId = this.loadPreOrderObj.addressId || uni.getStorageSync('selectedAddress').id
				this.createOrderObj.orderMerchantRequestList[0].userCouponId = this.computedPriceObj
					.orderMerchantRequestList[
						0]
					.userCouponId || this.loadPreOrderObj.merchantOrderVoList[0].userCouponId
				this.createOrderObj.orderMerchantRequestList[0].merId = this.loadPreOrderObj.merchantOrderVoList[0].merId
				this.createOrderObj.preOrderNo = this.preOrderNo
				// this.createOrderObj.addressId = 10
				console.log('提交订单', this.createOrderObj);
				// const res = await createOrderApi(this.createOrderObj)
				// if (res.code === 200) {
				// 	console.log('res', res);
				// 	uni.navigateTo({
				// 		url: `/pages/goods/order_payment/index?orderNo=${res.data.orderNo}&payPrice=${res.data.payPrice}`
				// 	})
				// }
				createOrderApi(this.createOrderObj).then((res) => {
					console.log('res', res);
					if (res.code === 200) {
						console.log('res', res);
						uni.navigateTo({
							url: `/pages/goods/order_payment/index?orderNo=${res.data.orderNo}&payPrice=${res.data.payPrice}`
						})
					}
				}).catch(err => {
					console.log('err', err);
					uni.showToast({
						icon: 'none',
						title: err,
						duration: 2000
					});
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.order-comfrim-content {
		.order-comfrim-top-nav-box {
			position: fixed;
			top: 0rpx;
			z-index: 1000;
			width: 100%;
			background: #f5f5f5;

			.order-comfrim-top-nav-title {
				display: flex;
				align-items: center;

				.order-comfrim-top-nav-title-text {
					flex: 1;
					text-align: center;
					font-size: 34rpx;
					color: #222222;
				}
			}
		}

		.order-comfrim-main-box {
			display: flex;
			justify-content: center;
			margin-top: 30rpx;
			margin-bottom: 20rpx;
			width: 750rpx;

			.order-comfrim-main {
				width: 690rpx;

				.order-one-mode-box {
					width: 690rpx;

					.order-distribution-mode-box {
						padding-top: 30rpx;
						padding-left: 20rpx;
						padding-right: 20rpx;
						padding-bottom: 30rpx;
						width: 690rpx;
						// height: 274rpx;
						background: #ffffff;
						border-radius: 16rpx;

						.took-mode-box {
							width: 100%;

							.took-shop-name-box {
								display: flex;
								align-items: center;
								margin-bottom: 10rpx;
								width: 100%;

								.took-shop-identification {
									margin-right: 10rpx;
									width: 86rpx;
									height: 32rpx;
									background: #bdfd5b;
									border-radius: 4rpx;
									text-align: center;
									line-height: 32rpx;
									font-size: 22rpx;
									color: #222222;
								}

								.took-shop-name {
									flex: 1;
									font-size: 28rpx;
									color: #333333;
									font-weight: 700;
									line-clamp: 2;
									overflow: hidden;
									text-overflow: ellipsis;
									text-overflow: -webkit-ellipsis-lastline;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}
							}

							.took-shop-address-box {
								display: flex;
								justify-content: space-between;
								width: 100%;

								.took-shop-address-text {
									width: 550rpx;
									font-size: 26rpx;
									color: #999999;
									line-clamp: 2;
									overflow: hidden;
									text-overflow: ellipsis;
									text-overflow: -webkit-ellipsis-lastline;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}
							}
						}

						.mode-line {
							margin-top: 20rpx;
							margin-bottom: 30rpx;
							width: 100%;
							height: 2rpx;
							background: #efefef;
							border-radius: 10rpx;
						}

						.took-person-info-box {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.took-person-info-text {
								width: 550rpx;
								font-size: 28rpx;
								font-weight: 700;
								color: #333333;
							}
						}
					}

					.order-delivery-mode-box {
						padding-top: 30rpx;
						padding-left: 20rpx;
						padding-right: 20rpx;
						padding-bottom: 30rpx;
						width: 690rpx;
						// height: 274rpx;
						background: #ffffff;
						border-radius: 16rpx;

						.delivery-not-address-box {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.delivery-not-address-text {
								font-size: 28rpx;
								color: #ff3127;
							}
						}

						.delivery-have-address-box {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.delivery-have-address-left-box {
								width: 544rpx;

								.address-left-text {
									margin-bottom: 10rpx;
									width: 544rpx;
									font-size: 28rpx;
									color: #333333;
									line-clamp: 2;
									overflow: hidden;
									text-overflow: ellipsis;
									text-overflow: -webkit-ellipsis-lastline;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;

									.address-left-tag {
										margin-right: 20rpx;
										display: inline-block;
										width: 108rpx;
										height: 32rpx;
										background: #4da6fa;
										border-radius: 8rpx;
										text-align: center;
										line-height: 32rpx;
										font-size: 22rpx;
										color: #ffffff;
									}
								}



								.address-left-info {
									width: 544rpx;
									font-size: 26rpx;
									color: #999999;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}
							}
						}
					}
				}

				.order-two-mode-box {
					padding-top: 30rpx;
					padding-bottom: 30rpx;
					padding-left: 16rpx;
					padding-right: 16rpx;
					width: 690rpx;
					background: #ffffff;
					border-radius: 28rpx;

					.order-two-mode-header-box {
						display: flex;
						margin-bottom: 30rpx;
						width: 100%;
						height: 68rpx;
						background: #f5f5f5;
						border-radius: 16rpx;

						.order-two-mode-header-item-box {
							width: 329rpx;
							height: 68rpx;
							// background: #f5f5f5;
							border-radius: 16rpx;
							text-align: center;
							line-height: 68rpx;
							font-size: 28rpx;
							font-weight: 700;
							color: #222222;
						}
					}

					.order-two-mode-main-box {
						width: 100%;

						.order-two-mode-delivery-box {
							width: 100%;

							.order-two-mode-not-address {
								display: flex;
								justify-content: space-between;
								align-items: center;
								width: 100%;

								.two-mode-not-address-text {
									font-size: 28rpx;
									color: #ff3127;
								}
							}

							.order-two-mode-have-address-box {
								display: flex;
								justify-content: space-between;
								align-items: center;
								width: 100%;

								.order-two-mode-have-address-left-box {
									width: 550rpx;

									.two-mode-have-address-text {
										width: 550rpx;
										margin-bottom: 10rpx;
										font-size: 28rpx;
										font-weight: bold;
										color: #333333;
										line-clamp: 2;
										overflow: hidden;
										text-overflow: ellipsis;
										text-overflow: -webkit-ellipsis-lastline;
										display: -webkit-box;
										-webkit-line-clamp: 2;
										line-clamp: 2;
										-webkit-box-orient: vertical;
									}

									.two-mode-have-person-info {
										width: 550rpx;
										font-size: 26rpx;
										color: #999999;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis;
									}
								}

							}
						}

						.order-two-mode-distribution-box {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.two-mode-distribution-left-box {
								width: 550rpx;

								.two-mode-distribution-merchant {
									width: 550rpx;
									font-size: 28rpx;
									font-weight: bold;
									color: #333333;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.two-mode-distribution-address {
									width: 550rpx;
									font-size: 26rpx;
									color: #999999;
									line-clamp: 2;
									overflow: hidden;
									text-overflow: ellipsis;
									text-overflow: -webkit-ellipsis-lastline;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.two-mode-person-info-text {
									width: 550rpx;
									font-size: 28rpx;
									font-weight: 700;
									color: #333333;
								}

								.two-mode-distribution-info {
									width: 550rpx;
									font-size: 26rpx;
									color: #999999;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}
							}
						}

					}
				}

				.order-three-mode-box {
					padding-top: 30rpx;
					padding-bottom: 30rpx;
					padding-left: 21rpx;
					padding-right: 21rpx;
					width: 690rpx;
					background: #ffffff;
					border-radius: 28rpx;

					.order-three-mode-header-box {
						display: flex;
						margin-bottom: 30rpx;
						width: 100%;
						height: 68rpx;
						background: #f5f5f5;
						border-radius: 8rpx;

						.order-three-mode-header-item-box {
							width: 216rpx;
							height: 68rpx;
							border-radius: 8rpx;
							text-align: center;
							line-height: 68rpx;
							font-size: 28rpx;
							font-weight: 700;
							color: #222222;
						}
					}

					.order-three-mode-main-box {
						width: 100%;

						.order-three-mode-delivery-box {
							width: 100%;

							.order-three-mode-not-address {
								display: flex;
								justify-content: space-between;
								align-items: center;
								width: 100%;

								.three-mode-not-address-text {
									font-size: 28rpx;
									color: #ff3127;
								}
							}

							.order-three-mode-have-address-box {
								display: flex;
								justify-content: space-between;
								align-items: center;
								width: 100%;

								.order-three-mode-have-address-left-box {
									width: 550rpx;

									.three-mode-have-address-text {
										width: 550rpx;
										margin-bottom: 10rpx;
										font-size: 28rpx;
										font-weight: bold;
										color: #333333;
										line-clamp: 2;
										overflow: hidden;
										text-overflow: ellipsis;
										text-overflow: -webkit-ellipsis-lastline;
										display: -webkit-box;
										-webkit-line-clamp: 2;
										line-clamp: 2;
										-webkit-box-orient: vertical;
									}

									.three-mode-have-person-info {
										width: 550rpx;
										font-size: 26rpx;
										color: #999999;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis;
									}
								}

							}
						}

						.order-three-mode-distribution-box {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.three-mode-distribution-left-box {
								width: 550rpx;

								.three-mode-distribution-merchant {
									width: 550rpx;
									font-size: 28rpx;
									font-weight: bold;
									color: #333333;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.three-mode-distribution-address {
									width: 550rpx;
									font-size: 26rpx;
									color: #999999;
									line-clamp: 2;
									overflow: hidden;
									text-overflow: ellipsis;
									text-overflow: -webkit-ellipsis-lastline;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.three-mode-person-info-text {
									width: 550rpx;
									font-size: 28rpx;
									font-weight: 700;
									color: #333333;
								}

								.three-mode-distribution-info {
									width: 550rpx;
									font-size: 26rpx;
									color: #999999;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}
							}
						}

					}
				}
			}
		}

		.order-goods-list-box {
			display: flex;
			justify-content: center;
			margin-bottom: 20rpx;
			width: 100%;

			.order-goods-list-main-box {
				padding-top: 20rpx;
				padding-bottom: 25rpx;
				padding-left: 20rpx;
				padding-right: 20rpx;
				width: 690rpx;
				background: #ffffff;
				border-radius: 16rpx;

				.order-goods-list-header-box {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 10rpx;
					width: 100%;

					.header-left-box {
						display: flex;
						align-items: center;

						.header-merchant-name-box {
							margin-right: 5rpx;
							max-width: 350rpx;
							font-size: 28rpx;
							font-weight: bold;
							color: #333333;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.header-type-tag {
							width: 64rpx;
							height: 32rpx;
							background: #bdfd5b;
							border-radius: 4rpx;
							font-size: 22rpx;
							text-align: center;
							line-height: 32rpx;
							color: #222222;
						}
					}
				}

				.order-goods-list-time-box {
					font-size: 26rpx;
					color: #ff3127;
				}

				.order-goods-list-line {
					margin-top: 20rpx;
					margin-bottom: 21rpx;
					width: 100%;
					height: 2rpx;
					background: #efefef;
					border-radius: 10rpx;
				}

				.order-goods-item-main-box {
					width: 100%;

					.order-goods-item-content-box {
						margin-bottom: 20rpx;
						width: 100%;

						.order-goods-item-box {
							display: flex;
							margin-bottom: 12rpx;
							width: 100%;

							.goods-item-img {
								margin-right: 20rpx;
								width: 180rpx;
								height: 180rpx;
								border-radius: 8rpx;
							}

							.goods-item-info {
								flex: 1;

								.goods-item-name {
									margin-bottom: 14rpx;
									width: 100%;
									font-size: 28rpx;
									color: #333333;
									line-clamp: 2;
									overflow: hidden;
									text-overflow: ellipsis;
									text-overflow: -webkit-ellipsis-lastline;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.goods-item-center-box {
									display: flex;
									justify-content: space-between;
									align-items: center;
									margin-bottom: 10rpx;
									width: 100%;

									.goods-item-sku {
										padding: 4rpx;
										// width: 80rpx;
										// height: 42rpx;
										line-height: 42rpx;
										background: #f5f5f5;
										border-radius: 8rpx;
										font-size: 24rpx;
										color: #666666;
									}

									.goods-item-num {
										font-size: 24rpx;
										color: #222222;
									}
								}

								.goods-item-price {
									font-size: 36rpx;
									color: #ff2222;
									font-weight: bold;
								}
							}
						}

						.order-goods-intro {
							padding-left: 10rpx;
							padding-right: 10rpx;
							padding-top: 12rpx;
							padding-bottom: 12rpx;
							width: 100%;
							background: #f5f5f5;
							border-radius: 16rpx;
							font-size: 24rpx;
							color: #999999;
						}
					}

				}

				.order-goods-list-receipts {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 30rpx;
					width: 100%;

					.receipts-text {
						font-size: 28rpx;
						color: #222222;
					}
				}

				.order-goods-total-info-box {
					display: flex;
					justify-content: flex-end;
					align-items: center;
					width: 100%;

					.total-info-text {
						margin-right: 10rpx;
						font-size: 26rpx;
						color: #333333;

						&:last-child {
							margin-right: 0;
						}
					}

					.total-info-price {
						font-size: 36rpx;
						font-weight: bold;
						color: #ff2222;
					}
				}
			}
		}

		.order-goods-price-detail-box {
			display: flex;
			justify-content: center;
			margin-bottom: 20rpx;
			width: 100%;

			.price-detail-main-box {
				box-sizing: border-box;
				padding-bottom: 30rpx;
				width: 690rpx;
				// height: 338rpx;
				background: #ffffff;
				border-radius: 16rpx;

				.price-detail-header-box {
					margin-bottom: 30rpx;
					padding-top: 20rpx;
					padding-left: 20rpx;
					width: 100%;
					// height: 80rpx;
					border-bottom: 2rpx solid #eeeeee;
					line-height: 80rpx;
					font-weight: bold;
					font-size: 28rpx;
					color: #333333;
				}

				.price-detail-info-box {
					padding-left: 20rpx;
					padding-right: 20rpx;
					width: 100%;

					.detail-info-item-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 36rpx;
						width: 100%;

						&:last-child {
							margin-bottom: 0;
						}

						.detail-info-item-title {
							font-size: 28rpx;
							color: #999999;
						}

						.detail-info-item-price {
							font-size: 36rpx;
							font-weight: bold;
							color: #ff2222;
						}

						.detail-info-item-freight-price {
							font-size: 36rpx;
							font-weight: bold;
							color: #222222;
						}

						.detail-info-item-coupon {
							display: flex;
							align-items: center;
							font-size: 28rpx;
							color: #222222;
						}
					}
				}
			}
		}

		.order-goods-remark-box {
			display: flex;
			justify-content: center;
			margin-bottom: 30rpx;
			width: 100%;

			.order-goods-remark-main-box {
				display: flex;
				padding: 20rpx;
				width: 690rpx;
				height: 222rpx;
				background: #ffffff;
				border-radius: 16rpx;

				.remark-label {
					margin-right: 10rpx;
					font-size: 28rpx;
					color: #999999;
				}

				.remark-textarea-box {
					position: relative;
					flex: 1;
					height: 182rpx;

					.remark-textarea-main {
						width: 100%;
						height: 150rpx;
						font-size: 26rpx;
						color: #222222;
					}

					.remark-textarea-word-count {
						position: absolute;
						right: 0;
						bottom: 0;
						font-size: 26rpx;
						color: #b2b2b2;
					}
				}
			}
		}

		.order-goods-bottom-placeholder {
			width: 750rpx;
			height: 188rpx;
		}

		.order-goods-bottom-box {
			position: fixed;
			left: 0;
			bottom: 0;
			display: flex;
			z-index: 90;
			justify-content: flex-end;
			padding-top: 18rpx;
			padding-bottom: 18rpx;
			padding-right: 30rpx;
			padding-left: 30rpx;
			width: 750rpx;
			height: 188rpx;
			background: #ffffff;
			border-radius: 32rpx 32rpx 0rpx 0rpx;

			.bottom-amount-statistics {
				flex: 1;

				.actual-payment-box {
					width: 100%;
					font-size: 26rpx;
					color: #333333;
					text-align: end;

					.actual-payment-num {
						font-size: 36rpx;
						font-weight: 700;
						color: #ff2222;
					}
				}

				.discount-amount-box {
					font-size: 26rpx;
					color: #999999;
					text-align: end;

					.discount-amount-num {
						display: inline-block;
						margin-left: 8rpx;
						font-size: 26rpx;
						color: #ff2222;
					}
				}
			}

			.bottom-submit-order-btn {
				margin-left: 20rpx;
				width: 286rpx;
				height: 84rpx;
				background: #bdfd5b;
				border-radius: 16rpx;
				text-align: center;
				line-height: 84rpx;
				font-size: 32rpx;
				color: #222222;
			}
		}

		.took-info-popup-content {
			padding-top: 30rpx;
			width: 750rpx;

			.info-popup-header-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 30rpx;
				padding: 0 26rpx;
				width: 100%;

				.info-popup-header-title {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					font-weight: 700;
					color: #222222;
				}

				.info-popup-header-icon {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.info-popup-main-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-left: 30rpx;
				padding-right: 30rpx;
				width: 100%;

				.info-label-box {
					font-size: 28rpx;
					color: #666666;
				}

				.info-input-box {
					width: 260rpx;
					text-align: right;
					font-size: 28rpx;
					color: #222222;
				}
			}

			.info-popup-line {
				margin-top: 31rpx;
				margin-bottom: 34rpx;
				width: 100%;
				height: 2rpx;
				background: #f5f5f5;
			}

			.info-popup-bottom-box {
				display: flex;
				justify-content: center;
				margin-top: 96rpx;
				width: 100%;

				.info-popup-bottom-btn {
					width: 670rpx;
					height: 88rpx;
					background: #bdfd5b;
					border-radius: 16rpx;
					text-align: center;
					line-height: 88rpx;
					font-size: 32rpx;
					font-weight: 700;
					color: #222222;
				}
			}
		}

		.time-popup-content {
			padding-top: 32rpx;
			width: 750rpx;

			.time-popup-header-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 26rpx;
				padding: 0 26rpx;
				width: 100%;

				.time-popup-header-title {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					font-weight: 700;
					color: #222222;
				}

				.time-popup-header-icon {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.time-popup-main-box {
				display: flex;
				justify-content: center;
				margin-bottom: 86rpx;
				width: 750rpx;

				.time-popup-main-list-box {
					padding-top: 20rpx;
					padding-bottom: 20rpx;
					padding-left: 25rpx;
					padding-right: 25rpx;
					width: 710rpx;
					height: 618rpx;
					background: #ffffff;
					border-radius: 16rpx;

					.time-popup-main-list-scroll-box {
						width: 660rpx;
						height: 578rpx;

						.time-list-box {
							width: 100%;

							&:last-child {
								margin-top: 23rpx;
							}

							.time-list-title {
								margin-bottom: 1rpx;
								width: 142rpx;
								height: 44rpx;
								background: #bdfd5b;
								border-radius: 8rpx;
								text-align: center;
								line-height: 44rpx;
								font-size: 26rpx;
								color: #222222;
							}

							.time-item-box {
								display: flex;
								justify-content: space-between;
								align-items: center;
								width: 100%;
								height: 79rpx;
								border-bottom: 2rpx solid #efefef;
								border-radius: 10rpx;

								.time-item-text {
									font-size: 28rpx;
									color: #666666;
								}
							}
						}
					}
				}
			}

			.time-popup-bottom-box {
				display: flex;
				justify-content: center;
				width: 100%;

				.time-popup-bottom-btn {
					width: 670rpx;
					height: 88rpx;
					background: #bdfd5b;
					border-radius: 16rpx;
					text-align: center;
					line-height: 88rpx;
					font-size: 30rpx;
					color: #222222;
				}
			}
		}

		.coupon-popup-content {
			padding-top: 32rpx;
			width: 750rpx;

			.coupon-popup-header-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				padding: 0 26rpx;
				width: 100%;

				.coupon-popup-header-title {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					font-weight: 700;
					color: #222222;
				}

				.coupon-popup-header-icon {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.coupon-popup-main-box {
				display: flex;
				justify-content: center;
				margin-bottom: 70rpx;
				width: 100%;

				.coupon-popup-list-box {
					width: 700rpx;
					height: 900rpx;

					.coupon-popup-main-list-scroll-box {
						width: 700rpx;
						height: 900rpx;

						.coupon-popup-main-list-box {
							width: 700rpx;

							.coupon-item-box {
								display: flex;
								align-items: center;
								margin-bottom: 20rpx;
								width: 700rpx;

								&:last-child {
									margin-bottom: 0;
								}

								.coupon-item-radio {
									// margin-right: 10rpx;
								}

								.coupon-item-content {
									display: flex;
									align-items: center;
									width: 650rpx;
									height: 210rpx;

									.coupon-item-content-left-box {
										padding: 20rpx;
										width: 450rpx;
										height: 210rpx;
										background: #ffffff;
										border-top-right-radius: 15rpx;
										border-bottom-right-radius: 15rpx;

										.coupon-item-name {
											margin-bottom: 10rpx;
											width: 100%;
											font-size: 26rpx;
											color: #333333;
											line-clamp: 2;
											overflow: hidden;
											text-overflow: ellipsis;
											text-overflow: -webkit-ellipsis-lastline;
											display: -webkit-box;
											-webkit-line-clamp: 2;
											line-clamp: 2;
											-webkit-box-orient: vertical;
										}

										.coupon-item-time {
											margin-bottom: 20rpx;
											font-size: 22rpx;
											color: #ff2d18;
										}

										.coupon-item-remark {
											font-size: 22rpx;
											color: #999999;
										}
									}

									.coupon-item-content-right-box {
										position: relative;
										padding-top: 15rpx;
										width: 200rpx;
										height: 210rpx;
										background: #ff5400;
										border-radius: 15rpx;

										.coupon-item-content-right-left-top {
											position: absolute;
											top: -1rpx;
											left: -1rpx;
											width: 15rpx;
											height: 15rpx;
											z-index: 1;
											background-color: #f5f5f5;
											border-radius: 0 0 15rpx 0;
										}

										.coupon-item-content-right-left-bottom {
											position: absolute;
											bottom: -1rpx;
											left: -1rpx;
											width: 15rpx;
											height: 15rpx;
											z-index: 1;
											background-color: #f5f5f5;
											border-radius: 0 15rpx 0 0;
										}

										.coupon-item-price-box {
											// display: flex;
											// justify-content: center;
											width: 100%;
											text-align: center;
											font-size: 60rpx;
											color: #ffffff;

											.coupon-item-price-symbol {
												font-size: 36rpx;
												color: #ffffff;
											}
										}

										.coupon-item-condition {
											font-size: 20rpx;
											text-align: center;
											color: #ffffff;
										}

										.coupon-item-btn-box {
											display: flex;
											justify-content: center;
											margin-top: 14rpx;
											width: 100%;

											.coupon-item-btn {
												width: 140rpx;
												height: 56rpx;
												background: #ffffff;
												border-radius: 28rpx;
												font-size: 26rpx;
												line-height: 56rpx;
												text-align: center;
												color: #ff5400;
											}
										}
									}
								}
							}
						}
					}
				}
			}

			.coupon-popup-bottom-box {
				display: flex;
				justify-content: center;
				width: 100%;

				.coupon-popup-bottom-btn {
					width: 670rpx;
					height: 88rpx;
					background: #bdfd5b;
					border-radius: 16rpx;
					text-align: center;
					line-height: 88rpx;
					font-size: 30rpx;
					color: #222222;
				}
			}
		}
	}
</style>