<template>
	<view :data-theme="theme">
		<view class="container_money">
			<view class="container_money_bg"></view>
			<view class="container_money_account">
				<view class="now_amount">
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view>
							<view style="font-size: 26rpx;color: #999999;">今日佣金(元)</view>
							<view style="margin-top: 10rpx;font-size: 52rpx;font-weight: 600;">{{statistics.nowDayPrice || 0}}</view>
						</view>
						<view class="apply_btn" @click="withdrawCash('/pages/users/withdraw_cash/index')">申请提现</view>
					</view>
					<view style="display: flex;margin-top: 50rpx;justify-content: space-between;">
						<view @click="commissionDetails('1')" class="sale_amount" style="width: 40%;font-size: 24rpx;color: #999999;">
							<view style="display: flex;align-items: center;"><text>累计佣金(元)</text><img @click.stop="showModel('累计佣金','所有分享给其他用户并付款成功的商品佣金')" class="tip_icon" src="/static/images/tip_icon.png" alt="" /></view>
							<view style="color: #222222;margin-top: 10rpx;font-size: 32rpx;font-weight: 600;">{{statistics.allPrice || 0}}</view>
							<view class="right-icon"></view>
						</view>
						<view style="border-right: 1px solid #EAEAEA;"></view>
						<view @click="commissionDetails('2')" class="sale_amount" style="width: 40%;font-size: 24rpx;color: #999999;">
							<view style="display: flex;align-items: center;"><text>累计销售额(元)</text><img @click.stop="showModel('累计销售额','所有分享给其他用户并付款成功的订单实付金额总和')" class="tip_icon" src="/static/images/tip_icon.png" alt="" /></view>
							<view style="color: #222222;margin-top: 10rpx;font-size: 32rpx;font-weight: 600;">{{statistics.salePrice || 0}}</view>
							<view class="right-icon"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view style="padding: 32rpx 20rpx;font-size: 24rpx;color: #999999;">佣金将在订单完成后{{statistics.freezeDay||0}}天后方可转为可提现佣</view>
		<view class="withdraw_cash">
			<view class="withdraw_cash_list" >
				<view class="withdraw_cash_left">
					<view style="font-size: 28rpx;display: flex;align-items: center;"><text>累计提现</text><img @click="showModel('累计提现','提现成功总金额')" class="tip_icon" src="/static/images/tip_icon.png" alt="" /></view>
					<view style="margin-top: 20rpx;color: #999999;font-size: 32rpx;">¥{{statistics.settledCommissionPrice || 0}}</view>
				</view>
			</view>
			<view class="withdraw_cash_list">
				<view class="withdraw_cash_left">
					<view style="font-size: 28rpx;display: flex;align-items: center;"><text>可提现</text><img @click="showModel('可提现','可申请提现的金额，即到达可提现佣金金额才可提取')" class="tip_icon" src="/static/images/tip_icon.png" alt="" /></view>
					<view style="margin-top: 20rpx;color: #999999;font-size: 32rpx;">¥{{statistics.brokeragePrice || 0}}</view>
				</view>
			</view>
			<view class="withdraw_cash_list">
				<view class="withdraw_cash_left">
					<view style="font-size: 28rpx;display: flex;align-items: center;"><text>提现中</text><img @click="showModel('提现中','已申请提现，但后台还在处理中/未处理的金额')" class="tip_icon" src="/static/images/tip_icon.png" alt="" /></view>
					<view style="margin-top: 20rpx;color: #999999;font-size: 32rpx;">¥{{statistics.brokeragePriceing || 0}}</view>
				</view>
			</view>
			<view class="withdraw_cash_list">
				<view class="withdraw_cash_left">
					<view style="font-size: 28rpx;display: flex;align-items: center;"><text>待结算</text><img @click="showModel('待结算','尚未到达结算周期的佣金')" class="tip_icon" src="/static/images/tip_icon.png" alt="" /></view>
					<view style="margin-top: 20rpx;color: #999999;font-size: 32rpx;">¥{{statistics.freezePrice || 0}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getProductHot
	} from '@/api/product.js';
	import {
		userActivity,
		getMyAccountApi,
		getBillList,
		getRechargeApi,
		rechargeCreateApi,
		myPromotion
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		alipayQueryPayResult
	} from '@/api/order.js';
	import recommend from "@/components/base/recommend.vue";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		components: {
			recommend
		},
		data() {
			return {
				statistics: {},
			};
		},
		computed: mapGetters(['isLogin', 'userInfo']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.userDalance();
					}
				},
				deep: true
			}
		},
		onLoad() {
			let that = this;
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#B8FE4E',
			});
		},
		onShow() {
			if (this.isLogin) {
				this.userDalance();
			} else {
				toLogin();
			}
		},
		methods: {
			withdrawCash(url) {
				uni.navigateTo({
					url: url
				})
			},
			showModel(title,desc) {
				uni.showModal({
					title: title,
					content: desc,
					showCancel: false,
					confirmText: '关闭',
					success: function (res) {
						if (res.confirm) {
							console.log('用户点击确定');
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			userDalance() {
				myPromotion().then(res => {
					this.statistics = res.data;
				})
			},
			commissionDetails(type){
				uni.navigateTo({
					url: '/pages/users/commission_details/index?type='+type
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.container_money_bg{
		width: 100%;
		height: 292rpx;
		background-image: linear-gradient(#B8FE4E,#F2F8E0);
		position: absolute;
		left: 0;
		top: 0;
	}
	.container_money_account{
		padding: 0 20rpx;
		position: relative;
	}
	.tip_icon{
		margin-left: 8rpx;
		width: 28rpx;
		height: 28rpx;
	}
	.now_amount{
		position: relative;
		margin-top: 24rpx;
		// width: 100%;
		padding: 40rpx 30rpx;
		// height: 292rpx;
		background-color: #fff;
		border-radius: 20rpx;
		.apply_btn{
			width: 170rpx;
			height: 72rpx;
			background-color: #BDFD5B;
			line-height: 72rpx;
			border-radius: 999rpx;
			overflow: hidden;
			font-size: 28rpx;
			text-align: center;
		}
		.sale_amount{
			position: relative;
			.right-icon{
				position: absolute;
				right: 0;
				top: 50%;
				width: 14rpx;
				height: 14rpx;
				border-top: 1px solid #666666;
				border-right: 1px solid #666666;
				transform: rotate(45deg);
			}
		}
		
	}
	.withdraw_cash{
		padding: 0 20rpx;
		.withdraw_cash_list{
			margin-bottom: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			.withdraw_cash_left{
			}
			.right-icon{
				width: 14rpx;
				height: 14rpx;
				border-top: 1px solid #666666;
				border-right: 1px solid #666666;
				transform: rotate(45deg);
			}
		}
	}
</style>
