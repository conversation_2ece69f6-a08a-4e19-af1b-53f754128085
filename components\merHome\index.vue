<template>
	<view id="store" class="store">
		<view class='pictrue'>
			<image :src="merchantInfoNew.avatar" class=""></image>
		</view>
		<view class="text">
			<navigator  hover-class="none">
				<view class="flex merchantInfo">
					<!-- <text v-if="merchantInfoNew.isSelf" class="font-bg-red bt-color mr10 self_min merType">自营</text> -->
					<text v-if="isShowTypeId && !merchantInfoNew.isSelf"
						class="font-bg-red bt-color mr10 merType">{{merchantInfo.typeId | merchantTypeFilter}}</text>
					<text class="name">{{merchantInfoNew.name}}</text>
					<text v-if="isShowTypeId" class="iconfont icon-jiantou"></text>
				</view>
			</navigator>
			<view class="score">
				<view class='starsList'>
					<block v-for="(itemn, indexn) in merchantInfoNew.starLevel" :key="indexn">
						<text class='iconfont icon-pingfen font-color'></text>
					</block>
					<block v-if="Number(merchantInfoNew.starLevel)<5-Number(merchantInfoNew.starLevel)">
						<text v-for="(itemn, indexn) in 1" :key="indexn" class='iconfont icon-pingfen' style="color: #BBBBBB;"></text>
					</block>
				</view>
			</view>
		</view>
		<!-- <button v-if="type!=='home'"  hover-class="none" class="merCollect" :class="merchantInfoNew.isCollect ? 'care' : ''" @click="followToggle">
			<text v-if="!merchantInfoNew.isCollect" class="iconfont icon-guanzhu"></text>
			{{ merchantInfoNew.isCollect ? '已关注' : '关注' }}
		</button> -->
		<button v-if="merchantid == '1'" class="merCollect" hover-class="none" @click="goToStore1">进店</button>
		<navigator v-if="merchantid !== '1'" :url="`/pages/home/<USER>/merchant-class?merchantId=${merchantid}`" hover-class="none">
			<button class="merCollect" hover-class="none">进店</button>
		</navigator>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getMerCollectAddApi,
		getMerCollectCancelApi
	} from '@/api/merchant.js';
	import {
		followMer
	} from '@/libs/merchant.js';
	import {
		mapGetters
	} from "vuex";
	import {
		toLogin
	} from '@/libs/login.js';
	export default {
		data() {
			return {
				skeletonShow: true,
				isShow: true,
				avatar: '',
				merchantInfoNew: this.merchantInfo
			}
		},
		computed: {
			...mapGetters(["merchantClassify", "merchantType", 'isLogin', 'uid']),
		},
		props: {
			merchantInfo: {
				type: Object,
				default: () => {}
			},
			merid: {
				type: Number,
				default: () => 0
			},
			type: {
				type: String,
				default: () => 'detail'
			},
			isShowTypeId: {
				type: Boolean,
				default: () => true
			},
			merchantid: {
				type: String,
				default: () => 0
			}
		},
		watch: {
			merchantInfo: function(nVal, oVal) {
				this.merchantInfoNew = JSON.parse(JSON.stringify(nVal));
			}
		},
		methods: {
			// 设置是否关注
			followToggle: function() {
				if (this.isLogin === false) {
					toLogin();
				} else {
					followMer(this.merchantInfoNew.isCollect, this.merid).then(() => {
						this.$set(this.merchantInfoNew, 'isCollect', !this.merchantInfoNew.isCollect);
					});
				}
			},
			// 跳转到商品分类页面（tabBar页面）
			goToStore1: function() {
				uni.switchTab({
					url: '/pages/goods_cate/index'
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.care {
		border: 1px solid #FFFFFF;
		background: inherit !important;
	}

	.font-color {
		// @include main_color(theme);
		color: #FF7600!important;
	}

	.iconfont {
		font-size: 24rpx !important;
	}

	.icon-pingweifen {
		color: #ccc;
	}

	.merchantInfo {
		align-items: center;
		margin-bottom: 6rpx;
	}

	.store {
		position: relative;
		z-index: 5;
		display: flex;
		align-items: center;
		padding: 24rpx;

		.pictrue {
			width: 86rpx;
			height: 86rpx;
			border-radius: 6rpx;
		}

		.easy-loadimage,
		image,
		uni-image {
			width: 100%;
			height: 100%;
		}

		.text {
			flex: 1;
			min-width: 0;
			margin-right: 20rpx;
			margin-left: 20rpx;

			navigator {
				align-items: center;
				max-width: 100%;

				.name {
					max-width: 65%;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					font-weight: bold;
					font-size: 30rpx;
					line-height: 1;
					color: #FFFFFF;
				}

				.iconfont {
					margin-top: 3rpx;
					margin-left: 10rpx;
					font-size: 17rpx;
					color: #FFFFFF;
				}
			}

			.score {
				display: flex;
				align-items: center;
				margin-top: 18rpx;
				font-weight: 500;
				font-size: 24rpx;
				line-height: 1;

				.iconfont {
					font-size: 20rpx;
				}
			}
		}
		
	}

	.self_min {
		text-align: center;
	}
</style>
