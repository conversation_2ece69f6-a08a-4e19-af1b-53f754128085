<template>
	<view :data-theme="theme">
		<tui-skeleton v-if="skeletonShow"></tui-skeleton>
		<view class="pad-30 mer-list">
			<merchant-list :merchantList="merchantList" :isStreet="true"></merchant-list>
		</view>
		<view class='loadingicon acea-row row-center-wrapper mer-loading'>
			<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
		</view>
		<view class='no-shop' v-if="!merchantList.length && !loading">
			<view class='msg'>
				<text>暂无店铺，快去搜索其他店铺吧</text>
			</view>
		</view>
	</view>		
</template>
<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		mapGetters
	} from "vuex";
	import {
		getMerStreetApi
	} from '@/api/merchant.js';
	import merchantList from '@/components/merchantList/index.vue'
	import tuiSkeleton from '@/components/base/tui-skeleton.vue';
	let app = getApp();
	export default {
		computed: mapGetters(['userAddress']),
		data() {
			return {
				theme: app.globalData.theme,
				where: {
					page: 1,
					limit: 5,
				},
				merchantList: [],
				skeletonShow: false,
				loadTitle: '',
				loading: false,
				loadend: false
			}
		},
		components: {
			merchantList,
			tuiSkeleton
		},
		onLoad: function(options) {
			uni.setNavigationBarTitle({
				title: this.$t(`page.store.street`)
			})
			this.getMerStreet();
		},
		methods:{
			goback() {
				uni.navigateBack()
			},
			getMerStreet: function(isPage) {
				let that = this;
				that.skeletonShow = true
				if (that.loadend) return;
				if (that.loading) return;

				that.loading = true;
				that.loadTitle = '';

				if (isPage === true) {
					that.$set(that, 'merchantList', []);
				}
				if (that.userAddress) {
					that.where.city = that.userAddress.city;
					that.where.district = that.userAddress.district;
				}
				getMerStreetApi(that.where).then(res => {
					let list = res.data.list;
					let loadend = list.length < that.where.limit;

					that.merchantList = that.$util.SplitArray(list, that.merchantList);
					that.$set(that, 'merchantList', that.merchantList);

					that.loadend = loadend;
					that.$set(that.where, 'page', that.where.page + 1);
					that.loading = false;
					that.loadTitle = loadend ? that.$t('page.goodsList.no') : that.$t('page.goodsList.more');
					
					that.skeletonShow = false
				}).catch(err => {
					that.loading = false;
					that.loadTitle = that.$t(`page.goodsList.more`);
				});
			},
		},
		onReachBottom: function() {
			if (!this.loadend && !this.loading) {
				this.getMerStreet();
			}
		}
	}
</script>

<style scoped lang="scss">
	.pad-30{
		padding: 30rpx;
		/deep/.street-pad20{
			padding: 0;
		}
	}

	.mer {
		&-list {
			padding-bottom: 0rpx;
		}
		&-loading {
			padding-bottom: calc(15px + constant(safe-area-inset-bottom) / 2);
			padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);
		}
	}
	.no-shop {
		position: absolute;
		top: 0;
		height: 100%;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #F5F5F5;
		
		.msg {
			margin-top: -80rpx;
		}
	}
	
</style>
