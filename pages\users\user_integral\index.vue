<template>
	<view :data-theme="theme">
		<view class='integral-page'>
			<!-- 自定义导航栏 -->
			<view class="custom-nav" :style="{ paddingTop: statusBarHeight + 'px' }">
				<view class="nav-content" :style="{ height: navBarHeight + 'px' }">
					<view class="nav-left" @click="goBack">
						<text class="iconfont icon-fanhui"></text>
					</view>
					<view class="nav-title">积分</view>
					<view class="nav-right"></view>
				</view>
			</view>

			<!-- 顶部渐变背景区域 -->
			<view class='header' :style="{ marginTop: (navBarHeight) + 'px' }">
				<view class='score-section'>
					<view class='score-left'>
						<view class='score-label-wrapper'>
							<text class='my-score-label'>我的积分</text>
							<view class='question-icon' @click='showRuleModal'>
								<text class='question-text'>?</text>
							</view>
						</view>
						<text class='score-value'>{{ signData.integralAll || integral.integral || 0 }}</text>
					</view>
					<view class='score-right'>
						<!-- 右侧背景图片位置预留 -->
					</view>
				</view>
				<!-- 连续签到区域 -->
				<view class='sign-section'>
					<view class='sign-header'>
						<text class='sign-text'>已连续签到{{ signData.signDayNum || 0 }}天</text>
					</view>
					<view class='sign-calendar'>
						<view class='day-item' v-for="(day, index) in weekDays" :key="index"
							@click="handleSignClick(day, index)">
							<view class='day-content'>
								<text class='day-score'>+{{ day.score }}</text>
								<view class='day-icon'>
									<image
										:src="day.signed ? '/static/images/sign_integral.png' : '/static/images/nosign_integral.png'"
										mode="aspectFit"></image>
								</view>
							</view>
							<text class='day-date' :class="{ 'signed-text': day.showStatus }">{{ day.displayText }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 积分明细和兑换记录按钮 -->
			<view class='action-buttons'>
				<view class='button-item' @click='goToDetail'>
					<text class='button-text'>积分明细</text>
					<text class='button-arrow'>→</text>
				</view>
				<view class='button-item' @click='goToExchange'>
					<text class='button-text'>兑换记录</text>
					<text class='button-arrow'>→</text>
				</view>
			</view>

			<!-- 积分攻略 -->
			<view class='strategy-section'>
				<view class='strategy-title'>积分攻略</view>
				<view class='strategy-list'>
					<view class='strategy-item' v-for="(item, index) in integrationTaskList" :key="index"
						@click='handleStrategyClick(item, index)'>
						<view class='strategy-icon'>
							<image :src="getStrategyIcon(index)" mode="aspectFit"></image>
						</view>
						<view class='strategy-content'>
							<text class='strategy-name'>{{ item.label }}</text>
							<text class='strategy-desc'>{{ item.tipsDesc }}</text>
						</view>
						<view class='strategy-score'>
							<image src='/static/images/sign_integral.png' mode="aspectFit" class="score-icon"></image>
							<text class='score-plus'>+{{ item.amt }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 热门推荐 -->
			<view class='recommend-section'>
				<view class='recommend-header'>
					<view class='recommend-title-wrapper'>
						<text class='recommend-title'>热门推荐</text>
						<text class='recommend-subtitle'>精选好物任您选</text>
					</view>
				</view>
				<view class='recommend-grid'>
					<view class='recommend-item' v-for="(item, index) in recommendList" :key="index"
						@click="goToProduct(item)">
						<view class='product-image-wrapper'>
							<image class='product-image' :src="item.image" mode="aspectFill"></image>
							<!-- 已售罄蒙版 -->
							<view class='soldout-mask' v-if="item.soldOut">
								<image class='soldout-icon' src='https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/24/349175254fb54147bc6ca3339d809e790nkfo3nus9.png' mode="aspectFit">
								</image>
							</view>
						</view>
						<view class='product-info'>
							<text class='product-name'>{{ item.name }}</text>
							<view class='product-price'>
								<text class='price-number'>{{ item.price }}</text>
								<text class='price-unit'>积分</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 签到结果弹窗 -->
			<view class='sign-modal' v-if="showSignModal" @click="closeSignModal">
				<view class='modal-content' @click.stop>
					<view class='modal-bg'>
						 <image src='https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/3a1506ef37af42db8fc582075d9c815c8157558bgh.png' mode="aspectFill" class='modal-bg-image'></image> 
					</view>
					<view class='modal-body'>
						<text class='modal-title'>{{ signResult.success ? '恭喜完成签到' : '签到失败' }}</text>
						<text class='modal-subtitle' v-if="signResult.success">获得{{ signResult.score }}积分</text>
						<text class='modal-subtitle' style="color: #999999;" v-else>很遗憾签到失败</text>
						<view class='modal-button' @click="closeSignModal">
							<text class='button-text'>知道了</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { postIntegralUser, getIntegralList, signInfo, userSign, getIntegralRecordList } from '@/api/user.js';
import {
	toLogin
} from '@/libs/login.js';
import {
	mapGetters
} from "vuex";
import emptyPage from '@/components/emptyPage.vue'
let app = getApp();
export default {
	components: {
		emptyPage
	},
	data() {
		return {
			integral: {},
			signDays: 0,
			weekDays: [],
			signData: {
				signDateList: [],
				signDayNum: 0,
				integral: 0,
				experience: 0,
				integralAll: 0,
				amtList: []
			},
			theme: app.globalData.theme,
			statusBarHeight: 0,
			navBarHeight: 44,
			showSignModal: false,
			signResult: {
				success: false,
				score: 0
			},
			recommendLoading: false,
			recommendLoadend: false,
			recommendPage: 1,
			recommendLimit: 20,
			recommendList: [],
			// 积分任务列表
			integrationTaskList: []
		};
	},
	computed: mapGetters(['isLogin']),
	watch: {
		isLogin: {
			handler: function (newV, oldV) {
				if (newV) {
					this.getUserInfo();
					this.getSignInfo();
				}
			},
			deep: true
		}
	},
	onLoad() {
		this.initCustomNav();
		if (this.isLogin) {
			this.getUserInfo();
			this.getSignInfo();
			this.getRecommendList();
		} else {
			toLogin();
		}
	},
	onReachBottom: function() {
		this.getRecommendList();
	},
	methods: {
		// 获取热门推荐
		getRecommendList: function() {
			if(this.recommendLoading || this.recommendLoadend) return
			this.recommendLoading = true
			let params = {
				page: this.recommendPage,
				limit: this.recommendLimit
			}
			getIntegralRecordList(params).then(res => {
				this.recommendLoading = false
				this.recommendLoadend = res.data.list.length < this.recommendLimit
				this.recommendPage++
				this.recommendList = this.recommendList.concat(res.data.list.map(item => {
					return {
						...item,
						soldOut: item.stock === 0
					}
				}))
			}).catch(err => {
				this.recommendLoading = false
			})
		},
		goToProduct(item) {
			uni.navigateTo({
				url: `/pages/users/user_integral_details/index?id=${item.id}`
			});
		},
		// 初始化自定义导航栏
		initCustomNav() {
			// #ifdef MP-WEIXIN
			// 获取胶囊信息
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();

			this.statusBarHeight = systemInfo.statusBarHeight;

			// 计算导航栏高度：胶囊高度 + 胶囊距离顶部的距离 + 胶囊距离底部的距离
			this.navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;
			// #endif

			// #ifndef MP-WEIXIN
			const systemInfoOther = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfoOther.statusBarHeight;
			this.navBarHeight = 44;
			// #endif
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		initWeekDays() {
			const weekDays = [];
			const today = new Date();
			const todayStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;

			// 如果有amtList数据，直接使用API返回的7天数据
			if (this.signData.amtList && this.signData.amtList.length > 0) {
				this.signData.amtList.forEach((item, index) => {
					const date = new Date(item.day);
					const month = date.getMonth() + 1;
					const day = date.getDate();
					const dateStr = `${month.toString().padStart(2, '0')}.${day.toString().padStart(2, '0')}`;
					const fullDateStr = item.day;

					let signed = false;
					let displayText = dateStr;
					let showStatus = false;
					let isToday = false;

					// 检查是否已签到
					if (this.signData.signDateList && this.signData.signDateList.includes(fullDateStr)) {
						signed = true;
					}

					// 判断是否为今天
					if (fullDateStr === todayStr) {
						isToday = true;
						displayText = signed ? '已签' : '未签';
						showStatus = true;
					} else {
						// 过去的日期如果已签到显示"已签"，否则显示日期
						const itemDate = new Date(fullDateStr);
						const currentDate = new Date(todayStr);

						if (itemDate < currentDate) {
							displayText = signed ? '已签' : dateStr;
							showStatus = signed;
						} else {
							// 未来的日期
							displayText = dateStr;
							showStatus = false;
						}
					}

					weekDays.push({
						date: dateStr,
						fullDate: fullDateStr,
						signed: signed,
						score: item.amt,
						displayText: displayText,
						showStatus: showStatus,
						isToday: isToday,
						dayIndex: index
					});
				});
			} else {
				// 如果没有amtList数据，使用默认的7天数据（前一天、今天和后五天）
				for (let i = -1; i <= 5; i++) {
					const date = new Date(today);
					date.setDate(today.getDate() + i);

					const year = date.getFullYear();
					const month = date.getMonth() + 1;
					const day = date.getDate();
					const dateStr = `${month.toString().padStart(2, '0')}.${day.toString().padStart(2, '0')}`;
					const fullDateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

					let signed = false;
					let score = 1; // 默认积分
					let displayText = dateStr;
					let showStatus = false;
					let isToday = false;

					// 设置签到状态和显示文本
					if (i === -1) { // 昨天
						displayText = dateStr;
						showStatus = false;
					} else if (i === 0) { // 今天
						displayText = '未签';
						showStatus = true;
						isToday = true;
					} else { // 未来的日期
						displayText = dateStr;
						showStatus = false;
					}

					weekDays.push({
						date: dateStr,
						fullDate: fullDateStr,
						signed: signed,
						score: score,
						displayText: displayText,
						showStatus: showStatus,
						isToday: isToday,
						dayIndex: i
					});
				}
			}

			this.weekDays = weekDays;
		},
		getUserInfo: function () {
			let that = this;
			postIntegralUser().then(function (res) {
				that.$set(that, 'integral', res.data);
			});
		},
		// 获取签到信息
		getSignInfo: function () {
			let that = this;
			const currentDate = new Date();
			const month = `${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`;

			signInfo({ month: month }).then(function (res) {
				if (res.code === 200) {
					that.signData = res.data;
					that.signDays = res.data.signDayNum || 0;
					// 更新用户总积分
					if (res.data.integralAll) {
						that.integral.integral = res.data.integralAll;
					}

					// 设置积分任务列表
					if (res.data.integrationTaskList && res.data.integrationTaskList.length > 0) {
						that.$set(that, 'integrationTaskList', res.data.integrationTaskList);
						console.log('积分任务列表已更新:', that.integrationTaskList);
						console.log('列表长度:', that.integrationTaskList.length);
					} else {
						console.log('没有获取到积分任务列表数据');
					}
					// 初始化签到日历
					that.initWeekDays();
				}
			}).catch(function (error) {
				console.error('获取签到信息失败:', error);
				// 如果接口失败，仍然初始化默认的签到日历
				that.initWeekDays();
			});
		},
		goToDetail() {
			uni.navigateTo({
				url: '/pages/users/user_integral_detail/index'
			});
		},
		goToExchange() {
			uni.navigateTo({
				url: '/pages/users/user_integral_exchange/index'
			});
		},
		navigateToOrder() {
			uni.switchTab({
				url: '/pages/order/order/index'
			});
		},
		navigateToShop() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		},
		navigateToInvite() {
			uni.navigateTo({
				url: '/pages/user/invitation/index'
			});
		},
		// 显示积分规则说明
		showRuleModal() {
			uni.navigateTo({
				url: '/pages/users/integral_rules/index'
			});
		},
		// 处理签到点击
		handleSignClick(day, index) {
			if (day.isToday && !day.signed) {
				// 今天未签到，执行签到
				this.performSign(day, index);
			} else if (day.dayIndex < 0) {
				// 过去的日期，不做任何操作
				return;
			} else if (day.dayIndex > 0) {
				// 未来的日期，不做任何操作
				return;
			}
		},
		// 执行签到
		performSign(day, index) {
			// 调用签到API
			userSign().then(res => {
				console.log('签到响应:', res); // 调试日志
				console.log('响应code:', res.code); // 调试code
				console.log('响应data:', res.data); // 调试data

				// 检查多种可能的成功状态
				// 情况1: 标准响应格式 {code: 200, data: {...}}
				// 情况2: 直接返回数据格式 {awardIntegral: 10, integral: 1, ...}
				let isSuccess = false;
				let responseData = null;

				if (res.code === 200 || res.code === 0) {
					// 标准格式
					isSuccess = true;
					responseData = res.data;
				} else if (res.awardIntegral !== undefined) {
					// 直接返回数据格式
					isSuccess = true;
					responseData = res;
				} else if (res.data && res.data.awardIntegral !== undefined) {
					// 嵌套数据格式
					isSuccess = true;
					responseData = res.data;
				}

				if (isSuccess && responseData) {
					// 计算本次签到获得的总积分（awardIntegral + integral）
					const totalAward = (responseData.awardIntegral || 0) + (responseData.integral || 0);

					// 签到成功
					this.signResult = {
						success: true,
						score: totalAward
					};

					// 更新签到状态
					this.weekDays[index].signed = true;
					this.weekDays[index].displayText = '已签';

					// 更新连续签到天数
					this.signDays += 1;
					if (this.signData.signDayNum !== undefined) {
						this.signData.signDayNum += 1;
					}

					// 更新总积分（原有积分 + 本次获得的积分）
					if (this.integral && this.integral.integral !== undefined) {
						this.integral.integral += totalAward;
					}
					if (this.signData && this.signData.integralAll !== undefined) {
						this.signData.integralAll += totalAward;
					}

					console.log('签到成功，获得积分:', totalAward);

					// 刷新积分数据
					this.getIntegralInfo();
				} else {
					// 签到失败
					console.log('签到失败，响应码不匹配:', res.code);
					console.log('完整响应:', JSON.stringify(res));
					this.signResult = {
						success: false,
						score: 0
					};
				}

				// 显示结果弹窗
				this.showSignModal = true;
			}).catch(error => {
				console.error('签到失败:', error);

				// 签到失败
				this.signResult = {
					success: false,
					score: 0
				};

				// 显示结果弹窗
				this.showSignModal = true;
			});
		},
		// 关闭签到弹窗
		closeSignModal() {
			this.showSignModal = false;
		},

		// 获取积分攻略图标
		getStrategyIcon(index) {
			const icons = [
				'/static/images/evaluate_my.png',
				'/static/images/evaluate_my2.png',
				'/static/images/evaluate_my3.png'
			];
			return icons[index % icons.length];
		},

		// 处理积分攻略点击
		handleStrategyClick(item, index) {
			console.log('点击积分攻略:', item, index);
			// 这里可以根据需要跳转到相应页面或执行相应操作
			uni.showToast({
				title: `${item.label}: ${item.tipsDesc}`,
				icon: 'none',
				duration: 2000
			});
		},

		// 获取积分信息（新增方法用于刷新积分）
		getIntegralInfo() {
			this.getUserInfo();
		}
	}
}
</script>

<style scoped lang="scss">
.integral-page {
	min-height: 100vh;
	background: #f5f5f5;
}

/* 自定义导航栏 */
.custom-nav {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #bdfd5b;
}

.nav-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-left .iconfont {
	font-size: 36rpx;
	color: #333;
}

.nav-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.nav-right {
	width: 60rpx;
}

.header {
	// background: linear-gradient(180deg, #b8fe4e, #f2f8e0);
	background-image: url('https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/32c88932d6894743a63063d3ebe9f8edya3nn9wmo9.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	height: 600rpx;
	position: relative;
	padding: 40rpx 30rpx;
	box-sizing: border-box;
	position: relative;
}

.withdraw-btn {
	position: absolute;
	top: 40rpx;
	right: 30rpx;
	width: 100rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.withdraw-text {
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
}

.score-section {
	margin-top: 200rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.score-left {
	display: flex;
	flex-direction: column;
}

.score-label-wrapper {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 20rpx;
}

.my-score-label {
	color: #333;
	font-size: 28rpx;
}

.question-icon {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	border: 2rpx solid #333333;
	display: flex;
	align-items: center;
	justify-content: center;
	background: transparent;
}

.question-text {
	color: #999;
	font-size: 20rpx;
	font-weight: bold;
}

.score-value {
	color: #333;
	font-size: 68rpx;
	font-weight: bold;
	font-family: Bebas, Bebas-Regular;
}

.score-right {
	width: 150rpx;
	height: 150rpx;
	/* 预留右侧背景图片位置 */
}

.sign-section {
	border-radius: 20rpx;
	padding: 30rpx;
	background-image: url('/static/images/integral.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-position: top left;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	bottom: -120rpx;
	height: 290rpx;
	width: 90%;
}

.sign-header {
	margin-bottom: 30rpx;
}

.sign-text {
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
}

.sign-calendar {
	display: flex;
	justify-content: space-between;
	gap: 8rpx;
}

.day-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.day-content {
	background: #f5f5f5;
	border-radius: 12rpx;
	padding: 25rpx 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 10rpx;
	width: 80rpx;
}

.day-score {
	color: #ff6b35;
	font-size: 20rpx;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.day-icon {
	width: 40rpx;
	height: 40rpx;
}

.day-icon image {
	width: 100%;
	height: 100%;
}

.day-date {
	color: #666;
	font-size: 22rpx;
	text-align: center;
}

.signed-text {
	color: #999999 !important;
}

.action-buttons {
	display: flex;
	flex-direction: row;
	margin: 150rpx 30rpx 20rpx;
	gap: 20rpx;
}

.button-item {
	flex: 1;
	height: 90rpx;
	background: #FFFFFF;
	border: 4rpx solid #BDFD5B;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	box-sizing: border-box;
}

.button-item:first-child {
	border-radius: 20rpx;
}

.button-item:last-child {
	border-radius: 20rpx;
}

.button-text {
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
}

.button-arrow {
	color: #ccc;
	font-size: 24rpx;
}

.strategy-section {
	background: white;
	margin: 20rpx 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.strategy-title {
	background: linear-gradient(180deg, #b8fe4e, #f2f8e0);
	color: #333;
	font-size: 28rpx;
	font-weight: bold;
	padding: 20rpx 30rpx;
	margin: 0;
}

.strategy-list {
	padding: 0 30rpx;
}

.strategy-item {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.strategy-item:last-child {
	border-bottom: none;
}

.strategy-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	overflow: hidden;
}

.star-icon {
	background: linear-gradient(135deg, #ff4757, #ff3838);
}

.cart-icon {
	background: linear-gradient(135deg, #2ed573, #1dd1a1);
}

.invite-icon {
	background: linear-gradient(135deg, #ffa502, #ff6348);
}

.strategy-icon image {
	width: 100%;
	height: 100%;
}

.strategy-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.strategy-name {
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.strategy-desc {
	color: #999;
	font-size: 24rpx;
}

.strategy-score {
	background: #EEFFD2;
	border-radius: 20rpx;
	padding: 8rpx 15rpx;
	margin-left: 20rpx;
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.score-icon {
	width: 40rpx;
	height: 40rpx;
}

.score-plus {
	color: #333333;
	font-size: 24rpx;
	font-weight: 600;
}

/* 热门推荐样式 */
.recommend-section {
	margin: 20rpx 30rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.recommend-header {
	padding: 20rpx 30rpx;
}

.recommend-title-wrapper {
	display: flex;
	align-items: baseline;
	gap: 16rpx;
}

.recommend-title {
	color: #222222;
	font-size: 28rpx;
	font-weight: bold;
}

.recommend-subtitle {
	color: #999999;
	font-size: 24rpx;
}

.recommend-grid {
	display: flex;
	flex-wrap: wrap;
	padding: 30rpx;
	gap: 20rpx;
	justify-content: flex-start;
}

.recommend-item {
	width: calc(50% - 20rpx);
	background: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
}

.product-image-wrapper {
	position: relative;
	width: 100%;
	height: 240rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.product-image {
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
}

.soldout-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 16rpx;
}

.soldout-icon {
	width: 150rpx;
	height: 100rpx;
}

.product-info {
	padding: 20rpx;
}

.product-name {
	color: #222222;
	font-size: 24rpx;
	line-height: 1.4;
	margin-bottom: 16rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.product-price {
	display: flex;
	align-items: baseline;
	gap: 4rpx;
}

.price-number {
	color: #222222;
	font-size: 28rpx;
	font-weight: bold;
}

.price-unit {
	color: #222222;
	font-size: 20rpx;
}

/* 签到弹窗样式 */
.sign-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-start;
	justify-content: center;
	padding-top: 30vh;
	z-index: 9999;
}

.modal-content {
	position: relative;
	width: 600rpx;
	height: 500rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.modal-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.modal-bg-image {
	width: 100%;
	height: 100%;
}

.modal-body {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 40rpx;
	box-sizing: border-box;
}

.modal-title {
	color: #333;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	text-align: center;
	margin-top: 185rpx;
}

.modal-subtitle {
	color: #FF2222;
	font-size: 28rpx;
	text-align: center;
	margin-bottom: 10rpx;
}

.modal-button {
	background: #BDFD5B;
	border-radius: 10rpx;
	padding: 20rpx 60rpx;
	margin-top: 20rpx;
}

.modal-button .button-text {
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
}
</style>
