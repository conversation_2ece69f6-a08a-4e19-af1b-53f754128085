<template>
	<view :data-theme="theme">
		<view class="container_money">
			<view class="container_money_bg"></view>
			<view class="container_money_account">
				<view class="now_amount">
					<img src="/pages/aastatictow/static/images/balance_wallet.png" alt="" />
					<view class="now_amount_content">
						<view style="color: #666666;font-size: 25rpx;">当前账户余额</view>
						<view style="color: #222222;font-size: 56rpx;margin-top: 20rpx;">{{statistics.nowMoney || 0}}</view>
					</view>
				</view>
			</view>
			<view class="container_money_log">
				<view class='nav acea-row'>
					<view class='item' :class='type==="expenditure" ? "on":""' @click='changeType("expenditure")'>
						消费记录
						<view v-show='type==="expenditure"' class="line"></view>
					</view>
					<view class='item' :class='type==="recharge" ? "on":""' @click='changeType("recharge")'>
						充值记录
						<view v-show='type==="recharge"' class="line"></view>
					</view>
				</view>
				<view style="border-radius: 20rpx;overflow: hidden;">
					<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="scrolltolower">
						<view class='sign-record'>
							<view class='list borderPad' v-for="(item,index) in userBillList" :key="index">
								<view class='item'>
									<view style="padding-left: 10rpx;">
										<view style="padding: 20rpx 0 10rpx 0;">
											<text style="font-size: 40rpx;">{{monthStr(item.month)}}</text>月
										</view>
										<view style="font-size: 26rpx;">总支出：{{item.sumMoney || 0}}</view>
									</view>
									<view class='listn borRadius14'>
										<view class="itemn" v-for="(vo,indexn) in item.list":key="indexn">
											<view class='acea-row row-between-wrapper'>
												<view>
													<view class='name line1'>{{vo.type == 2?'支付':'充值'}}</view>
													<view>{{vo.createTime}}</view>
												</view>
												<view style="color: #222222;" class='num'>{{vo.type == 2?'-':'+'}}¥{{vo.amount}}</view>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class='loadingicon acea-row row-center-wrapper'>
								<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{userBillList.length > 0?loadTitle:''}}
							</view>
							<view class="no-bill" v-if="userBillList.length == 0 && !loading">
								<emptyPage title="暂无账单的记录哦～"></emptyPage>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
			<view class="container_money_footer">
				<button class="but" @click="onlineRecharge">在线充值</button>
			</view>
		</view>
		<!-- 在线充值弹框 -->
		<view class="product-window" :class="(cartAttr === true ? 'on' : '')">
			<form @submit="submitSub" report-submit='true'>
			<!-- 弹框头部 -->
			<view class="popup-header">
				<view class="popup-title">充值</view>
				<view class="close-btn" @click="closeAttr">
					<text class="iconfont icon-guanbi"></text>
				</view>
			</view>
			<view class="payment">
				<view style="font-size: 28rpx;font-weight: 600;color: #222222;padding-left: 20rpx;">当前账户余额：{{statistics.nowMoney || 0}}</view>
				<view class='tip picList'>
					<view class="pic-box pic-box-color acea-row row-center-wrapper row-column"
						:class="activePic === index ? 'pic-box-color-active' : ''" v-for="(item, index) in packageList"
						:key="index" @click="picCharge(index, item)">
						<view class="pic-number-pic">
							{{ item.price }}<span class="pic-number"> 元</span>
						</view>
						<view class="pic-number">赠送：{{ item.giveMoney }} 元</view>
					</view>
					<view class="pic-box pic-box-color acea-row row-center-wrapper"
						:class="parseFloat(activePic)===parseFloat(packageList.length)?'pic-box-color-active':''"
						@click="picCharge(packageList.length)">
						<input type="digit" placeholder="其他" v-model="money" @input="onInput($event)" maxlength="5"
							class="pic-box-money pic-number-pic uni-input" :placeholder-class="parseFloat(activePic) === parseFloat(packageList.length) ? 'placeColor':''"
							:class="parseFloat(activePic) === parseFloat(packageList.length) ? 'pic-box-color-active' : ''"
							 />
					</view>
					<view class="tips-box" style="margin-top: 20rpx;">
						<view class="tips-samll">注意事项：</view>
						<view class="tips-samll" v-for="item in noticeList" :key="item">
							{{ item }}
						</view>
					</view>
				</view>
				<!-- #ifndef  MP-->
				<view class='wrapper borRadius14  px-30' v-if='!active'>
					<view class='item'>
						<view>支付方式</view>
						<view class='list'>
							<view class='payItem acea-row row-middle' :class='curActive==index ?"on":""'
								@tap='payItem(index)' v-for="(item,index) in cartArr" :key='index'
								v-if="item.payStatus==1">
								<view class='name acea-row row-center-wrapper'>
									<view class='iconfont animated'
										:class='(item.icon) + " " + (animated==true&&active==index ?"bounceIn":"")'>
									</view>
									{{item.name}}
								</view>
								<view class='tip'>{{item.title}}</view>
							</view>
						</view>
					</view>
				</view>
				<!-- #endif -->
				<button class='but' formType="submit">确认</button>
				<view class="alipaysubmit" v-html="formContent"></view>
			</view>
			</form>
		</view>
		<view class="mask" @touchmove.prevent :hidden="cartAttr === false" @click="closeAttr"></view>
		<view v-if="false" class='my-account'>
			<view class='wrapper'>
				<view class='header'>
					<view class='headerCon'>
						<view class='account acea-row row-top row-between'>
							<view class='assets'>
								<view>账户余额(元)</view>
								<view class='money'>{{statistics.nowMoney || 0}}</view>
							</view>
							<view v-show="statistics.rechargeSwitch"
								@click="openSubscribe('/pages/users/user_payment/index?nowMoney=' + statistics.nowMoney)"
								class='recharge font_color'>充值</view>
						</view>
						<view class='cumulative acea-row row-top'>
							<view class='item' v-if="statistics.rechargeSwitch">
								<view>余额充值(元)</view>
								<view class='money'>{{statistics.recharge || 0}}</view>
							</view>
							<view class='item'>
								<view>余额消费(元)</view>
								<view class='money'>{{statistics.monetary || 0}}</view>
							</view>
						</view>
					</view>
				</view>
				<view class='nav acea-row row-middle'>
					<navigator class='item' hover-class='none' url='/pages/users/user_bill/index?type=all'>
						<view class='pictrue'>
							<text class="iconfont icon-s-zhangdanjilu icon_txt"></text>
						</view>
						<view>余额记录</view>
					</navigator>
					<navigator class='item' hover-class='none' url='/pages/users/user_bill/index?type=expenditure'>
						<view class='pictrue'>
							<text class="iconfont icon-s-xiaofeijilu icon_txt"></text>
						</view>
						<view>消费记录</view>
					</navigator>
					<navigator class='item' hover-class='none' url='/pages/users/user_bill/index?type=income'
						v-if="userInfo.rechargeSwitch">
						<view class='pictrue'>
							<text class="iconfont icon-s-chongzhijilu icon_txt"></text>
						</view>
						<view>充值记录</view>
					</navigator>
					<navigator class='item' hover-class='none' url='/pages/users/user_integral/index'>
						<view class='pictrue'>
							<text class="iconfont icon-jifenzhongxin icon_txt"></text>
						</view>
						<view>积分中心</view>
					</navigator>
				</view>
			</view>
			<recommend ref="recommendIndex" @noCommodity="noCommodity"></recommend>
			<view class='noCommodity' v-if="isNoCommodity">
				<view class='pictrue'>
					<image src='@/static/images/noShopper.png'></image>
				</view>
				<text class="text">暂无商品~</text>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getProductHot
	} from '@/api/product.js';
	import {
		userActivity,
		getMyAccountApi,
		getBillList,
		getRechargeApi,
		rechargeCreateApi
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		alipayQueryPayResult
	} from '@/api/order.js';
	import recommend from "@/components/base/recommend.vue";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		components: {
			recommend
		},
		data() {
			return {
				isClose: false,
				activity: {},
				hotScroll: false,
				statistics: {},
				hotPage: 1,
				hotLimit: 10,
				theme: app.globalData.theme,
				isNoCommodity: true,
				bgColor: '#B8FE4E',
				loadTitle: '加载更多',
				loading: true,
				loadend: false,
				page: 1,
				limit: 20,
				type: 'expenditure',
				userBillList: [],
				cartAttr: false,
				packageList: [],
				activePic: 0,
				rechar_id: 0,
				noticeList: [],
				money: "",
				payChannel: '',
				payType: 'weixin', //支付方式
				openType: 1, //优惠券打开方式 1=使用
				curActive: 0, //支付方式切换
			};
		},
		computed: mapGetters(['isLogin', 'userInfo']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.userDalance();
					}
				},
				deep: true
			}
		},
		onLoad() {
			let that = this;
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: that.bgColor,
			});
		},
		onShow() {
			if (this.isLogin) {
				// #ifdef H5
				var url = window.location.search;
				if (url) {
					var theRequest = new Object();
					if (url.indexOf("?") != -1) {
						var str = url.substr(1);
						var strs = str.split("&");
						for (var i = 0; i < strs.length; i++) {
							theRequest[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]);
						}
					}
					this.orderId = theRequest.out_trade_no; //返回的订单号
					this.alipayQueryPay();
				}
				// #endif
				this.userDalance();
				this.getUserBillList();
			} else {
				toLogin();
			}
		},
		methods: {
			/**
			 * 获取账户明细
			 */
			getUserBillList: function() {
				let that = this;
				if (that.loadend) return;
				that.loading = true;
				that.loadTitle = "";
				let data = {
					page: that.page,
					limit: that.limit,
					type: that.type
				}
				getBillList(data).then(function(res) {
					let list = res.data.list ? res.data.list : [],
					loadend = res.data.totalPage <= that.page;
					for (let i = 0; i < list.length; i++) {
						let time1 = list[i].month;
						let amount1 = list[i].sumMoney;
						let array1 = list[i].list;
						let isEquals = false;
						for (let j = 0; j < that.userBillList.length; j++) {
							let time2 = that.userBillList[j].month;
							let array2 = that.userBillList[j].list;
							if (time1 == time2) {
								array2.push.apply(array2, array1);
								that.userBillList[j].list = array2;
								isEquals = true;
								break;
							}
						}
						if (!isEquals) {
							that.userBillList.push({
								month: time1,
								sumMoney: amount1,
								list: array1
							})
						}
					}
			        that.$set(that, 'userBillList', that.userBillList);
					that.page += 1;
					that.loadend = loadend;
					
					that.loading = false;
					that.loadTitle = loadend ? "我也是有底线的~" : "加载更多";
				}, function(res) {
					that.loading = false;
					that.loadTitle = '加载更多';
				});
			},
			/**
			 * 切换导航
			 */
			changeType: function(type) {
				this.type = type;
				this.loadend = false;
				this.page = 1;
				this.$set(this, 'userBillList', []);
				this.getUserBillList();
			},
			noCommodity(e) {
				this.isNoCommodity = e == 0 ? true : false;
			},
			scrolltolower() {
				this.getUserBillList();
				console.log('加载中...')
			},
			monthStr(str) {
				let months = str?str.substring(5,7):str
				return months>9?months:months.substring(1,2)
			},
			async onlineRecharge() {
				await this.getRecharge();
				this.cartAttr = true
			},
			async closeAttr() {
				this.cartAttr = false
			},
			/**
			 * 充值额度选择
			 */
			async getRecharge() {
				await getRechargeApi()
					.then(res => {
						this.packageList = res.data.packageList;
						if (this.packageList[0]) {
							this.rechar_id = this.packageList[0].id;
							this.numberPic = this.packageList[0].price;
						}
						this.noticeList = res.data.noticeList || [];
					})
					.catch(res => {
						this.$dialog.toast({
							mes: res
						});
					});
			},
			/**
			 * 选择金额
			 */
			picCharge(idx, item) {
				this.activePic = idx;
				if (item === undefined) {
					this.rechar_id = 0;
					this.numberPic = "";
				} else {
					this.money = "";
					this.rechar_id = item.id;
					this.numberPic = item.price;
				}
			},
			onInput(e) {
				 let val = e.target.value.replace(/(^\s*)|(\s*$)/g, "")
				    if (!val) {
				        this.val = '';
				        return
				    }
				    var reg = /[^\d.]/g
				    // 只能是数字和小数点，不能是其他输入
				    val = val.replace(reg, "")
				    // // 保证第一位只能是数字，不能是点
				    val = val.replace(/^\./g, "");
				    // // 小数只能出现1位
				    val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
				    // // 小数点后面保留2位
				    val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
				    this.$nextTick(() => {
				        this.money = val;
				    })
			},
			/*
			 * 用户充值
			 */
			submitSub: Debounce(function(e) {
				let that = this
				let value = e.detail.value.number ? e.detail.value.number : that.numberPic;
				uni.showLoading({
						title: '正在支付',
					})
					let money = parseFloat(that.money);
					if (that.rechar_id == 0) {
						if (Number.isNaN(money)) {
							return that.$util.Tips({
								title: '充值金额必须为数字'
							});
						}
						if (money <= 0) {
							return that.$util.Tips({
								title: '充值金额不能为0'
							});
						}
						if (money > 50000) {
							return that.$util.Tips({
								title: '充值金额最大值为50000'
							});
						}
					} else {
						money = that.numberPic
					}
					if (that.payType == 'alipay') {
						// #ifdef H5
						that.payChannel = 'alipay';
						// #endif
						// #ifdef APP-PLUS
						that.payChannel = 'alipayApp';
						// #endif
					} else {
						// #ifdef H5
						that.payChannel = that.$wechat.isWeixin() ? "public" : "h5";
						// #endif
						// #ifdef MP
						that.payChannel = "mini";
						// #endif
						// #ifdef APP-PLUS
						cpayChannel = that.systemPlatform === 'ios' ? 'weixinAppIos' : 'weixinAppAndroid';
						// #endif
					}
					rechargeCreateApi({
						payChannel: that.payChannel,
						price: money,
						payType: that.payType,
						groupDataId: that.rechar_id
					}).then(res => {
						uni.hideLoading();
						that.pay(res);
					}).catch(err => {
						uni.hideLoading();
						that.$util.Tips({
							title: err
						})
						setTimeout(()=>{
							uni.navigateTo({
								url: '/pages/users/pay_success/index?status=0'
							})
						},1000)
					});
			}),
			pay(res) {
				let that = this
				switch (that.payType) {
					case 'weixin':
						// #ifdef APP-PLUS
						let jsConfig = res.data.jsConfig;
						uni.requestPayment({
							provider: 'wxpay',
							orderInfo: {
								"appid": jsConfig.appId, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
								"noncestr": jsConfig.nonceStr, // 随机字符串
								"package": "Sign=WXPay", // 固定值
								"partnerid": jsConfig.partnerid, // 微信支付商户号
								"prepayid": jsConfig.packages, // 统一下单订单号
								"timestamp": Number(jsConfig.timeStamp), // 时间戳（单位：秒）
								"sign": that.systemPlatform === 'ios' ? 'MD5' : jsConfig.paySign // 签名，这里用的 MD5 签名
							}, //微信、支付宝订单数据 【注意微信的订单信息，键值应该全部是小写，不能采用驼峰命名】
							success: function(res) {
								that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								}, {
									tab: 3,
								});
								setTimeout(()=>{
									uni.navigateTo({
										url: '/pages/users/pay_success/index?status=1'
									})
								},1000)
							},
							fail: function(err) {
								that.$util.Tips({
									title: '支付失败'
								});
								setTimeout(()=>{
									uni.navigateTo({
										url: '/pages/users/pay_success/index?status=0'
									})
								},1000)
							},
							complete: function(res) {
								if (res.errMsg == 'requestPayment:cancel') return that.$util.Tips({
									title: '取消支付'
								});
							}
						})
						// #endif
			
						// #ifdef MP
						let jsConfig = res.data.jsConfig;
						uni.requestPayment({
							timeStamp: jsConfig.timeStamp,
							nonceStr: jsConfig.nonceStr,
							package: jsConfig.packages,
							signType: jsConfig.signType,
							paySign: jsConfig.paySign,
							success: function(res) {
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								}, {
									tab: 3,
								});
							},
							fail: function(err) {
								return that.$util.Tips({
									title: '支付失败'
								});
							},
							complete: function(res) {
								if (res.errMsg == 'requestPayment:cancel') return that.$util.Tips({
									title: '取消支付'
								});
							}
						})
						// #endif
						// #ifdef H5
						let jsConfig = res.data.jsConfig;
						let orderNo = res.data.orderNo;
						let data = {
							timestamp: jsConfig.timeStamp,
							nonceStr: jsConfig.nonceStr,
							package: jsConfig.packages,
							signType: jsConfig.signType,
							paySign: jsConfig.paySign
						};
						if (that.payChannel == "h5") {
							uni.hideLoading();
							that.$util.Tips({
								title: '支付成功'
							}, {
								tab: 3,
							});
							setTimeout(() => {
								location.href = jsConfig.mwebUrl;
							}, 100)
						} else {
							that.$wechat.pay(data)
								.finally(() => {
									return that.$util.Tips({
										title: '支付成功',
										icon: 'success'
									}, {
										tab: 3,
									});
								})
								.catch(function(err) {
									return that.$util.Tips({
										title: '支付失败'
									});
								});
						}
						// #endif
						break;
					case 'alipay':
						// alipayFull
						// #ifdef APP-PLUS
						let alipayRequest = res.data.alipayRequest;
						uni.requestPayment({
							provider: 'alipay',
							orderInfo: alipayRequest,
							success: (e) => {
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								}, {
									tab: 5,
									url: '/pages/users/user_money/index'
								});
							},
							fail: (e) => {
								return that.$util.Tips({
									title: '支付失败'
								});
							},
							complete: () => {
								uni.hideLoading();
							},
						});
						// #endif
						// #ifdef H5
						if (that.$wechat.isWeixin()) {
							uni.redirectTo({
								url: `/pages/users/alipay_invoke/index?price=${money}&rechar_id=${that.rechar_id}&type=users`
							});
						} else {
							that.formContent = res.data.alipayRequest;
							that.$nextTick(() => {
								document.forms['punchout_form'].submit();
							})
						}
						// #endif
						break;
				}
			},
			/**
			 * 支付宝充值结果查询
			 */
			alipayQueryPay() {
				uni.showLoading({
					title: '查询中...'
				});
				alipayQueryPayResult(this.orderId).then(res => {
					this.userDalance();
					return this.$util.Tips({
						title: '充值成功'
					});
					uni.hideLoading();
				}).catch(err => {
					uni.hideLoading();
					return this.$util.Tips({
						title: err
					});
				})
			},
			userDalance() {
				getMyAccountApi().then(res => {
					this.statistics = res.data;
				})
			},
			openSubscribe: function(page) {
				uni.navigateTo({
					url: page,
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.container_money_bg{
		width: 100%;
		height: 292rpx;
		background-image: linear-gradient(#B8FE4E,#F2F8E0);
		position: absolute;
		left: 0;
		top: 0;
	}
	.container_money_account{
		padding: 0 20rpx;
		position: relative;
	}
	.now_amount{
		position: relative;
		margin-top: 24rpx;
		width: 100%;
		height: 292rpx;
		img{
			width: 100%;
			height: 100%;
		}
		.now_amount_content{
			position: absolute;
			left: 36rpx;
			top: 54rpx;
		}
	}
	.container_money_log{
		padding: 0 20rpx;
	}
	.container_money_log .nav {
		// background-color: #fff;
		// height: 90rpx;
		width: 100%;
		// line-height: 90rpx;
	}
	.container_money_log .nav .item {
		flex: 1;
		text-align: center;
		font-size: 30rpx;
		color: #666666;
		position: relative;
		padding: 30rpx 0 40rpx 0;
		.line{
			position: absolute;
			left: 50%;
			bottom: 22rpx;
			margin-left: -25rpx;
			width: 50rpx;
			height: 6rpx;
			border-radius: 20rpx;
			background-color: #BDFD5B;
		}
	}

	.container_money_log .nav .item.on {
		font-weight: 600;
		color: #333333;
	}
	.scroll-Y {
			height: 56vh;
			background-color: #fff;
		}
	.container_money_footer{
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 24rpx 24rpx 60rpx 24rpx;
		z-index: 10;
		background-color: #F5F5F5;
		.but{
			color: #222222;
			font-size: 30rpx;
			width: 100%;
			height: 86rpx;
			border-radius: 20rpx;
			margin: 50rpx auto 0 auto;
			background-color: #BDFD5B;
			line-height: 86rpx;
		}
	}
	.product-window {
		position: fixed;
		bottom: 0;
		width: 100%;
		left: 0;
		background-color: #fff;
		z-index: 377;
		border-radius: 24rpx 24rpx 0 0;
		padding-bottom: 40rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 40rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
		transform: translate3d(0, 100%, 0);
		transition: all .2s cubic-bezier(0, 0, .25, 1);
		max-height: 80vh;
		overflow: hidden;
	}
	
	.product-window.on {
		transform: translate3d(0, 0, 0);
	}
	// 弹框头部
	.popup-header {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx 0;
		position: relative;
	
		.popup-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
		}
	
		.close-btn {
			position: absolute;
			right: 20rpx;
			top: 30rpx;
			width: 40rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 1px solid #999999;
			border-radius: 50%;
			.iconfont {
				font-size: 20rpx;
				color: #999999;
			}
		}
	}
	.payment {
		position: relative;
		// top: -60rpx;
		width: 100%;
		background-color: #fff;
		border-radius: 10rpx;
		// padding-top: 25rpx;
		border-top-right-radius: 14rpx;
		border-top-left-radius: 14rpx;
	}
	
	.payment .nav {
		height: 75rpx;
		line-height: 75rpx;
		padding: 0 100rpx;
	}
	
	.payment .nav .item {
		font-size: 30rpx;
		color: #333;
	}
	
	.payment .nav .item.on {
		font-weight: bold;
		@include tab_border_bottom(theme);
	}
	
	.payment .input {
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom: 1px dashed #dddddd;
		margin: 60rpx auto 0 auto;
		padding-bottom: 20rpx;
		font-size: 56rpx;
		color: #333333;
		flex-wrap: nowrap;
	
	}
	
	.payment .input text {
		padding-left: 106rpx;
	}
	
	.payment .input input {
		padding-right: 106rpx;
		width: 310rpx;
		height: 94rpx;
		text-align: center;
		font-size: 70rpx;
	}
	
	.payment .placeholder {
		color: #fff;
		height: 100%;
		line-height: 94rpx;
	}
	
	.payment .tip {
		font-size: 26rpx;
		color: #888888;
		padding: 0 20rpx;
		// margin-top: 25rpx;
	}
	
	.payment .but {
		// color: #fff;
		font-size: 30rpx;
		width: 700rpx;
		height: 86rpx;
		border-radius: 43rpx;
		margin: 50rpx auto 0 auto;
		background-color: #BDFD5B;
		line-height: 86rpx;
	}
	
	.payment-top {
		width: 100%;
		height: 278rpx;
		@include main_bg_color(theme);
	
		.name1 {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			margin-top: -38rpx;
			margin-bottom: 30rpx;
		}
	
		.pic {
			font-size: 32rpx;
			color: #fff;
		}
	
		.pic-font {
			font-size: 78rpx;
			color: #fff;
		}
	}
	.picList {
		display: flex;
		flex-wrap: wrap;
		// margin: 30rpx 0;
	
		.pic-box {
			width: 32%;
			height: auto;
			border-radius: 20rpx;
			margin-top: 21rpx;
			padding: 20rpx 0;
			margin-right: 12rpx;
	
			&:nth-child(3n) {
				margin-right: 0;
			}
		}
	
		.pic-box-color {
			background-color: #f4f4f4;
			color: #656565;
		}
	
		.pic-number {
			font-size: 22rpx;
		}
	
		.pic-number-pic {
			font-size: 38rpx;
			margin-right: 10rpx;
			text-align: center;
		}
	
	}
	
	.pic-box-color-active {
		background-color: #BDFD5B !important;
	}
	
	.tips-box {
		.tips {
			font-size: 28rpx;
			color: #333333;
			font-weight: 800;
			margin-bottom: 14rpx;
			margin-top: 20rpx;
		}
	
		.tips-samll {
			font-size: 24rpx;
			color: #333333;
			margin-bottom: 14rpx;
		}
	
		.tip-box {
			margin-top: 30rpx;
		}
	}
	
	.tips-title {
		margin-top: 20rpx;
		font-size: 24rpx;
		color: #333;
	}
	
	.wrapper .item textarea {
		background-color: #f9f9f9;
		width: auto !important;
		height: 140rpx;
		border-radius: 14rpx;
		margin-top: 30rpx;
		padding: 15rpx;
		box-sizing: border-box;
		font-weight: 400;
	}
	
	.px-30 {
		padding-left: 30rpx;
		padding-rigt: 30rpx;
	}
	
	.wrapper .item .placeholder {
		color: #ccc;
	}
	
	.wrapper .item .list {
		margin-top: 35rpx;
	}
	
	.wrapper .item .list .payItem {
		border: 1px solid #eee;
		border-radius: 14rpx;
		height: 86rpx;
		width: 95%;
		box-sizing: border-box;
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #282828;
	}
	
	.wrapper .item .list .payItem.on {
		// border-color: #fc5445;
		@include coupons_border_color(theme);
		color: $theme-color;
	}
	
	.payItem{
		.name {
			width: 50%;
			text-align: center;
			border-right: 1px solid #eee;
		}
		
		.name .iconfont {
			width: 44rpx;
			height: 44rpx;
			border-radius: 50%;
			text-align: center;
			line-height: 44rpx;
			background-color: #fe960f;
			color: #fff;
			font-size: 30rpx;
			margin-right: 15rpx;
		}
		
		.name .iconfont.icon-weixin2 {
			background-color: #41b035;
		}
		
		.name .iconfont.icon-zhifubao {
			background-color: #00AAEA;
		}
	}
	
	.payItem .tip {
		width: 49%;
		text-align: center;
		font-size: 26rpx;
		color: #aaa;
	}
	// 遮罩层
	.mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 376;
	}
	// .my-account {
	// 	padding-bottom: 10rpx;
	// 	padding-bottom: calc(10rpx + constant(safe-area-inset-bottom) / 3);
	// 	padding-bottom: calc(10rpx + env(safe-area-inset-bottom) / 3);
	// }
	// .my-account .wrapper {
	// 	background-color: #fff;
	// 	padding: 32rpx 0 15rpx 0;
	// }

	// .my-account .wrapper .header {
	// 	width: 690rpx;
	// 	height: 330rpx;
	// 	@include main_bg_color(theme);
	// 	border-radius: 16rpx;
	// 	margin: 0 auto;
	// 	box-sizing: border-box;
	// 	color: rgba(255, 255, 255, 0.6);
	// 	font-size: 24rpx;
	// }

	// .my-account .wrapper .header .headerCon {
	// 	background-image: url('data:image/png;base64,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');
	// 	background-repeat: no-repeat;
	// 	background-size: 100%;
	// 	height: 100%;
	// 	width: 100%;
	// 	padding: 36rpx 0 29rpx 0;
	// 	box-sizing: border-box;
	// }

	// .my-account .wrapper .header .headerCon .account {
	// 	padding: 0 35rpx;
	// }

	// .my-account .wrapper .header .headerCon .account .assets .money {
	// 	font-size: 72rpx;
	// 	color: #fff;
	// 	font-family: 'Guildford Pro';
	// }

	// .my-account .wrapper .header .headerCon .account .recharge {
	// 	font-size: 28rpx;
	// 	width: 150rpx;
	// 	height: 54rpx;
	// 	border-radius: 27rpx;
	// 	background-color: #fff9f8;
	// 	text-align: center;
	// 	line-height: 54rpx;
	// }

	// .font_color {
	// 	@include main_color(theme);
	// }

	// .icon_txt {
	// 	font-size: 43rpx;
	// 	@include main_color(theme);
	// }

	// .my-account .wrapper .header .headerCon .cumulative {
	// 	margin-top: 46rpx;
	// }

	// .my-account .wrapper .header .headerCon .cumulative .item {
	// 	flex: 1;
	// 	padding-left: 35rpx;
	// }

	// .my-account .wrapper .header .headerCon .cumulative .item .money {
	// 	font-size: 48rpx;
	// 	font-family: 'Guildford Pro';
	// 	color: #fff;
	// 	margin-top: 6rpx;
	// }

	// .my-account .wrapper .nav {
	// 	height: 155rpx;
	// 	border-bottom: 1rpx solid #f5f5f5;
	// }

	// .my-account .wrapper .nav .item {
	// 	flex: 1;
	// 	text-align: center;
	// 	font-size: 26rpx;
	// 	color: #999;
	// }

	// .my-account .wrapper .nav .item .pictrue {
	// 	width: 44rpx;
	// 	height: 44rpx;
	// 	margin: 0 auto;
	// 	margin-bottom: 20rpx;
	// }

	// .my-account .wrapper .nav .item .pictrue image {
	// 	width: 100%;
	// 	height: 100%;
	// }

	// .my-account .wrapper .advert {
	// 	padding: 0 30rpx;
	// 	margin-top: 30rpx;
	// }

	// .my-account .wrapper .advert .item {
	// 	background-color: #fff6d1;
	// 	width: 332rpx;
	// 	height: 118rpx;
	// 	border-radius: 10rpx;
	// 	padding: 0 27rpx 0 25rpx;
	// 	box-sizing: border-box;
	// 	font-size: 24rpx;
	// 	color: #e44609;
	// }

	// .my-account .wrapper .advert .item.on {
	// 	background-color: #fff3f3;
	// 	color: #e96868;
	// }

	// .my-account .wrapper .advert .item .pictrue {
	// 	width: 78rpx;
	// 	height: 78rpx;
	// }

	// .my-account .wrapper .advert .item .pictrue image {
	// 	width: 100%;
	// 	height: 100%;
	// }

	// .my-account .wrapper .advert .item .text .name {
	// 	font-size: 30rpx;
	// 	font-weight: bold;
	// 	color: #f33c2b;
	// 	margin-bottom: 7rpx;
	// }

	// .my-account .wrapper .advert .item.on .text .name {
	// 	color: #f64051;
	// }

	// .my-account .wrapper .list {
	// 	padding: 0 30rpx;
	// }

	// .my-account .wrapper .list .item {
	// 	margin-top: 44rpx;
	// }

	// .my-account .wrapper .list .item .picTxt .iconfont {
	// 	width: 82rpx;
	// 	height: 82rpx;
	// 	border-radius: 50%;
	// 	background-image: linear-gradient(to right, #ff9389 0%, #f9776b 100%);
	// 	text-align: center;
	// 	line-height: 82rpx;
	// 	color: #fff;
	// 	font-size: 40rpx;
	// }

	// .my-account .wrapper .list .item .picTxt .iconfont.yellow {
	// 	background-image: linear-gradient(to right, #ffccaa 0%, #fea060 100%);
	// }

	// .my-account .wrapper .list .item .picTxt .iconfont.green {
	// 	background-image: linear-gradient(to right, #a1d67c 0%, #9dd074 100%);
	// }

	// .my-account .wrapper .list .item .picTxt {
	// 	width: 428rpx;
	// 	font-size: 30rpx;
	// 	color: #282828;
	// }

	// .my-account .wrapper .list .item .picTxt .text {
	// 	width: 317rpx;
	// }

	// .my-account .wrapper .list .item .picTxt .text .infor {
	// 	font-size: 24rpx;
	// 	color: #999;
	// 	margin-top: 5rpx;
	// }

	// .my-account .wrapper .list .item .bnt {
	// 	font-size: 26rpx;
	// 	color: #282828;
	// 	width: 156rpx;
	// 	height: 52rpx;
	// 	border: 1rpx solid #ddd;
	// 	border-radius: 26rpx;
	// 	text-align: center;
	// 	line-height: 52rpx;
	// }

	// .my-account .wrapper .list .item .bnt.end {
	// 	font-size: 26rpx;
	// 	color: #aaa;
	// 	background-color: #f2f2f2;
	// 	border-color: #f2f2f2;
	// }
</style>
