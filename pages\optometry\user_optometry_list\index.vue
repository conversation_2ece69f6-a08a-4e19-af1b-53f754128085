<template>
	<view :data-theme="theme">
		<scroll-view scroll-y="true" @scrolltolower="scrolltolower" class="optometry-container">
			<!-- 验光单列表 -->
			<radio-group class="optometry-list" @change="radioChange" v-if="optometryList.length">
				<view class="optometry-item" v-for="(item,index) in optometryList" :key="index">
					<!-- 验光单信息行 -->
					<view class="optometry-info" @click="checkOptometry(index)">
						<view class="optometry-name">姓名：{{item.name}}</view>
						<view class="optometry-arrow">
							<text class="iconfont icon-you"></text>
						</view>
					</view>

					<!-- 操作行 -->
					<view class="optometry-actions">
						<!-- 默认验光单选择 -->
						<view class="default-selection">
							<!-- #ifndef MP -->
							<radio class="radio-checkbox" :value="index.toString()" :checked="item.isDefault">
								<text class="radio-text">设置为默认验光单</text>
							</radio>
							<!-- #endif -->
							<!-- #ifdef MP -->
							<radio class="radio-checkbox" :value="index" :checked="item.isDefault">
								<text class="radio-text">设置为默认验光单</text>
							</radio>
							<!-- #endif -->
						</view>

						<!-- 编辑按钮 -->
						<view class="edit-button" @click="editOptometry(item.id)">
							<text class="iconfont icon-bianji"></text>
							<text class="edit-text">编辑</text>
						</view>
					</view>
				</view>
			</radio-group>

			<!-- 空状态 -->
			<view class="empty-state" v-if="optometryList.length == 0 && !loading">
				<emptyPage src="/pages/aastatictoT/static/images/noAddress.png" title="暂无验光单哦~"></emptyPage>
			</view>

			<!-- 加载状态 -->
			<view class="loading-state" v-if="optometryList.length && loading">
				<text class="loading-icon iconfont icon-jiazai"></text>
				<text class="loading-text">{{loadTitle}}</text>
			</view>
		</scroll-view>

		<!-- 底部添加按钮 -->
		<view class="bottom-container">
			<view class="add-button" @click="addOptometry">
				添加验光单
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getOptometryList,
		setOptometryDefault
	} from '@/api/user.js';
	import emptyPage from '@/components/emptyPage.vue'
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		components: {
			emptyPage,
		},
		data() {
			return {
				isCheck: false,
				optometryList: [],
				loading: false,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 7,
				theme:app.globalData.theme
			};
		},
		computed: mapGetters(['isLogin']),
		watch:{
			isLogin:{
				handler:function(newV,oldV){
					if(newV){
						this.getOptometryList(true);
					}
				},
				deep:true
			}
		},
		onLoad(options) {
			this.isCheck = options.isCheck || false;
			if (this.isLogin) {
				this.getOptometryList(true);
			} else {
				toLogin();
			}
		},
		onShow: function() {
			let that = this;
			that.getOptometryList(true);
		},
		methods: {
			checkOptometry: function(index) {
				if (this.isCheck) {
					let optometry = this.optometryList[index];
					uni.$emit('checkoptometry', optometry);
					uni.navigateBack({
						delta: 1
					});
				}
			},
			/**
			 * 获取验光单列表
			 */
			getOptometryList: function(isPage) {
				let that = this;
				if (isPage) {
					that.loadend = false;
					that.page = 1;
					that.$set(that, 'optometryList', []);
				};
				if (that.loading) return;
				if (that.loadend) return;
				that.loading = true;
				that.loadTitle = '';
				getOptometryList({
					page: that.page,
					limit: that.limit
				}).then(res => {
					let list = res.data.list.map(item => {
						item.data = JSON.parse(item.data);
						item.nearShow = false;
						return item;
					})
					let loadend = list.length < that.limit;
					
					that.optometryList = that.$util.SplitArray(list, that.optometryList);
					that.$set(that, 'optometryList', that.optometryList);

					that.loadend = loadend;
					that.page = that.page + 1;
					that.loading = false;
					that.loadTitle = loadend ? '已加载全部' : '加载更多';
				}).catch(err => {
					that.loading = false;
					that.loadTitle = '加载更多';
				});
			},
			/**
			 * 设置默认验光单
			 */
			radioChange: function(e) {
				let index = parseInt(e.detail.value),
					that = this;
				let optometry = this.optometryList[index];
				if (optometry == undefined) {
					return that.$util.Tips({
						title: '您设置的默认验光单不存在!'
					});
				}
				setOptometryDefault(optometry.id).then(res => {
					for (let i = 0, len = that.optometryList.length; i < len; i++) {
						if (i == index) that.optometryList[i].isDefault = true;
						else that.optometryList[i].isDefault = false;
					}
					that.$util.Tips({
						title: '设置成功',
						icon: 'success'
					}, function() {
						that.$set(that, 'optometryList', that.optometryList);
					});
				}).catch(err => {
					return that.$util.Tips({
						title: err
					});
				});
			},
			/**
			 * 编辑验光单
			 */
			editOptometry: function(id) {
				uni.navigateTo({
					url: '/pages/optometry/user_optometry/index?id=' + id
				})
			},

			/**
			 * 新增验光单
			 */
			addOptometry: function() {
				uni.navigateTo({
					url: '/pages/optometry/user_optometry/index'
				})
			},
			scrolltolower() {
				this.getOptometryList();
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f5f5;
	}

	.optometry-container {
		height: 100vh;
		padding: 0 32rpx;
		padding-bottom: 160rpx;
		box-sizing: border-box;
	}

	// 验光单列表
	.optometry-list {
		padding-top: 32rpx;

		.optometry-item {
			background-color: #ffffff;
			border-radius: 16rpx;
			margin-bottom: 24rpx;
			overflow: hidden;

			// 验光单信息行
			.optometry-info {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 32rpx 40rpx;
				border-bottom: 1rpx solid #f0f0f0;

				.optometry-name {
					font-size: 32rpx;
					color: #333333;
					font-weight: 500;
				}

				.optometry-arrow {
					.iconfont {
						font-size: 28rpx;
						color: #999999;
					}
				}
			}

			// 操作行
			.optometry-actions {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 32rpx 40rpx;

				// 默认验光单选择
				.default-selection {
					display: flex;
					align-items: center;

					.radio-checkbox {
						display: flex;
						align-items: center;

						.radio-text {
							margin-left: 16rpx;
							font-size: 28rpx;
							color: #666666;
						}
					}
				}

				// 编辑按钮
				.edit-button {
					display: flex;
					align-items: center;
					color: #666666;

					.iconfont {
						font-size: 28rpx;
						margin-right: 8rpx;
					}

					.edit-text {
						font-size: 28rpx;
					}
				}
			}
		}
	}

	// 空状态
	.empty-state {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 60vh;

		/deep/ .empty-box {
			margin: 0 !important;
			padding: 0 !important;
		}
	}

	// 加载状态
	.loading-state {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;

		.loading-icon {
			font-size: 32rpx;
			color: #999999;
			margin-right: 16rpx;
			animation: rotate 1s linear infinite;
		}

		.loading-text {
			font-size: 28rpx;
			color: #999999;
		}
	}

	// 底部容器
	.bottom-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 32rpx;
		padding-bottom: calc(32rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
		background-color: #ffffff;
		border-top: 1rpx solid #f0f0f0;

		.add-button {
			width: 100%;
			height: 88rpx;
			background-color: #BDFD5B;
			border-radius: 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			color: #222222;
			font-weight: 500;
			transition: all 0.2s;

			&:active {
				background-color: #8AEF8A;
			}
		}
	}

	// 复选框样式
	/deep/ radio .wx-radio-input {
		width: 36rpx !important;
		height: 36rpx !important;
		border-radius: 4rpx !important;
		border: 2rpx solid #d9d9d9 !important;
	}

	/deep/ radio .wx-radio-input.wx-radio-input-checked {
		background-color: #BDFD5B !important;
		border-color: #BDFD5B !important;
	}

	/deep/ radio .wx-radio-input.wx-radio-input-checked::before {
		content: '✓';
		font-size: 24rpx;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		line-height: 1;
	}

	/deep/ radio .uni-radio-input {
		width: 36rpx !important;
		height: 36rpx !important;
		border-radius: 4rpx !important;
		border: 2rpx solid #d9d9d9 !important;
	}

	/deep/ radio .uni-radio-input.uni-radio-input-checked {
		background-color: #BDFD5B !important;
		border-color: #BDFD5B !important;
	}

	/deep/ radio .uni-radio-input.uni-radio-input-checked::before {
		content: '✓';
		font-size: 24rpx;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		line-height: 1;
	}

	// 动画
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
