<template>
	<view class="page" :data-theme="theme">
		<view class="system-height" :style="{height:statusBarHeight}"></view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- Logo区域 -->
			<view class="logo-section">
				<view class="logo-container">
					<image src="/static/images/icon_logo.png" class="logo-image" mode="aspectFit"></image>
				</view>
				<view class="title-text">乐腾生态平台</view>
				<view class="subtitle-text">清晰视界</view>
			</view>

			<!-- 登录卡片区域 -->
			<view class="login-card" v-if="!showMobileForm">
				<view class="login-buttons">
					<!-- 手机登录按钮 -->
					<button hover-class="none" @click="mobileLogin" class="phone-login-btn">手机号验证登录</button>

					<!-- 微信登录按钮 -->
					<!-- #ifdef H5 -->
					<button hover-class="none" @click="wechatLogin" class="wechat-login-btn">快捷登录</button>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<button hover-class="none" @click="getUserProfile" class="wechat-login-btn">快捷登录</button>
					<!-- #endif -->
				</view>

				<!-- 用户协议 -->
				<view class="agreement-section">
					<view class="agreement-checkbox" @click="toggleAgreement">
						<image v-if="isAgreed" src="/static/images/icon_circle.png" class="checkbox-icon"></image>
						<view v-else class="checkbox-unchecked"></view>
					</view>
					<view class="agreement-text">
						<text>我已阅读并同意</text>
						<text class="agreement-link" @click="goToUserAgreement">《用户协议》</text>
						<text class="agreement-link" @click="goToPrivacyPolicy">《隐私政策》</text>
					</view>
				</view>
			</view>

			<!-- 手机登录表单 -->
			<view class="mobile-form-card" v-if="showMobileForm">
				<view class="form-group">
					<view class="input-row">
						<input
							v-model="mobilePhone"
							placeholder="请输入手机号"
							type="number"
							maxlength="11"
							class="phone-input"
						/>
						<button
							@click="sendVerifyCode"
							class="verify-btn"
							:disabled="disabled"
						>
							{{ text }}
						</button>
					</view>
					<input
						v-model="verifyCode"
						placeholder="请输入验证码"
						type="number"
						maxlength="6"
						class="verify-input"
					/>
					<button @click="submitLogin" class="login-submit-btn">登录</button>
				</view>

				<!-- 返回按钮 -->
				<view class="back-btn-section">
					<button hover-class="none" @click="backToLoginOptions" class="back-btn">返回</button>
				</view>

				<!-- 用户协议 -->
				<view class="agreement-section">
					<view class="agreement-checkbox" @click="toggleAgreement">
						<image v-if="isAgreed" src="/static/images/icon_circle.png" class="checkbox-icon"></image>
						<view v-else class="checkbox-unchecked"></view>
					</view>
					<view class="agreement-text">
						<text>我已阅读并同意</text>
						<text class="agreement-link" @click="goToUserAgreement">《用户协议》</text>
						<text class="agreement-link" @click="goToPrivacyPolicy">《隐私政策》</text>
					</view>
				</view>
			</view>
		</view>

		<block v-if="isUp">
			<mobileLogin :isUp="isUp" @close="maskClose" :authKey="authKey" @wechatPhone="wechatPhone"></mobileLogin>
		</block>
		<atModel v-if="isPhoneBox" :userPhoneType="true" :isPhoneBox="isPhoneBox" :authKey="authKey"
			:content="getPhoneContent" @closeModel="bindPhoneClose" @confirmModel="confirmModel"></atModel>
	</view>
</template>

<script>
	const app = getApp();
	let statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
	import mobileLogin from '../components/login_mobile/index.vue'
	import atModel from '@/components/accredit/index.vue'
	import {
		mapGetters
	} from "vuex";
	import {
		getLogo,
		getUserPhone
	} from '@/api/public';
	import {
		LOGO_URL,
		EXPIRES_TIME,
		USER_INFO,
		STATE_R_KEY
	} from '@/config/cache';
	import {
		HTTP_REQUEST_URL
	} from '@/config/app';
	import {
		spread,
		loginMobile,
		registerVerify
	} from '@/api/user.js'
	import sendVerifyCode from "@/mixins/SendVerifyCode";
	import {
		Debounce
	} from '@/utils/validate.js'
	import Routine from '@/libs/routine';
	import wechat from "@/libs/wechat";
	import {
		autoFollow
	} from '@/libs/merchant';
	export default {
		name: 'wechat_login',
		mixins: [sendVerifyCode],
		computed: mapGetters(['userInfo', 'isLogin', 'globalData']),
		data() {
			return {
				isUp: false,
				phone: '',
				statusBarHeight: statusBarHeight,
				isHome: false,
				isPhoneBox: false,
				logoUrl: '',
				code: '',
				authKey: '',
				options: '',
				userInfo: {},
				codeNum: 0,
				theme: app.globalData.theme,
				getPhoneContent: '申请获取您的手机号用于注册，完成后可使用商城更多功能',
				isAgreed: false, // 用户协议勾选状态
				showMobileForm: false, // 是否显示手机登录表单
				mobilePhone: '', // 手机号
				verifyCode: '' // 验证码
			}
		},
		computed: {
			...mapGetters(['currentMerId']),
		},
		components: {
			mobileLogin,
			atModel
		},

		onLoad(options) {
			getLogo().then(res => {
				this.logoUrl = res.data.logoUrl
			})
			let that = this
			// #ifdef H5
			document.body.addEventListener("focusout", () => {
				setTimeout(() => {
					const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop ||
						0;
					window.scrollTo(0, Math.max(scrollHeight - 1, 0));
				}, 100);
			});
			const {
				code,
				state,
				scope
			} = options;
			this.options = options
			// 获取确认授权code
			this.code = code || ''
			//if(!code) location.replace(decodeURIComponent(decodeURIComponent(option.query.back_url)));
			if (code && this.options.scope !== 'snsapi_base') {
				let spread = app.globalData.spread ? app.globalData.spread : 0;
				//公众号授权登录回调 wechatAuth(code, Cache.get("spread"), loginType)
				wechat.auth(code, spread).then(res => {
					if (res.type === 'register') {
						this.authKey = res.key;
						this.isUp = true
					}
					if (res.type === 'login') {
						this.$store.commit('LOGIN', {
							token: res.token
						});
						this.$store.commit("SETUID", res.id);
						this.getUserInfo();
						//this.wechatPhone();
						//location.replace(decodeURIComponent(decodeURIComponent(option.query.back_url)));
					}
				}).catch(error => {});
			}
			// #endif
			let pages = getCurrentPages();
			// let prePage = pages[pages.length - 2];
			// if (prePage.route == 'pages/order_addcart/order_addcart') {
			// 	this.isHome = true
			// } else {
			// 	this.isHome = false
			// }

		},
		methods: {
			// 切换用户协议勾选状态
			toggleAgreement() {
				this.isAgreed = !this.isAgreed;
			},
			// 跳转到用户协议页面
			goToUserAgreement() {
				uni.navigateTo({
					url: '/pages/users/agreement_info/index?from=userinfo'
				});
			},
			// 跳转到隐私政策页面
			goToPrivacyPolicy() {
				uni.navigateTo({
					url: '/pages/users/agreement_info/index?from=userprivacyinfo'
				});
			},
			// 手机登录
			mobileLogin() {
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					});
					return;
				}
				this.showMobileForm = true;
			},
			// 返回到登录选项
			backToLoginOptions() {
				this.showMobileForm = false;
				// 清空表单数据
				this.mobilePhone = '';
				this.verifyCode = '';
			},
			// 发送验证码
			sendVerifyCode: Debounce(function() {
				if (!this.mobilePhone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}
				if (!/^1[3-9]\d{9}$/.test(this.mobilePhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					});
					return;
				}

				// 调用发送验证码API
				registerVerify(this.mobilePhone)
					.then(res => {
						uni.showToast({
							title: res.message || '验证码已发送',
							icon: 'success'
						});
						// 开始倒计时
						this.sendCode();
					})
					.catch(err => {
						uni.showToast({
							title: err || '发送失败，请重试',
							icon: 'none'
						});
					});
			}),
			// 提交登录
			submitLogin: Debounce(function() {
				if (!this.mobilePhone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}
				if (!/^1[3-9]\d{9}$/.test(this.mobilePhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				if (!this.verifyCode) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none'
					});
					return;
				}
				if (!/^[\w\d]+$/i.test(this.verifyCode)) {
					uni.showToast({
						title: '请输入正确的验证码',
						icon: 'none'
					});
					return;
				}
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					});
					return;
				}

				uni.showLoading({
					title: '登录中...'
				});

				// 调用登录API
				loginMobile({
					phone: this.mobilePhone,
					captcha: this.verifyCode,
					spreadPid: this.$Cache.get("spread")
				})
				.then(res => {
					let data = res.data;
					this.$store.commit("LOGIN", {
						'token': res.data.token
					});
					uni.hideLoading();
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});
					// 获取用户信息并跳转
					this.getUserInfo(data);
				})
				.catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err || '登录失败，请重试',
						icon: 'none'
					});
				});
			}),
			// 获取用户信息并跳转
			getUserInfo(data) {
				this.$store.commit("SETUID", data.id);
				let backUrl = this.$Cache.get("login_back_url") || "/pages/index/index";
				if (backUrl.indexOf('/pages/users/wechat_login/index') !== -1) {
					backUrl = '/pages/index/index';
				}
				setTimeout(() => {
					uni.reLaunch({
						url: backUrl
					});
				}, 1500);
			},
			back() {
				uni.navigateBack();
			},
			home() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			modelCancel() {
				this.isPhoneBox = false;
			},
			// 弹窗关闭
			maskClose() {
				// this.isUp = false  //点击模态框会关闭登录弹框，防止用户误触而关闭
			},
			bindPhoneClose(data) {
				if (data.isStatus) {
					this.isPhoneBox = false
					this.$util.Tips({
						title: '登录成功',
						icon: 'success'
					}, {
						tab: 3
					})
				} else {
					this.isPhoneBox = false
				}

			},
			// #ifdef MP
			/**
			 * 登录成功之后回调
			 */
			getUserInfo() {
				this.$util.Tips({
					title: '登录成功',
					icon: 'success'
				}, {
					tab: 3
				})
			},
			getUserProfile() {
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					});
					return;
				}
				console.log('getUserProfile');
				let self = this;
				uni.showLoading({
					title: '正在登录中'
				});
				const hostSDKVersion = uni.getSystemInfoSync().hostSDKVersion; //小程序基础库版本号
				if (Routine.compareVersion(hostSDKVersion, '2.21.2') >= 0) {
					Routine.getCode()
						.then(code => {
							let userInfo = {
								code: code,
								spreadPid: app.globalData.spread, //获取推广人ID
								type: 'routine'
							};
							self.getWxUser(userInfo);
						})
						.catch(res => {
							uni.hideLoading();
						});
				} else {
					Routine.getUserProfile().then(res => {
							Routine.getCode()
								.then(code => {
									let userInfo = {
										code: code,
										spreadPid: app.globalData.spread, //获取推广人ID
										type: 'routine'
									};
									self.getWxUser(userInfo);
								})
								.catch(res => {
									uni.hideLoading();
								});
						})
						.catch(res => {
							uni.hideLoading();
						});
				}
			},

			getWxUser(userInfo) {
				let self = this;
				Routine.authUserInfo(userInfo)
					.then(res => {
						self.authKey = res.data.key;
						if (res.data.type === 'register') {
							uni.hideLoading();
							self.isPhoneBox = true;
						}
						if (res.data.type === 'login') {
							uni.hideLoading();
							self.getUserInfo();

							if (app.globalData.spread) {
								spread(app.globalData.spread).then(res => {});
							}
							if (app.globalData.mid) {
								autoFollow(app.globalData.mid).then(() => {
									self.$store.commit('CURRENT_MERID', app.globalData.mid);
								}).catch(e => {});
							}
						}
					})
					.catch(res => {
						uni.hideLoading();
						uni.showToast({
							title: res,
							icon: 'none',
							duration: 2000
						});
					});
			},
			// #endif
			// #ifdef H5
			// 获取url后面的参数
			getQueryString(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
				var reg_rewrite = new RegExp("(^|/)" + name + "/([^/]*)(/|$)", "i");
				var r = window.location.search.substr(1).match(reg);
				var q = window.location.pathname.substr(1).match(reg_rewrite);
				if (r != null) {
					return unescape(r[2]);
				} else if (q != null) {
					return unescape(q[2]);
				} else {
					return null;
				}
			},
			// 公众号登录
			wechatLogin() {
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					});
					return;
				}
				if (!this.code && this.options.scope !== 'snsapi_base') {
					this.$wechat.oAuth('snsapi_userinfo', '/pages/users/wechat_login/index');
				} else {
					// if (this.authKey) {
					// 	this.isUp = true;
					// }
					this.isUp = true;
				}
			},
			// 输入手机号后的回调
			wechatPhone() {
				this.$Cache.clear('snsapiKey');
				if (this.options.back_url) {
					let url = uni.getStorageSync('snRouter');
					url = url.indexOf('/pages/index/index') != -1 ? '/' : url;
					if (url.indexOf('/pages/users/wechat_login/index') !== -1) {
						url = '/';
					}
					if (!url) {
						url = '/pages/index/index';
					}
					this.isUp = false
					uni.showToast({
						title: '登录成功',
						icon: 'none'
					})
					setTimeout(res => {
						location.href = url
					}, 800)
				} else {
					uni.navigateBack()
				}
			}
			// #endif
		}
	}
</script>

<style lang="scss">
	page {
		background: linear-gradient(180deg, #f5f5f5, #b8fe4e);
		height: 100vh;
		overflow: hidden;
	}

	.page {
		background: linear-gradient(180deg, #f5f5f5, #b8fe4e);
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.main-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 0 50rpx;
		padding-bottom: 100rpx;
	}

	.logo-section {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-start;
		padding-top: 120rpx;
	}

	.logo-container {
		width: 200rpx;
		height: 200rpx;
		background: #ffffff;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 60rpx
	}

	.title-text {
		font-size: 60rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		line-height: 1.2;
	}

	.subtitle-text {
		font-size: 60rpx;
		font-weight: bold;
		color: #333333;
		line-height: 1.2;
	}

	.login-card {
		background: #ffffff;
		border-radius: 40rpx;
		padding: 60rpx 50rpx 80rpx;
		margin: 0 auto;
		width: 100%;
	}

	/* 手机登录表单卡片样式 */
	.mobile-form-card {
		background: #ffffff;
		border-radius: 40rpx;
		padding: 60rpx 50rpx 80rpx;
		margin: 0 auto;
		width: 100%;
	}

	.form-group {
		margin-bottom: 40rpx;
	}

	.input-row {
		display: flex;
		align-items: center;
		background: #f5f5f5;
		border-radius: 50rpx;
		padding: 0 30rpx;
		margin-bottom: 30rpx;
		height: 100rpx;
	}

	.phone-input {
		flex: 1;
		height: 100%;
		font-size: 32rpx;
		color: #333333;
		background: transparent;
		border: none;
		outline: none;

		&::placeholder {
			color: #999999;
		}
	}

	.verify-btn {
		background: transparent;
		border: none;
		font-size: 28rpx;
		color: #666666;
		padding: 0;
		margin: 0;

		&::after {
			border: none;
		}

		&:disabled {
			color: #cccccc;
		}
	}

	.verify-input {
		height: 100rpx;
		background: #f5f5f5;
		border-radius: 50rpx;
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333333;
		border: none;
		outline: none;
		margin-bottom: 40rpx;

		&::placeholder {
			color: #999999;
		}
	}

	.login-submit-btn {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		background: #000000;
		border: none;
		border-radius: 50rpx;
		font-size: 32rpx;
		color: #ffffff;
		text-align: center;

		&::after {
			border: none;
		}
	}

	.back-btn-section {
		display: flex;
		justify-content: center;
		margin: 40rpx 0;
	}

	.back-btn {
		font-size: 28rpx;
		color: #000000;
		font-weight: bold;

		&::after {
			border: none;
		}
	}

	.login-buttons {
		margin-bottom: 40rpx;
	}

	.phone-login-btn {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		background: #ffffff;
		border: 2rpx solid #e0e0e0;
		border-radius: 50rpx;
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 30rpx;
		text-align: center;

		&::after {
			border: none;
		}
	}

	.wechat-login-btn {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		background: #000000;
		border: none;
		border-radius: 50rpx;
		font-size: 32rpx;
		color: #ffffff;
		text-align: center;

		&::after {
			border: none;
		}
	}

	.agreement-section {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
	}

	.agreement-checkbox {
		width: 32rpx;
		height: 32rpx;
		margin-right: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.checkbox-icon {
		width: 32rpx;
		height: 32rpx;
	}

	.checkbox-unchecked {
		width: 24rpx;
		height: 24rpx;
		border: 2rpx solid #cccccc;
		border-radius: 4rpx;
		background: #ffffff;
	}

	.agreement-text {
		font-size: 24rpx;
		color: #999999;
		line-height: 1.4;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.agreement-link {
		color: #999999;
		margin: 0 4rpx;
	}
</style>