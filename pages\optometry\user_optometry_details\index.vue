<template>
	<view :data-theme="theme">
		<view class='container'>
			<view class='optometry borRadius14'>
				<view class='optometry-name'>验光人：{{optometry.name}}</view>
				<view class='optometry-data' v-if="optometry.data">
					<view class='optometry-flex optometry-column'>
						<view class='optometry-flex optometry-row'>
							<view class='flex-item'></view>
							<view class='flex-item'>右眼</view>
							<view class='flex-item'>左眼</view>
						</view>
						<view class='optometry-flex optometry-row'>
							<view class='flex-item'>度数(S)</view>
							<view class='flex-item'>{{optometry.data.RS > 0 ? '+' + optometry.data.RS : optometry.data.RS}}</view>
							<view class='flex-item'>{{optometry.data.LS > 0 ? '+' + optometry.data.LS : optometry.data.LS}}</view>
						</view>
						<view class='optometry-flex optometry-row'>
							<view class='flex-item'>散光(C)</view>
							<view class='flex-item'>{{optometry.data.RC}}</view>
							<view class='flex-item'>{{optometry.data.LC}}</view>
						</view>
						<view class='optometry-flex optometry-row'>
							<view class='flex-item'>轴位(A)</view>
							<view class='flex-item'>{{optometry.data.RA}}</view>
							<view class='flex-item'>{{optometry.data.LA}}</view>
						</view>
						<view class='optometry-flex optometry-row'>
							<view class='flex-item'>瞳距(PD)</view>
							<view class='flex-item'>{{optometry.data.RPD}}</view>
							<view class='flex-item'>{{optometry.data.LPD}}</view>
						</view>
						<view class='optometry-flex optometry-row'>
							<view class='flex-item'>瞳高(PH)</view>
							<view class='flex-item'>{{optometry.data.RPH}}</view>
							<view class='flex-item'>{{optometry.data.LPH}}</view>
						</view>
					</view>
					<view class='optometry-flex optometry-row'>
						<view class='flex-item'>下加光(ADD)</view>
						<view class='flex-item' style='flex: 2'>{{optometry.data.ADD}}</view>
					</view>
				</view>
				<view v-if="optometry.url">
					<view class='image-title'>验光单照片</view>
					<image :src="optometry.url" mode="widthFix" @click="previewImg(optometry.url)"></image>
				</view>
			</view>
			<view v-if="id" class='default acea-row row-middle borRadius14'>
				<checkbox-group>
					<checkbox :checked="optometry.isDefault" disabled="true"/>是否为默认验光单
				</checkbox-group>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getOptometryDetail
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		data() {
			return {
				id: 0,
				optometry: {
					name: '',
					data: {
						RS: 0,
						RC: 0,
						RA: 0,
						LS: 0,
						LC: 0,
						LA: 0,
						RPD: 0,
						LPD: 0,
						RPH: 0,
						LPH: 0,
						ADD: 0
					},
					url: '',
					isDefault: false
				},
				theme: app.globalData.theme,
				nearShow: false
			};
		},
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						if (this.id) {
							this.getOptometryDetail();
						}
					}
				},
				deep: true
			}
		},
		onLoad(options) {
			if (this.isLogin) {
				this.id = options.id || 0;
				if (this.id) {
					this.getOptometryDetail();
				} else {
					let optometry = JSON.parse(options.optometry);
					if (optometry) {
						this.$set(this, 'optometry', optometry);
					}	
				}
			} else {
				toLogin();
			}
		},
		methods: {
			getOptometryDetail: function() {
				if (!this.id) return false;
				let that = this;
				getOptometryDetail(this.id).then(res => {
					if(res.data){
						res.data.data = JSON.parse(res.data.data);
						that.$set(that, 'optometry', res.data);
					}
				});
			},
			previewImg: function(url) {
				uni.previewImage({
					current: 0,
					urls: [url]
				});
			}
		}
	}
</script>

<style lang="scss">
	.container {
		padding: 20rpx;
	}
	.optometry {
		background-color: #fff;
		padding: 20rpx 20rpx;
		font-size: 28rpx;
		color: #282828;
	}
	.optometry .optometry-name {
		font-size: 28rpx;
		margin-top: 10rpx;
		margin-left: 15rpx;
	}
	.optometry .optometry-data {
		margin-top: 30rpx;
		border-bottom: 1rpx solid #ccc;
		border-right: 1rpx solid #ccc;
		font-size: 26rpx;

		.optometry-flex {
			display: flex;
			flex-direction: row;
		}
		.optometry-row {
			flex-direction: row;
		}
		.optometry-column {
			flex-direction: column;
		}
		.flex-item {
			flex: 1;
			border-left: 1rpx solid #ccc;
			border-top: 1rpx solid #ccc;
			padding: 20rpx 0rpx;
			text-align: center;
		}
	}
	.optometry .image-title {
		padding: 20rpx 0;
		font-size: 28rpx;
		margin-left: 15rpx;
		margin-top: 10rpx;
	}
	.optometry image {
		width: 100%;
	}
	
	.default {
		padding: 0 30rpx;
		height: 90rpx;
		background-color: #fff;
		margin: 20rpx 0;
	}
	
	.default checkbox {
		margin-right: 15rpx;
	}
	
	/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {
		@include main_bg_color(theme);
		@include coupons_border_color(theme);
		color: #fff !important
	}
	
	/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
		@include main_bg_color(theme);
		@include coupons_border_color(theme);
		color: #fff !important;
		margin-right: 0 !important;
	}
</style>
