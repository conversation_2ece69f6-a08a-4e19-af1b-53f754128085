<template>
  <view class="integral-detail">
    <!-- Tab切换 -->
    <view class="tab-container">
      <view class="tab-item" 
            :class="{ active: activeTab === 0 }" 
            @click="switchTab(0)">
        获得积分
      </view>
      <view class="tab-item" 
            :class="{ active: activeTab === 1 }" 
            @click="switchTab(1)">
        积分使用
      </view>
    </view>
    
    <!-- 积分记录列表 -->
    <view class="list-container">
      <view class="integral-list">
        <view class="integral-item" v-for="(item, index) in integralList" :key="index">
          <view class="item-left">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-time">{{ item.time }}</view>
          </view>
          <view class="item-right">
            <view class="item-score" :class="{ minus: item.score < 0 }">
              {{ item.score > 0 ? '+' + item.score : item.score }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getIntegralListType } from "@/api/user.js";

export default {
  data() {
    return {
      activeTab: 0, // 0: 获得积分, 1: 积分使用
      integralList: [],
      page: 1,
      limit: 20,
      loading: false,
      finished: false
    }
  },
  onLoad() {
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '积分明细'
    });
    // 初始化数据
    this.loadIntegralData();
  },
  methods: {
    switchTab(index) {
      this.activeTab = index;
      // 重置分页状态
      this.page = 1;
      this.finished = false;
      this.integralList = [];
      // 加载对应tab的数据
      this.loadIntegralData();
    },
    
    async loadIntegralData() {
      if (this.loading || this.finished) return;
      
      this.loading = true;
      try {
        const params = {
          type: this.activeTab + 1, // 1-获得积分, 2-积分使用
          page: this.page,
          limit: this.limit
        };
        
        const res = await getIntegralListType(params);
        if (res.code === 200) {
          const data = res.data;
          if (data.list && data.list.length > 0) {
            // 格式化数据
            const formattedList = data.list.map(item => ({
              title: item.title,
              time: item.updateTime,
              score: this.activeTab === 0 ? item.integral : -Math.abs(item.integral)
            }));
            
            if (this.page === 1) {
              this.integralList = formattedList;
            } else {
              this.integralList = this.integralList.concat(formattedList);
            }
            
            // 判断是否还有更多数据
            if (data.list.length < this.limit) {
              this.finished = true;
            } else {
              this.page++;
            }
          } else {
            this.finished = true;
          }
        }
      } catch (error) {
        console.error('获取积分记录失败:', error);
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.integral-detail {
  background-color: #F5F5F5;
  min-height: 100vh;
}

.tab-container {
  display: flex;
  height: 88upx;
  
  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28upx;
    color: #999999;
    position: relative;
    
    &.active {
      color: #333333;
      font-weight: 500;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60upx;
        height: 4upx;
        background-color: #BDFD5B;
        border-radius: 8upx;
      }
    }
  }
}

.list-container {
  padding: 20upx 30upx;
}

.integral-list {
  background-color: #FFFFFF;
  border-radius: 16upx;
  overflow: hidden;
  
  .integral-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30upx;
    border-bottom: 1upx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-left {
      flex: 1;
      
      .item-title {
        font-size: 28upx;
        color: #333333;
        margin-bottom: 10upx;
      }
      
      .item-time {
        font-size: 24upx;
        color: #999999;
      }
    }
    
    .item-right {
      .item-score {
        font-size: 28upx;
        color: #333333;
        font-weight: 500;
        
        &.minus {
          color: #333333;
        }
      }
    }
  }
}
</style>
