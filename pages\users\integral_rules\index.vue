<template>
	<view :data-theme="theme">
		<view class='rules-page'>
			<view class='strategy-section'>
				<view class='strategy-title'>
					<image src='/static/images/logo.png' class='logo-icon' mode="aspectFit"></image>
					<text class='section-title-text'>积分规则</text>
				</view>
				<view class='strategy-list'>
					<rich-text :nodes="integralRule"></rich-text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		signInfo
	} from '@/api/user.js';
	let app = getApp();
	export default {
		data() {
			return {
				theme: app.globalData.theme,
				integralRule: ''
			};
		},
		onLoad() {
			this.getIntegralRule();
		},
		methods: {
			getIntegralRule() {
				const month = `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}`;
				signInfo({
					month: month
				}).then(res => {
					if (res.data && res.data.integralRule) {
						this.integralRule = res.data.integralRule.replace(/\r?\n|↵/g, '<br/>');
					}
				});
			},
			// 返回上一页
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style scoped lang="scss">
	.rules-page {
		min-height: 100vh;
		background: #f5f5f5;
		padding: 20rpx 0;
	}

	/* 积分规则区域 */
	.strategy-section {
		background: white;
		margin: 0 30rpx;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	}

	.strategy-title {
		background: linear-gradient(180deg, #b8fe4e, #f2f8e0);
		color: #333;
		font-size: 28rpx;
		font-weight: bold;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.logo-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.section-title-text {
		color: #333;
		font-size: 28rpx;
		font-weight: bold;
	}

	.strategy-list {
		padding: 30rpx;
		color: #333;
		font-size: 28rpx;
		line-height: 1.8;
	}
</style>
