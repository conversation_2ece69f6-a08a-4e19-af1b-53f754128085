<template>
	<view>
		<view v-if="merchantList.length" class="store-wrapper" :class="isStreet?'street-pad20':''">
			<view v-if="item.proList.length" class="store-item" :class="isStreet?'street-noPad':''" v-for="(item,index) in merchantList"
				:key="index"
				:style="[isStreet ? { 'background-image': item.streetBackImage?'url('+ item.streetBackImage +')': 'url('+ moren +')' } : '']">
				<view @click="goShop(item.id)" class="head" :class="isStreet?'street-backImage':''">

					<view class="left-wrapper">
						<view class="logo" :class="isStreet?'street-logo':''">
							<image :src="isStreet?item.rectangleLogo:item.avatar" mode=""></image>
						</view>
						<view class="con-box">
							<view class="name line1" :class="isStreet?'street-name':''">
								<text class="mer_name line1">{{item.name}}</text>
								<text v-if="item.isSelf" class="font-bg-red merType mr10" style="position: absolute;margin-top: 4rpx;">自营</text>
							</view>
							<view class="star-box">
								<view v-show="!isStreet" class="star">
									<view class="stars" :class="isStreet?'street-active':'star-active'"
										:style="'width:'+(item.starLevel/5)*100+'%'"></view>
								</view>
								<view v-show="!isStreet" class="lines tui-skeleton-rect"></view>
								<view class="fans" :style="isStreet?'color:#fff':'color:#999'">
									{{ item.followerNum < 10000 ? item.followerNum : (item.followerNum / 10000).toFixed(2) + '万' }}人关注
								</view>
							</view>
						</view>
					</view>
					<view v-if="!isStreet" class="link" @click="goShop(item.id)">进店</view>
				</view>
				<view class="pic-wrapper">
					<view v-for="(goods,indexn) in item.proList" :key="indexn" class="goods"
						@click="godDetail(goods)">
						<view class="pic-item">
							<easy-loadimage :image-src="goods.image"></easy-loadimage>
							<view v-if="!isStreet" class="price">
								<text>￥</text>{{goods.price}}
							</view>
						</view>
						<view class="pic-name line1" v-if="isStreet">{{goods.name}}</view>
						<view v-if="isStreet" class="street-price">
							￥{{goods.price}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		goShopDetail
	} from '@/libs/order.js'
	import {
		mapGetters
	} from "vuex";
	import easyLoadimage from '@/components/base/easy-loadimage.vue';
	export default {
		data() {
			return {
				skeletonShow: true,
				isShow: true,
				moren: require('@/static/images/mermoren.png')
			}
		},
		components: {
			easyLoadimage
		},
		computed: mapGetters(['uid']),
		props: {
			merchantList: {
				type: Array,
				default: () => []
			},
			isStreet: {
				type: Boolean,
				default: () => false
			}
		},
		created() {},
		methods: {
			godDetail(item) {
				goShopDetail(item.id)
			},
			menusTap(url) {
				uni.navigateTo({
					url
				})
			},
			goShop(id) {
				uni.navigateTo({
					url: `/pages/merchant/home/<USER>
				})
			},
		}
	};
</script>

<style scoped lang="scss">
	.store-wrapper {
		padding: 0 30rpx;
	}

	.star-box {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;

		.lines {
			width: 2rpx;
			height: 28rpx;
			background: #BFBFBF;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			margin-left: 10rpx;
			margin-right: 10rpx;
		}

		.fans {
			font-size: 20rpx;
		}

		.star {
			position: relative;
			width: 111rpx;
			height: 19rpx;
			background: url(~static/images/star.png);
			background-size: 111rpx 19rpx;
		}

		.stars {
			position: absolute;
			left: 0;
			top: 0;
			width: 111rpx;
			height: 19rpx;
			overflow: hidden;
		}

		.star-active {
			background: url(~static/images/star_active.png);
			background-size: 111rpx 19rpx;
		}

		.num {
			color: $theme-color;
			font-size: 24rpx;
			margin-left: 10rpx;
		}
	}

	.street {
		&-logo {
			width: 191rpx !important;
			height: 80rpx !important;
			background-image: url('@/static/images/shang.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			border-radius: 0 !important;
			margin-right: 0 !important;

			image {
				width: 130rpx !important;
				height: 44rpx !important;
				margin-top: 13rpx;
				margin-left: 17rpx;
			}
		}

		&-name {
			color: #fff !important;
		}

		&-pad20 {
			padding: 0 20rpx;
		}

		&-backImage {
			padding: 0rpx !important;
		}

		&-noPad {
			border-top-left-radius: 16rpx;
			border-top-right-radius: 16rpx;
			height: 353rpx;
			padding: 24rpx !important;
			background-size: 100% 100%;
			margin-bottom: 97rpx !important;
		}

		&-pic {
			border-radius: 8rpx !important;
			overflow: hidden;
			margin-right: 12rpx !important;
		}

		&-price {
			font-size: 28rpx;
			@include main-color(theme);
			margin-top: 5rpx;
			font-weight: 800;
		}

		&-wrapper {
			padding: 16rpx !important;
			margin-top: -2rpx;
		}

		&-active {
			background: url(~static/images/star_active_bai.png);
			background-size: 111rpx 19rpx;
		}
	}

	.backImage {
		padding: 24rpx 0 24rpx 20rpx;
		border-radius: 16px 16px 0px 0px;
	}

	.store-item {
		margin-bottom: 30rpx;
		padding: 0 10rpx;
		background-color: #fff;
		border-radius: 24rpx;

		.head {
			display: flex;
			justify-content: space-between;
			padding: 20rpx;
			padding-top: 30rpx;
			padding-bottom: 10rpx;

			.left-wrapper {
				display: flex;
				align-items: center;

				.logo {
					width: 80rpx;
					height: 80rpx;
					border-radius: 6rpx;
					overflow: hidden;
					margin-right: 20rpx;

					image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 6rpx;
						overflow: hidden;
					}
				}

				.con-box {
					.bt-color {
						width: max-content;
						white-space: nowrap;
						font-size: 16rpx;
						padding: 2rpx 10rpx;
						background-color: #fff;
						@include main_color(theme);
						border-radius: 13rpx;

					}

					.name {
						font-size: 30rpx;
						color: #333;
						font-weight: bold;

						.mer_name {
							max-width: 400rpx;
							margin-right: 10rpx;
						}
					}
				}
			}

			.link {
				width: 114rpx;
				height: 50rpx;
				line-height: 50rpx;
				@include linear-gradient(theme);
				border-radius: 25rpx;
				text-align: center;
				color: #fff;
				font-size: 24rpx;
			}
		}

		.pic-wrapper {
			background-color: #FFFFFF;
			border-radius: 0px 16rpx 16rpx 16rpx;
			padding: 20rpx 10rpx;
			
			.goods {
				width: 33.33%;
				display: inline-block;
				padding: 0 10rpx;
			}

			.pic-name {
				margin-top: 10rpx;
				font-size: 24rpx;
				color: #333333;
			}

			.pic-item {
				position: relative;
				height: 198rpx;
				overflow: hidden;
				border-radius: 14rpx;

				.easy-loadimage,
				image,
				uni-image {
					overflow: hidden;
					border-radius: 16rpx;
					width: 100%;
					height: 100%;
				}

				.price {
					position: absolute;
					right: 0;
					bottom: 0;
					height: 36rpx;
					padding: 0 10rpx;
					line-height: 36rpx;
					text-align: center;
					background: rgba(0, 0, 0, .5);
					border-radius: 16rpx 2rpx 16rpx 2rpx;
					color: #fff;
					font-size: 24rpx;

					text {
						font-size: 18rpx;
					}
				}

				&:nth-child(3n) {
					margin-right: 0;
				}
			}
		}
	}
</style>
