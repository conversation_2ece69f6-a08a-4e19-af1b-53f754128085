<template>
	<view>
		<view class="integral-product-window" :class="isVisible ? 'show' : ''">
			<!-- 弹框头部 -->
			<view class="popup-header">
				<view class="popup-title">选择规格</view>
				<view class="close-btn" @click="closeWindow">
					<text class="iconfont icon-guanbi"></text>
				</view>
			</view>

			<!-- 商品信息区域 -->
			<view class="product-info-section">
				<view class="product-image">
					<image :src="selectedSku.image || productInfo.image"></image>
				</view>
				<view class="product-details">
					<!-- 积分价格信息 -->
					<view class="price-info">
						<view class="integral-price">
							<text class="price-number">{{ productInfo.integralPrice }}</text>
							<text class="price-unit">积分</text>
						</view>
					</view>
					<!-- 商品名称 -->
					<view class="product-name">
						{{ productInfo.name }}
					</view>
					<!-- 库存信息 -->
					<view class="stock-info">
						库存：{{ selectedSku.stock || 0 }}件
					</view>
				</view>
			</view>

			<!-- 规格选择区域 -->
			<view class="spec-selection-area">
				<view class="spec-item" v-for="(attr, index) in productAttr" :key="index">
					<view class="spec-title">{{ attr.attrName }}</view>
					<view class="spec-options">
						<view 
							class="spec-option"
							:class="selectedAttrs[attr.attrName] === option ? 'selected' : ''"
							v-for="option in attr.attrValues"
							:key="option"
							@click="selectAttr(attr.attrName, option)"
						>
							{{ option }}
						</view>
					</view>
				</view>

				<!-- 数量选择 -->
				<view class="quantity-section">
					<view class="quantity-title">数量</view>
					<view class="quantity-controls">
						<view 
							class="quantity-btn decrease"
							:class="quantity <= 1 ? 'disabled' : ''"
							@click="decreaseQuantity"
						>
							-
						</view>
						<input 
							class="quantity-input"
							type="number"
							v-model="quantity"
							@input="onQuantityInput"
							min="1"
							:max="selectedSku.stock || 999"
						/>
						<view 
							class="quantity-btn increase"
							:class="quantity >= (selectedSku.stock || 999) ? 'disabled' : ''"
							@click="increaseQuantity"
						>
							+
						</view>
					</view>
				</view>
			</view>

			<!-- 底部按钮 -->
			<view class="bottom-buttons">
				<view class="confirm-btn" @click="confirmSelection">
					确定兑换
				</view>
			</view>
		</view>
		<view class="mask" v-if="isVisible" @click="closeWindow"></view>
	</view>
</template>

<script>
export default {
	props: {
		isVisible: {
			type: Boolean,
			default: false
		},
		productInfo: {
			type: Object,
			default: () => ({})
		},
		productValue: {
			type: Object,
			default: () => ({})
		},
		productAttr: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			selectedAttrs: {}, // 选中的属性
			quantity: 1,
			selectedSku: {} // 当前选中的SKU
		};
	},
	watch: {
		productAttr: {
			handler(newAttrs) {
				if (newAttrs && newAttrs.length > 0) {
					this.initializeSelection();
				}
			},
			immediate: true
		},
		selectedAttrs: {
			handler() {
				this.updateSelectedSku();
			},
			deep: true
		}
	},
	methods: {
		// 初始化选择
		initializeSelection() {
			const attrs = {};
			this.productAttr.forEach(attr => {
				if (attr.attrValues && attr.attrValues.length > 0) {
					// 默认选中第一个选项
					attrs[attr.attrName] = attr.attrValues[0];
				}
			});
			this.selectedAttrs = attrs;
		},

		// 选择属性
		selectAttr(attrName, option) {
			this.$set(this.selectedAttrs, attrName, option);
		},

		// 更新选中的SKU
		updateSelectedSku() {
			if (!this.productValue || Object.keys(this.selectedAttrs).length === 0) {
				return;
			}

			// 根据选中的属性找到对应的SKU
			for (let sku in this.productValue) {
				const skuData = this.productValue[sku];
				if (skuData.attrValue) {
					try {
						const attrValue = JSON.parse(skuData.attrValue);
						let match = true;
						
						for (let attrName in this.selectedAttrs) {
							if (attrValue[attrName] !== this.selectedAttrs[attrName]) {
								match = false;
								break;
							}
						}
						
						if (match) {
							this.selectedSku = skuData;
							// 如果当前数量超过库存，重置为库存数量
							if (this.quantity > skuData.stock) {
								this.quantity = Math.max(1, skuData.stock);
							}
							break;
						}
					} catch (e) {
						console.error('解析SKU属性失败:', e);
					}
				}
			}
		},

		// 减少数量
		decreaseQuantity() {
			if (this.quantity > 1) {
				this.quantity--;
			}
		},

		// 增加数量
		increaseQuantity() {
			const maxStock = this.selectedSku.stock || 999;
			if (this.quantity < maxStock) {
				this.quantity++;
			}
		},

		// 数量输入处理
		onQuantityInput(e) {
			let value = parseInt(e.detail.value) || 1;
			const maxStock = this.selectedSku.stock || 999;
			
			if (value < 1) value = 1;
			if (value > maxStock) value = maxStock;
			
			this.quantity = value;
		},

		// 确认选择
		confirmSelection() {
			if (!this.selectedSku.id) {
				uni.showToast({
					title: '请选择规格',
					icon: 'none'
				});
				return;
			}

			if (this.selectedSku.stock < this.quantity) {
				uni.showToast({
					title: '库存不足',
					icon: 'none'
				});
				return;
			}

			const selectionData = {
				sku: this.selectedSku,
				quantity: this.quantity,
				selectedAttrs: this.selectedAttrs,
				totalIntegral: this.productInfo.integralPrice * this.quantity
			};

			this.$emit('confirm', selectionData);
		},

		// 关闭弹窗
		closeWindow() {
			this.$emit('close');
		}
	}
};
</script>

<style lang="scss" scoped>
.integral-product-window {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	transform: translateY(100%);
	transition: transform 0.3s ease;
	z-index: 999;
	max-height: 80vh;
	overflow-y: auto;

	&.show {
		transform: translateY(0);
	}
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #222222;
	}

	.close-btn {
		padding: 10rpx;
		
		.iconfont {
			font-size: 32rpx;
			color: #999999;
		}
	}
}

.product-info-section {
	display: flex;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.product-image {
		width: 150rpx;
		height: 150rpx;
		margin-right: 20rpx;

		image {
			width: 100%;
			height: 100%;
			border-radius: 12rpx;
		}
	}

	.product-details {
		flex: 1;

		.price-info {
			margin-bottom: 16rpx;

			.integral-price {
				display: flex;
				align-items: baseline;

				.price-number {
					font-size: 36rpx;
					color: #ff2222;
					font-weight: bold;
				}

				.price-unit {
					font-size: 24rpx;
					color: #ff2222;
					margin-left: 8rpx;
				}
			}
		}

		.product-name {
			font-size: 28rpx;
			color: #222222;
			line-height: 40rpx;
			margin-bottom: 12rpx;
		}

		.stock-info {
			font-size: 24rpx;
			color: #999999;
		}
	}
}

.spec-selection-area {
	padding: 30rpx;
}

.spec-item {
	margin-bottom: 40rpx;

	.spec-title {
		font-size: 28rpx;
		color: #222222;
		margin-bottom: 20rpx;
		font-weight: 500;
	}

	.spec-options {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;

		.spec-option {
			padding: 16rpx 32rpx;
			border: 2rpx solid #f0f0f0;
			border-radius: 8rpx;
			font-size: 26rpx;
			color: #666666;
			background: #f8f8f8;

			&.selected {
				border-color: #ff2222;
				background: #fff5f5;
				color: #ff2222;
			}
		}
	}
}

.quantity-section {
	.quantity-title {
		font-size: 28rpx;
		color: #222222;
		margin-bottom: 20rpx;
		font-weight: 500;
	}

	.quantity-controls {
		display: flex;
		align-items: center;
		gap: 20rpx;

		.quantity-btn {
			width: 60rpx;
			height: 60rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			color: #666666;
			background: #ffffff;

			&.disabled {
				color: #cccccc;
				border-color: #f0f0f0;
			}
		}

		.quantity-input {
			width: 120rpx;
			height: 60rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 8rpx;
			text-align: center;
			font-size: 28rpx;
			background: #ffffff;
		}
	}
}

.bottom-buttons {
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;

	.confirm-btn {
		width: 100%;
		height: 80rpx;
		background: #ff2222;
		color: #ffffff;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 998;
}
</style> 