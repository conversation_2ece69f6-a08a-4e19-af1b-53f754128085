<template>
	<view :data-theme="theme" class="">
	<!-- 	<skeleton :show="showSkeleton" :isNodes="isNodes" ref="skeleton" loading="chiaroscuro" selector="skeleton"
			bgcolor="#FFF"></skeleton> -->
		<!-- pictrue#ifdef MP || APP-PLUS -->
		<!-- <view style="visibility: hidden;" :style="{ height: navHeight + 'px' }" v-if="isFixed"></view> -->
	
		<view class="navTabBox tabNav tui-skeletonpictrue" :class="{scrolled:isScrolled}">
			<view class="longTab">
				<scroll-view scroll-x="true" style="white-space: nowrap; display: flex;" scroll-with-animation
					:scroll-left="tabLeft" show-scrollbar="true">
					<view class="longItem" :class="index===tabClick?'click':''" :data-index="index"
						v-for="(item,index) in tabTitle" :key="index" :id="'id'+index"
						@click="longClick(index,item.id,item.pid)">
						<view class="acea-row row-middle" :class="{'scrolled': isScrolled}">
							<view class="name tui-skeleton-rect">{{item.name}}</view>
							<view class="underlineBox" v-if="index===tabClick">
								<view class="underline"></view>
							</view>
						</view>
					</view>

				</scroll-view>
			</view>
			<view class="category" :class="{'scrolled': isScrolled}">
				<text v-if="isShow" class="iconfont icon-xiangshang" @click="isShow=false"></text>
				<text v-if="!isShow" class="iconfont icon-xiangxia" @click="isShow=true"></text>
			</view>
		</view>
		<view v-if="isShow" class="navChangeBox" catchtouchmove="true">
			<view class="navChange acea-row">
				<view class="titleBox"><text class="title nobg">全部分类</text></view>
				<block v-for="(item,index) in tabTitle" :key="item.id">
					<view class="titleBox"><text class="title line1"  :class="index===tabClick?'changed':''" 
					:id="'id'+index" @click="longClick(index,item.id,item.pid)">{{item.name}}</text></view>
				</block>	
			</view>
			<view class="mask" @touchmove.prevent :hidden="!isShow" @click="isShow=false"></view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getCategoryFirst,
		getCategoryThird
	} from '@/api/api.js';
	let app = getApp();
	export default {
		name: 'navTab',
		props: {
			merId: {
				type: String || Number,
				default: ''
			},
			navIndex: {
				type: Number,
				default: 0
			},
			isScrolled: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				showSkeleton:  true,
				tabClick: 0, //导航栏被点击
				isLeft: 0, //导航栏下划线位置
				isWidth: 0, //每个导航栏占位
				mainWidth: 0,
				tabLeft: 0,
				swiperIndex: 0,
				childIndex: 0,
				childID: 0,
				bgColor: '',
				mbConfig: '',
				txtColor: '',
				fixedTop: 0,
				isTop: 0,
				navHeight: 0,
				theme:app.globalData.theme,
				isShow: false,
				tabTitle: [{},{},{},{},{},{},{},{},]
			};
		},
		created() {
			var that = this
			that.getFirstCategory();
			// 获取设备宽度
			uni.getSystemInfo({
				success(e) {
					that.mainWidth = e.windowWidth
					that.isWidth = (e.windowWidth - 65) / 4
				}
			})
			setTimeout((e) => {
				const query = uni.createSelectorQuery().in(this);
				query.select('.navTabBox').boundingClientRect(data => {
					that.navHeight = data.height
					that.$emit('bindHeight', data.height)
				}).exec();
			}, 200)
			// #ifdef MP || APP-PLUS
			this.isTop = (uni.getSystemInfoSync().statusBarHeight + 110) + 'px'
			// #endif
			// #ifdef H5 
			this.isTop = 0
			// #endif
		},
		methods: {
			getFirstCategory: function() {
				let that = this;
				getCategoryFirst().then(res => {
					res.data.unshift({
						name: '首页',
						id: 0
					});
					that.tabTitle = res.data;
				})
			},
			// 导航栏点击
			longClick(index, id, fid) {
				this.$nextTick(() => {
					let id = 'id' + index
				})
				this.tabLeft = (index - 2) * this.isWidth //设置下划线位置
				this.tabClick = index //设置导航点击了哪一个
				this.parentEmit(id, index);
				this.isShow = false;
			},
			parentEmit(id,index) {
				this.$emit('changeTab', id, index);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.scrolled{
		color: #000 !important;
		background-color: #FFFFFF;
	}
	.cateTop{
		top: 68rpx;
	}
	.mask{
		z-index: 999;
		top: 340rpx;
	}
	.lines{
		width: 2rpx;
		height: 28rpx;
		background: linear-gradient(180deg, rgba(255,255,255,0.1) 0%, #FFFFFF 49%, rgba(255,255,255,0.1) 100%);
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
	}
	.navChangeBox{
		position: relative;
	}
	.navChange {
		background-color: #fff;
		position: absolute;
		z-index: 999999;
		width: 100%;
		border-radius: 0px 0px 16rpx 16rpx;
		padding: 24rpx 20rpx;
		padding-bottom: 0;

		.nobg {
			background-color: #fff !important;
		}
        .titleBox{
			width: 134rpx;
			height: 58rpx;
			margin-right: 10rpx;
			margin-bottom: 30rpx;
			background: #F2F2F2;
			border-radius: 29rpx;
			text-align: center;
		}
		.title {
			margin: 0 auto;
			display: inline-block;
			width: 118rpx;
			height: 58rpx;
			line-height: 58rpx;
			text-align: center;
			
			opacity: 1;
			color: #333333;
			font-size: 24rpx;
		}

		.titleBox:nth-child(5n) {
			margin-right: 0;
		}
		.titleBox:last-child {
			margin-bottom: 0;
		}
		.changed{
			background: #fff;
			border-radius: 29px 29px 29px 29px;
			opacity: 1;
			@include coupons_border_color(theme);
			@include main_color(theme);
		}
	}

	.row-middle {
		flex-direction: column;
	}

	.navTabBox {
		width: 100%;
		height: 66rpx;
		color: rgba(255, 255, 255, 1);
		position: relative;
		padding: 0 24rpx 0 24rpx;
		display: flex;
		justify-content: space-between;
		z-index: 9;

		&.isFixed {
			z-index: 10;
			position: fixed;
			left: 0;
			width: 100%;
			/* #ifdef H5 */
			top: 0;
			/* #endif */
			@include main_bg_color(theme);
		}

		.click {
			color: white;
		}

		.longTab {
			width: 94%;

			.longItem {
				//height: 72rpx;
				display: inline-block;
				// line-height: 52rpx;
				text-align: center;
				font-size: 28rpx;
				color: #fff;
				white-space: nowrap;
				text-overflow: ellipsis;
				margin-right: 42rpx;

				&.click {
					font-weight: bold;
					font-size: 30rpx;
					color: #fff;
					font-weight: bold;
				}
				.name{
					height: 48rpx;
				}
			}

			.underlineBox {
				height: 3px;
				// width: 20%;
				// display: flex;
				// align-content: center;
				// justify-content: center;
				transition: .5s;

				.underline {
					width: 33rpx;
					height: 4rpx;
					background-color: #fff;
				}
			}
		}

		.category {
			height: 60rpx;
			line-height: 42rpx;
			z-index: 3;

			// padding: 0 15rpx 0 25rpx;
			.iconfont {
				font-size: 24rpx;
			}
		}
	}

	.child-box {
		width: 100%;
		position: relative;
		// height: 152rpx;
		background-color: #fff;
		/* #ifdef H5 */
		box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.02);
		/* #endif */
		/* #ifdef MP */
		box-shadow: 0 2rpx 3rpx 1rpx #f9f9f9;
		/* #endif */

		.wrapper {
			display: flex;
			align-items: center;
			padding: 20rpx 0;
			background: #fff;
			/* #ifdef H5 */
			//box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.06);
			/* #endif */
		}

		.child-item {
			flex-shrink: 0;
			width: 140rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-left: 10rpx;

			image {
				width: 90rpx;
				height: 90rpx;
				border-radius: 50%;
			}

			.txt {
				font-size: 24rpx;
				color: #282828;
				text-align: center;
				margin-top: 10rpx;
			}

			&.on {
				image {
					border: 1px solid $theme-color-opacity;
				}

				.txt {
					color: $theme-color;
				}
			}
		}
	}
</style>
