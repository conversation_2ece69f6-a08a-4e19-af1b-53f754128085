<template>
	<view :data-theme="theme">
		<view class="navbar acea-row row-around">
			<view class="item acea-row row-center-wrapper" :class="{ on: navOn === 'usable' }" @click="onNav('usable')">未使用</view>
			<view class="item acea-row row-center-wrapper" :class="{ on: navOn === 'unusable' }" @click="onNav('unusable')">已使用</view>
			<view class="item acea-row row-center-wrapper" :class="{ on: navOn === 'overdue' }" @click="onNav('overdue')">已过期</view>
		</view>
		<view class="coupon-box">
			<!-- 优惠券列表 -->
			<view class="coupon-list" v-if="couponsList.length">
				<view
					v-for="(coupon, index) in couponsList"
					:key="index"
					class="coupon-item"
					:class="navOn === 'usable' ? 'available' : (navOn === 'unusable' ? 'used' : 'expired')"
					@click="goCouponsProList(coupon)"
				>
					<!-- 优惠券主体 -->
					<view class="coupon-main">
						<!-- 左侧内容 -->
						<view class="coupon-left">
							<!-- 优惠券图片 -->
							<view class="coupon-image" v-if="coupon.category === 2 && coupon.product && coupon.product.image">
								<image :src="coupon.product.image" mode="aspectFill"></image>
							</view>
							<view class="coupon-image" v-else-if="coupon.image">
								<image :src="coupon.image" mode="aspectFill"></image>
							</view>

							<!-- 优惠券信息 -->
							<view class="coupon-info">
								<view class="coupon-title">
									<span>{{coupon.name}}</span>
								</view>
								<view class="coupon-time">有效期：{{coupon.endTime.slice(0,10)}}</view>
								<view class="coupon-desc">备注：{{coupon.category === 1 ? '商家券' : (coupon.category === 2 ? '商品券' : '平台券')}}</view>
							</view>
						</view>

						<!-- 右侧优惠券面值和按钮 -->
						<view class="coupon-right" :class="navOn === 'usable' ? 'available' : (navOn === 'unusable' ? 'used' : 'expired')">
							<view class="coupon-value">
								<view class="discount" v-if="coupon.couponType === 2">
									{{coupon.discount}}<text style="font-size: 30rpx;"> 折</text>
								</view>
								<view class="money" v-else>
									<text class="currency">¥</text>
									<text>{{coupon.money}}</text>
								</view>
								<view class="condition" v-if="coupon.minPrice > 0">
									满{{coupon.minPrice}}元
								</view>
								<view class="condition" v-else>
									无门槛
								</view>
							</view>

							<view
								class="coupon-btn"
								:class="navOn === 'usable' ? 'available' : (navOn === 'unusable' ? 'used' : 'expired')"
							>
								{{navOn === 'usable' ? '立即使用' : (navOn === 'unusable' ? '已使用' : '已过期')}}
							</view>
						</view>
					</view>
					<!-- 上方半圆缺口 -->
					<view class="circle-top"></view>
					<!-- 下方半圆缺口 -->
					<view class="circle-bottom"></view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class='loadingicon acea-row row-center-wrapper' v-if="couponsList.length > 0 || loading">
			     <text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
			</view>

			<!-- 没有优惠券 -->
			<view class="no-coupons" v-if="!couponsList.length && !loading">
				<view class='noCommodity'>
					<view class='pictrue'>
						<image src='../../../pages/aastatictoT/static/images/noCoupon.png'></image>
					</view>
					<view class="text">暂无优惠券哦~</view>
				</view>
			</view>
		</view>

		<!-- 优惠券中心按钮 -->
		<view class="coupon-center-btn" @click="goCouponCenter">
			优惠券中心
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getUserCoupons
	} from '@/api/api.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		filters: {
		    validStrFilter(status) {
		      const statusMap = {
		        'usable': '立即使用',
		        'unusable': '已用',
				'overdue': '过期',
				'notStart': '未开始'
		      }
		      return statusMap[status]
		    }
		},
		data() {
			return {
				couponsList: [],
				loading: false,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 20,
				navOn: 'usable',
				theme:app.globalData.theme,
			};
		},
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getUseCoupons();
					}
				},
				deep: true
			}
		},
		onLoad() {
			if (this.isLogin) {
				this.getUseCoupons();
			} else {
				toLogin();
			}
		},
		methods: {
			goCouponsProList(item){
				if(this.navOn === 'usable'){
					uni.navigateTo({
						url: '/pages/goods/coupon_goods_list/index?userCouponId=' + item.id + '&minPrice=' + item.minPrice + '&money=' + item.money
					})
				}
			},
			goCouponCenter() {
				uni.navigateTo({
					url: '/pages/coupon/coupon_center/index'
				})
			},
			onNav: function(type) {
				this.navOn = type;
				this.couponsList = [];
				this.page = 1;
				this.loadend = false;
				this.getUseCoupons();
			},

			/**
			 * 获取领取优惠券列表
			 */
			getUseCoupons: function() {
				let that = this;
				if(this.loadend) return false;
				if(this.loading) return false;
				this.loading = true;
				this.loadTitle = '';

				// 根据navOn状态决定API参数
				let apiType = that.navOn;
				if (that.navOn === 'overdue') {
					apiType = 'overdue'; // 拼接overdue
				}

				getUserCoupons({ page: that.page, limit: that.limit, type: apiType}).then(res => {
					let list = res.data ? res.data.list : [];
					let loadend = list.length < that.limit;

					let couponsList = that.$util.SplitArray(list, that.couponsList);
					that.$set(that,'couponsList',couponsList);

					that.loadend = loadend;
					that.page = that.page + 1;
					that.loading = false;
					that.loadTitle = loadend ? '我也是有底线的' : '加载更多';
				}).catch(err=>{
					  that.loading = false;
					  that.loadTitle = '加载更多';
				  });
			}
		},
		/**
		  * 页面上拉触底事件的处理函数
		  */
		 onReachBottom: function () {
		   this.getUseCoupons();
		 }
	}
</script>

<style lang="scss" scoped>
	.coupon-box{
		width: 100%;
		position: absolute;
		padding-bottom: 120rpx;
		padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
		background: #f5f5f5;
		min-height: 100vh;
	}
	.navbar {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 106rpx;
		z-index: 9;

		.item {
			border-top: 5rpx solid transparent;
			border-bottom: 5rpx solid transparent;
			font-size: 30rpx;
			color: #999999;
			position: relative;
			&.on{
				color: #333333;
				font-weight: bold;

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 6rpx;
					background-color: #BDFD5B;
					border-radius: 3rpx;
				}
			}
		}
	}

	/* 优惠券列表 */
	.coupon-list {
		padding: 32rpx 24rpx;
		margin-top: 122rpx;
	}

	.coupon-item {
		position: relative;
		margin-bottom: 32rpx;
		height: 200rpx;
	}

	.coupon-main {
		display: flex;
		background: #fff;
		height: 100%;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
	}

	.coupon-left {
		flex: 1;
		display: flex;
		padding: 32rpx 28rpx;
		align-items: center;
	}

	.coupon-image {
		width: 110rpx;
		height: 110rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-right: 24rpx;
		flex-shrink: 0;
		background: #f8f8f8;
		border: 1rpx solid #eeeeee;
	}

	.coupon-image image {
		width: 100%;
		height: 100%;
	}

	.coupon-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.coupon-title {
		font-size: 30rpx;
		color: #333333;
		font-weight: 600;
		line-height: 1.4;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.coupon-time {
		font-size: 22rpx;
		color: #FF2D18;
		margin-bottom: 30rpx;
		font-weight: 500;
	}

	.coupon-desc {
		font-size: 22rpx;
		color: #888888;
		font-weight: 400;
	}

	.coupon-right {
		width: 220rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #FF5400;
		position: relative;
	}

	.coupon-right.used,
	.coupon-right.expired {
		background: linear-gradient(135deg, #CCCCCC 0%, #B8B8B8 100%);
	}

	.coupon-value {
		text-align: center;
		margin-bottom: 20rpx;
	}

	.discount {
		font-size: 56rpx;
		font-weight: 700;
		color: #fff;
		line-height: 1;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.money {
		color: #fff;
		line-height: 1;
		display: flex;
		align-items: baseline;
		justify-content: center;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.currency {
		font-size: 28rpx;
		margin-right: 4rpx;
		font-weight: 600;
	}

	.money text:not(.currency) {
		font-size: 56rpx;
		font-weight: 700;
	}

	.condition {
		font-size: 22rpx;
		color: #fff;
		margin-top: 8rpx;
		font-weight: 400;
		opacity: 0.9;
	}

	.coupon-btn {
		padding: 12rpx 28rpx;
		border-radius: 28rpx;
		font-size: 26rpx;
		text-align: center;
		min-width: 140rpx;
		font-weight: 600;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.coupon-btn.available {
		background: #fff;
		color: #FF5400;
	}

	.coupon-btn.used {
		background: rgba(255, 255, 255, 0.3);
		color: #fff;
	}

	.coupon-btn.expired {
		background: rgba(255, 255, 255, 0.3);
		color: #fff;
	}

	/* 上下半圆缺口 - 位于白色和橙色交界处 */
	.circle-top,
	.circle-bottom {
		position: absolute;
		left: calc(100% - 220rpx - 10rpx);
		width: 24rpx;
		height: 24rpx;
		background: #f5f5f5;
		border-radius: 50%;
		z-index: 3;
	}

	.circle-top {
		top: -12rpx;
	}

	.circle-bottom {
		bottom: -12rpx;
	}

	/* 分类标签样式 */
	.line-title {
		width: 90rpx;
		height: 40rpx !important;
		line-height: 40rpx !important;
		padding: 2rpx 10rpx;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		opacity: 1;
		border-radius: 20rpx;
		margin-right: 12rpx;
		font-size: 18rpx !important;
	}

	.bg-color-check {
		@include main_color(theme);
		@include coupons_border_color(theme);
	}

	.bg-color-huic {
		background-color: #F5F5F5;
		color: #666666;
		border: 1rpx solid #E5E5E5;
	}

	.main_bg{
		@include main_bg_color(theme);
	}

	.bg_color{
		@include main_bg_color(theme);
	}

	/* 加载更多样式 */
	.loadingicon {
		padding: 40rpx;
		text-align: center;

		.loading {
			margin-right: 10rpx;
		}
	}

	/* 没有优惠券样式 */
	.no-coupons {
		width: 100%;
		height: 100%;
		position: fixed;
		display: flex;
		align-items: center;
		justify-content: center;
		top: -100rpx;

		.noCommodity {
			margin: 0 !important;
			padding: 0 !important;
			text-align: center;

			.pictrue {
				width: 200rpx;
				height: 200rpx;
				margin: 0 auto 20rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.text {
				font-size: 28rpx;
				color: #999999;
			}
		}
	}

	/* 优惠券中心按钮 */
	.coupon-center-btn {
		position: fixed;
		bottom: 0;
		left: 24rpx;
		right: 24rpx;
		height: 88rpx;
		background-color: #BDFD5B;
		color: #222222;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 16rpx;
		z-index: 10;
		padding-bottom: constant(safe-area-inset-bottom);
		// padding-bottom: env(safe-area-inset-bottom);
		margin-bottom: 40rpx;
	}
</style>
