<template>
  <view class="exchange-record">
    <!-- 兑换记录列表 -->
    <view class="exchange-list">
      <view class="exchange-item" v-for="(item, index) in exchangeList" :key="index">
        <view class="item-main">
          <view class="item-left">
            <image :src="item.image" mode="aspectFill" class="item-image"></image>
          </view>
          <view class="item-content">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-info">
              <text class="info-text">下单日期：{{ item.orderDate }}</text>
              <text class="info-quantity">x{{ item.quantity }}</text>
            </view>
          </view>
        </view>
        <view class="item-separator"></view>
        <view class="item-right">
          <view class="item-score">
            <text class="score-label">共{{ item.quantity }}件</text>
            <text class="score-value">合计：<text class="score-number">{{ item.totalScore }}</text>积分</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getIntegralOrderList } from '@/api/user.js';
export default {
  data() {
    return {
      exchangeList: [],
			page: 1,
			limit: 10,
			loading: false,
			loadend: false,
    }
  },
  onLoad() {
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '兑换记录'
    });
    this.loadExchangeData();
  },
	onReachBottom() {
		if(!this.loadend){
			this.loadExchangeData()
		}
	},
  methods: {
    loadExchangeData() {
			if(this.loading || this.loadend) return
			this.loading = true
      getIntegralOrderList({
				page: this.page,
				limit: this.limit
			}).then(res => {
				const list = res.data.list
				const formattedList = list.map(item => {
					const firstOrderInfo = item.orderInfoList && item.orderInfoList.length > 0 ? item.orderInfoList[0] : {};
					return {
							image: firstOrderInfo.image,
							title: firstOrderInfo.productName,
							orderDate: item.payTime,
							quantity: item.totalNum,
							totalScore: item.useIntegral
					};
				});
				this.exchangeList = this.exchangeList.concat(formattedList);
				this.page++;
				this.loadend = list.length < this.limit;
				this.loading = false;
			}).catch(() => {
				this.loading = false;
			});
    }
  }
}
</script>

<style lang="scss" scoped>
.exchange-record {
  background-color: #F5F5F5;
  min-height: 100vh;
}

.exchange-list {
  padding: 20upx;
  
  .exchange-item {
    background-color: #FFFFFF;
    border-radius: 16upx;
    padding: 30upx;
    margin-bottom: 20upx;

    .item-main {
      display: flex;
      margin-bottom: 20upx;
    }

    .item-left {
      margin-right: 20upx;

      .item-image {
        width: 120upx;
        height: 120upx;
        border-radius: 12upx;
      }
    }

    .item-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .item-title {
        font-size: 28upx;
        color: #333333;
        line-height: 40upx;
        margin-bottom: 20upx;
        // 多行文本省略
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
      }

      .item-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .info-text {
          font-size: 24upx;
          color: #999999;
        }

        .info-quantity {
          font-size: 24upx;
          color: #333333;
        }
      }
    }

    .item-separator {
      height: 1upx;
      background-color: #F5F5F5;
      margin: 0 -30upx 20upx -30upx;
    }

    .item-right {
      display: flex;
      justify-content: flex-end;

      .item-score {
        text-align: right;
        display: flex;

        .score-label {
          font-size: 24upx;
          color: #999999;
          display: block;
          margin-bottom: 8upx;
          margin-right: 15upx;
        }

        .score-value {
          font-size: 24upx;
          color: #333333;

          .score-number {
            color: #FF2222;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
