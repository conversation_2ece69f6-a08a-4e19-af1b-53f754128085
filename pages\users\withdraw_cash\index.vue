<template>
	<view :data-theme="theme">
		<view class="container_money">
			<view class="container_money_bg"></view>
			<view class="container_money_account">
				<view class="now_amount">
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view>
							<view style="font-size: 26rpx;color: #999999;">可提现</view>
							<view style="margin-top: 10rpx;font-size: 52rpx;font-weight: 600;">{{statistics.brokerage || 0}}</view>
						</view>
						<navigator url="/pages/users/withdraw_log/index" class="apply_btn"><view>查看提现记录</view><view class="right-icon"></view></navigator>
					</view>
					<view style="display: flex;margin-top: 50rpx;justify-content: space-between;">
						<view class="sale_amount">
							<view style="display: flex;align-items: center;"><text>待入账(元)</text></view>
							<view class="withdraw_amount">{{statistics.freezeBrokerage || 0}}</view>
						</view>
						<view class="sale_amount">
							<view style="display: flex;align-items: center;"><text>提现中(元)</text></view>
							<view class="withdraw_amount">{{statistics.brokeragePriceing || 0}}</view>
						</view>
						<view class="sale_amount">
							<view style="display: flex;align-items: center;"><text>已提现(元)</text></view>
							<view class="withdraw_amount">{{statistics.closingPrice || 0}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="withdraw_title" style="display: flex;align-items: center;;padding: 30rpx 20rpx;">
			<view style="width: 12rpx;height: 30rpx;background-color: #000;"></view>
			<view style="font-size: 30rpx;font-weight: 600;margin-left: 6rpx;">提现方式</view>
			<view style="font-size: 26rpx;color: #B2B2B2;margin-left: 10rpx;">多种提现方式</view>
		</view>
		<view class="withdraw_cash">
			<view class="withdraw_cash_list" >
				<view class="withdraw_cash_left">
					<view style="font-size: 28rpx;display: flex;align-items: center;">到账方式</view>
				</view>
				<view style="display: flex;align-items: center;" @click="onMethod">
					<img v-if="withdrawMethod=='weixin'" src="/static/images/icon_wechat.png" style="width: 40rpx;height: 40rpx;margin-right: 10rpx;" alt="" />
					<view style="font-size: 28rpx;">{{withdrawName?withdrawName:'请选择提现方式'}}</view>
					<view class="right-icon" style="margin-left: 20rpx;"></view>
				</view>
			</view>
		</view>
		<view class="withdraw_title" style="display: flex;align-items: center;;padding: 30rpx 20rpx;">
			<view style="width: 12rpx;height: 30rpx;background-color: #000;"></view>
			<view style="font-size: 30rpx;font-weight: 600;margin-left: 6rpx;">提现方式</view>
			<view style="font-size: 26rpx;color: #B2B2B2;margin-left: 10rpx;">多种提现方式</view>
		</view>
		<view style="padding: 20rpx;">
			<view class="withdraw_number">
				<view style="font-size: 30rpx;color: #999999;padding: 10rpx 0;">提现金额(元)</view>
				<view class="withdraw_amount_input">
					<text>¥</text>
					<input class="uni-input" @input="onKeyInput" :value="price" placeholder-class="uni-input_placeholder" type="digit" placeholder="请输提现金额" />
				</view>
				<view class="withdraw_amount_have">可提现金额{{statistics.brokerage || 0}}</view>
			</view>
		</view>
		<view v-if="statistics.txRatio" style="padding: 32rpx 20rpx;font-size: 26rpx;color: #999999;">注意：您好，提现会收取{{statistics.txRatio}}%手续费（银行收取），实际到账=提现金额-手续费。感谢理解！</view>
		<view class="container_money_footer">
			<button class="but" @click="onWithdraw">立即提现</button>
		</view>
		<!-- 选择提现方式弹框 -->
		<view class="product-window" :class="(cartAttr === true ? 'on' : '')">
			<form @submit="formSubmit">
			<!-- 弹框头部 -->
			<view class="popup-header">
				<view class="popup-title">提现</view>
				<view class="close-btn" @click="closeAttr">
					<text class="iconfont icon-guanbi"></text>
				</view>
			</view>
			<view class="withdraw_type">
				<view class="withdraw_type_check">
					<view>提现到余额</view>
					<view class="check_icon" @click="onCheck('yue')"><img :src="`/static/images/withdraw_${withdrawMethod=='yue'?'check':'nocheck'}.png`" style="width: 40rpx;height: 40rpx;" alt="" /></view>
				</view>
				<view class="withdraw_type_check" :style="{paddingBottom: withdrawMethod=='weixin'?0:'24rpx',borderBottom: withdrawMethod=='weixin'?'none':'1px solid #F5F5F5',display: 'block',fontSize: '26rpx'}">
					<view class="flex">
						<view>提现到微信</view>
						<view class="check_icon" @click="onCheck('weixin')"><img :src="`/static/images/withdraw_${withdrawMethod=='weixin'?'check':'nocheck'}.png`" style="width: 40rpx;height: 40rpx;" alt="" /></view>
					</view>
					<view v-if="withdrawMethod=='weixin'" class="withdraw_weixin">
						<view class="flex withdraw_weixin_list">
							<text>姓名</text>
							<input class="uni-input" name="realName" placeholder="请输入您的真实姓名" />
						</view>
						<view class="flex withdraw_weixin_list">
							<text>账号</text>
							<input class="uni-input" name="wechatNo" placeholder="请输入微信账号" />
						</view>
						<view style="padding: 24rpx 20rpx;">
							<view>收款码</view>
							<view class="flex" style="margin-top: 20rpx;">
								<img v-if="qrcodeUrlW" style="width: 120rpx;height: 120rpx;" :src="qrcodeUrlW" alt="" />
								<img v-else style="width: 120rpx;height: 120rpx;" src="/static/images/upload_img.png" alt="" @click='uploadpic("W")' />
							</view>
						</view>
					</view>
				</view>
				<view class="withdraw_type_check" :style="{paddingBottom: withdrawMethod=='alipay'?0:'24rpx',borderBottom: withdrawMethod=='alipay'?'none':'1px solid #F5F5F5',display: 'block',fontSize: '26rpx'}">
					<view class="flex">
						<view>提现到支付宝</view>
						<view class="check_icon" @click="onCheck('alipay')"><img :src="`/static/images/withdraw_${withdrawMethod=='alipay'?'check':'nocheck'}.png`" style="width: 40rpx;height: 40rpx;" alt="" /></view>
					</view>
					<view v-if="withdrawMethod=='alipay'" class="withdraw_weixin">
						<view class="flex withdraw_weixin_list">
							<text>姓名</text>
							<input class="uni-input" name="realName" placeholder="请输入您的真实姓名" />
						</view>
						<view class="flex withdraw_weixin_list">
							<text>账号</text>
							<input class="uni-input" name="alipayAccount" placeholder="请输入支付宝账号" />
						</view>
						<view style="padding: 24rpx 20rpx;">
							<view>收款码</view>
							<view class="flex" style="margin-top: 20rpx;">
								<img v-if="qrcodeUrlZ" style="width: 120rpx;height: 120rpx;" :src="qrcodeUrlZ" alt="" />
								<img v-else style="width: 120rpx;height: 120rpx;" src="/static/images/upload_img.png" alt="" @click='uploadpic("Z")' />
							</view>
						</view>
					</view>
				</view>
				<view class="withdraw_type_yhk">
					<view style="padding-bottom: 30rpx;">提现到银行卡</view>
					<view class="withdraw_type_yhk_check" v-for="item in bankList" :key="item.id">
						<view>{{`${item.bankName}储蓄卡（${item.bankNo.substring(item.bankNo.length-4,item.bankNo.length)}）`}}</view>
						<view class="check_icon" @click="onCheck('bank'+item.id)"><img :src="`/static/images/withdraw_${withdrawMethod=='bank'+item.id?'check':'nocheck'}.png`" style="width: 40rpx;height: 40rpx;" alt="" /></view>
					</view>
					<view @click="addBank('/pages/users/add_bank/index')" class="flex" style="padding-right: 14rpx;">
						<view style="font-size: 28rpx;font-weight: 600;">添加新卡提现</view><view class="right-icon"></view>
					</view>
				</view>
			</view>
			<button class='but' formType="submit">确认</button>
			</form>
		</view>
		<view class="mask" @touchmove.prevent :hidden="cartAttr === false" @click="closeAttr"></view>
	</view>
</template>

<script>
	import {
		getProductHot
	} from '@/api/product.js';
	import {
		userActivity,
		getMyAccountApi,
		getBillList,
		getRechargeApi,
		rechargeCreateApi,
		settlementList,
		closingConfigApi,
		extractCash,
		transferIn
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		alipayQueryPayResult
	} from '@/api/order.js';
	import recommend from "@/components/base/recommend.vue";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		components: {
			recommend
		},
		data() {
			return {
				statistics: {},
				cartAttr: false,
				withdrawMethod: '',
				weixinSrc: '',
				weixinInfo: {},
				alipayInfo: {},
				bankList: [],
				bankInfo: {},
				price: '',
				withdrawName:'',
				qrcodeUrlW: 'http://wxa.mp.video.tencent-cloud.com/271/20304/stodownload?m=6a604d088817731d9586a7a6f11958c8&filekey=30350201010421301f0202010f0402535a04106a604d088817731d9586a7a6f11958c8020300880c040d00000004627466730000000132&hy=SZ&storeid=26867f32300061b6e000000000000010f00004f50535a27b6bae1e05f536dd&bizid=1023',
				qrcodeUrlZ: 'http://wxa.mp.video.tencent-cloud.com/271/20304/stodownload?m=6a604d088817731d9586a7a6f11958c8&filekey=30350201010421301f0202010f0402535a04106a604d088817731d9586a7a6f11958c8020300880c040d00000004627466730000000132&hy=SZ&storeid=26867f32300061b6e000000000000010f00004f50535a27b6bae1e05f536dd&bizid=1023',
				isCommitted: false //防止多次提交
			};
		},
		computed: mapGetters(['isLogin', 'userInfo']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.userDalance();
					}
				},
				deep: true
			}
		},
		onLoad() {
			let that = this;
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#B8FE4E',
			});
		},
		async onShow() {
			if (this.isLogin) {
				await this.userDalance();
				await this.settlementList()
			} else {
				toLogin();
			}
		},
		onHide() {
			// if(this.cartAttr) return
			// this.cartAttr = false
		},
		methods: {
			uploadpic: function(type) {
				let that = this;
				that.$util.uploadImageOne({
					url: 'upload/image',
					name: 'multipart',
					model: "user",
					pid: 1
			 }, function(res) {
					if (type === 'W') {
						that.qrcodeUrlW = res.data.url;
					} else {
						that.qrcodeUrlZ = res.data.url;
					}
				});
			},
			async onMethod() {
				this.cartAttr = true
			},
			async closeAttr() {
				this.cartAttr = false
			},
			onCheck(check) {
				this.withdrawMethod = check
			},
			// 确认提现方式
			formSubmit(e) {
				if(this.withdrawMethod=='yue'){
					this.withdrawName = '提现到余额'
				}else if(this.withdrawMethod=='weixin'){
					const {realName,wechatNo} = e.detail.value
					if(!this.qrcodeUrlW||!realName||!wechatNo) return
					this.withdrawName = '提现到微信'
					this.weixinInfo = {...e.detail.value,paymentCode: this.qrcodeUrlW}
					console.log(this.weixinInfo)
				}else if(this.withdrawMethod=='alipay'){
					const {realName,alipayAccount} = e.detail.value
					if(!this.qrcodeUrlZ||!realName||!alipayAccount) return
					this.withdrawName = '提现到支付宝'
					this.alipayInfo = {...e.detail.value,paymentCode: this.qrcodeUrlZ}
					console.log(this.alipayInfo)
				}else if(this.withdrawMethod.includes('bank')){
					const bankId = this.withdrawMethod.substring(4,this.withdrawMethod.length)
					this.bankInfo = this.bankList.filter(item=>item.id==bankId)[0]
					this.withdrawName = `${this.bankInfo.bankName}储蓄卡（${this.bankInfo.bankNo.substring(this.bankInfo.bankNo.length-4,this.bankInfo.bankNo.length)}）`
					console.log(this.bankInfo)
				}
				this.cartAttr = false
			},
			// 添加银行卡
			addBank(url) {
				this.cartAttr = false
				uni.navigateTo({
					url: url
				})
			},
			onKeyInput: function(e) {
				let that = this
				let val = e.target.value.replace(/(^\s*)|(\s*$)/g, "")
			   var reg = /[^\d.]/g
			   // 只能是数字和小数点，不能是其他输入
			   val = val.replace(reg, "")
			   // // 保证第一位只能是数字，不能是点
			   val = val.replace(/^\./g, "");
			   // // 小数只能出现1位
			   val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
			   // // 小数点后面保留2位
			   val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
			   that.$nextTick(() => {
				   that.price = val;
			   })
			},
			// 立即提现
			async onWithdraw() {
				let that = this
				let price = that.price
				if(!that.withdrawMethod)return this.$util.Tips({
					title: '请选择提现方式'
				});
				if (parseFloat(price) < 0 || parseFloat(price) == NaN || price == undefined || price == "") {
					return that.$util.Tips({
						title: '请输入金额'
					});
				}
				if (that.price.length == 0) return this.$util.Tips({
					title: '请填写结算金额'
				});
				if (!(/^(\d?)+(\.\d{0,2})?$/.test(that.price))) return this.$util.Tips({
					title: '结算金额保留2位小数'
				});
				if (parseFloat(that.price) < parseFloat(that.statistics.minPrice)) return this.$util.Tips({
					title: '结算金额不能低于' + that.statistics.minPrice
				});
				if(that.withdrawMethod=='yue') {
					uni.showModal({
						title: '转入余额',
						content: '转入余额后无法再次转出，确认是否转入余额',
						success(res) {
							if (res.confirm) {
								transferIn({
									price: parseFloat(price)
								}).then(res => {
									that.$store.commit("changInfo", {
										amount1: 'brokeragePrice',
										amount2: that.$util.$h.Sub(that.userInfo
											.brokeragePrice, parseFloat(price))
									});
									return that.$util.Tips({
										title: '转入成功',
										icon: 'success'
									}, {
										tab: 5,
										url: `/pages/users/withdraw_status/index?status=1&price=${price}`
									});
								}).catch(err => {
									return that.$util.Tips({
										title: err
									});
								})
							} else if (res.cancel) {
								return that.$util.Tips({
									title: '已取消'
								});
							}
						},
					})
				}else{
					if (this.isCommitted == false) {
						this.isCommitted = true;
						const params = {
							bankId: that.bankInfo.id,
							type: that.withdrawMethod.includes('bank')?'bank':that.withdrawMethod,
							closingPrice: that.price,
							realName: that.weixinInfo.realName||that.alipayInfo.realName,
							wechatNo: that.weixinInfo.wechatNo,
							alipayAccount: that.alipayInfo.alipayAccount,
							paymentCode: that.weixinInfo.paymentCode||that.alipayInfo.paymentCode,
						}
						await extractCash(params).then(res => {
							return this.$util.Tips({
								title: "申请成功",
								icon: 'success'
							}, {
								tab: 5,
								url: `/pages/users/withdraw_status/index?status=1&price=${price}`
							});
							this.isCommitted = false;
						}).catch(err => {
							this.isCommitted = false;
							return this.$util.Tips({
								title: err
							});
						});
					}
				}
			},
			// 银行卡列表获取
			async settlementList() {
				uni.showLoading({
					title: '加载中...'
				});
				await settlementList().then(res=>{
					this.bankList = res.data
					uni.hideLoading();
				}).catch(msg => {
					uni.hideLoading();
					return that.$util.Tips({
						title: msg || '银行卡列表获取失败'
					});
				});
			},
			// 获取可提现余额
			async userDalance() {
				await closingConfigApi().then(res => {
					this.statistics = res.data;
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.flex{
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.right-icon{
		width: 14rpx;
		height: 14rpx;
		border-top: 1px solid #666666;
		border-right: 1px solid #666666;
		transform: rotate(45deg);
	}
	.container_money_bg{
		width: 100%;
		height: 292rpx;
		background-image: linear-gradient(#B8FE4E,#F2F8E0);
		position: absolute;
		left: 0;
		top: 0;
	}
	.container_money_account{
		padding: 0 20rpx;
		position: relative;
	}
	.tip_icon{
		margin-left: 8rpx;
		width: 28rpx;
		height: 28rpx;
	}
	.now_amount{
		position: relative;
		margin-top: 24rpx;
		// width: 100%;
		padding: 40rpx 30rpx;
		// height: 292rpx;
		background-color: #fff;
		border-radius: 20rpx;
		.apply_btn{
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #999999;
			.right-icon{
				margin-left: 10rpx;
				width: 14rpx;
				height: 14rpx;
				border-top: 1px solid #666666;
				border-right: 1px solid #666666;
				transform: rotate(45deg);
			}
		}
		.sale_amount{
			position: relative;
			font-size: 24rpx;
			color: #999999;
			.withdraw_amount{
				color: #222222;
				margin-top: 10rpx;
				font-size: 32rpx;
				font-weight: 600;
				text-align: center;
			}
		}
		
	}
	.withdraw_cash{
		padding: 0 20rpx;
		.withdraw_cash_list{
			margin-bottom: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			.withdraw_cash_left{
			}
			.right-icon{
				width: 14rpx;
				height: 14rpx;
				border-top: 1px solid #666666;
				border-right: 1px solid #666666;
				transform: rotate(45deg);
			}
		}
	}
	.withdraw_number{
		background-color: #fff;
		border-radius: 20rpx;
		padding: 20rpx 30rpx;
		.withdraw_amount_input{
			margin-top: 10rpx;
			display: flex;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1px solid #F5F5F5;
			text{
				font-size: 52rpx;
				font-weight: 600;
			}
			.uni-input{
				font-size: 44rpx;
				font-weight: 600;
				margin-left: 10rpx;
			}
			/deep/.uni-input_placeholder{
				color: #999999;
				font-weight: normal;
			}
		}
		.withdraw_amount_have{
			padding: 20rpx 0;
			font-size: 26rpx;
			color: #999999;
		}
	}
	.container_money_footer{
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 0 30rpx 60rpx 30rpx;
		z-index: 10;
		.but{
			color: #222222;
			font-size: 30rpx;
			width: 100%;
			height: 86rpx;
			border-radius: 20rpx;
			margin: 0 auto;
			background-color: #BDFD5B;
			line-height: 86rpx;
		}
	}
	.product-window {
		position: fixed;
		bottom: 0;
		width: 100%;
		left: 0;
		background-color: #fff;
		z-index: 377;
		border-radius: 24rpx 24rpx 0 0;
		padding-bottom: 40rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 40rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
		transform: translate3d(0, 100%, 0);
		transition: all .2s cubic-bezier(0, 0, .25, 1);
		max-height: 80vh;
		overflow: scroll;
		// 弹框头部
		.popup-header {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 30rpx 0;
			position: relative;
			border-bottom: 1px solid #F5F5F5;
			.popup-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333333;
			}
		
			.close-btn {
				position: absolute;
				right: 20rpx;
				top: 30rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid #999999;
				border-radius: 50%;
				.iconfont {
					font-size: 20rpx;
					color: #999999;
				}
			}
		}
		.withdraw_type{
			.withdraw_type_check{
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx;
				border-bottom: 1px solid #F5F5F5;
				.check_icon{
					display: flex;
				}
				.withdraw_weixin{
					background-color: #F5F5F5;margin-top: 30rpx;border-radius: 10rpx;
					.withdraw_weixin_list{
						padding: 24rpx 20rpx;
						border-bottom: 1px solid #fff;
						.uni-input{
							text-align: right;
						}
					}
				}
			}
			.withdraw_type_yhk{
				padding: 30rpx;
				// border-top: 1px solid #F5F5F5;
				border-bottom: 1px solid #F5F5F5;
				.withdraw_type_yhk_check{
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 30rpx;
					font-size: 28rpx;
					font-weight: 600;
				}
			}
		}
		.but {
			// color: #fff;
			font-size: 30rpx;
			width: 86vw;
			height: 86rpx;
			border-radius: 20rpx;
			margin: 50rpx auto 0 auto;
			background-color: #BDFD5B;
			line-height: 86rpx;
		}
	}
	.product-window.on {
		transform: translate3d(0, 0, 0);
	}

</style>
