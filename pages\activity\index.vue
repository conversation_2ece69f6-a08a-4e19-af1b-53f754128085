<template>
	<view class="activity-container">
		<!-- 自定义状态栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="nav-bar">
				<view class="nav-back" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
			</view>
		</view>
		
		<!-- 顶部背景图片 -->
		<view class="header-bg">
			<image v-if="activityType === 'seckill'" src="https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/ea329e63fa7a4cd5acbc5f3b56fa3915gqr7aovcqu.png" mode="widthFix" class="bg-image"></image>
			<image v-else-if="activityType === 'newuser'" src="https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/d4812fcaf41b41f4b0a3054be3239984xp0y70anr6.png" mode="widthFix" class="bg-image"></image>
			<image v-else-if="activityType === 'newproduct'" src="https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/351f0cc77c884cceae695c7da47552cbe1pwe3jxwv.png" mode="widthFix" class="bg-image"></image>
		</view>
		
		<!-- 倒计时模块 -->
		<view :class="['countdown-section', activityType]" v-if="activityType === 'seckill' || activityType === 'newproduct'">
			<view class="countdown-content">
				<text class="countdown-title" v-if="activityType === 'seckill'">限时狂欢！省到尖叫！</text>
				<text class="countdown-title" v-if="activityType === 'newproduct'">新品首发！限时特惠！</text>
				<view class="countdown-timer">
					<view class="time-item">
						<text class="time-number">{{ formatTime(countdown.hours) }}</text>
					</view>
					<text class="time-separator">:</text>
					<view class="time-item">
						<text class="time-number">{{ formatTime(countdown.minutes) }}</text>
					</view>
					<text class="time-separator">:</text>
					<view class="time-item">
						<text class="time-number">{{ formatTime(countdown.seconds) }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 新人活动图片 -->
		<view class="newuser-image-section" v-else-if="activityType === 'newuser'">
			<image src="https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/24/94648d83e5e34413b876ee502ad5e2defzi3qgtx03.png" mode="widthFix" class="huaban-image"></image>
		</view>
		
		<!-- 商品推荐模块 -->
		<view class="products-section" :class="{ 'newuser-bg': activityType === 'newuser', 'newproduct-bg': activityType === 'newproduct' }">
			<view class="products-container">
				<view class="products-wrapper">
					<view class="products-grid">
						<view class="product-item" v-for="(item, index) in productList" :key="index" @click="goToDetail(item)">
							<view class="product-card">
								<view class="product-image-wrapper">
									<image :src="item.image" mode="aspectFill" class="product-image"></image>
								</view>
								<view class="product-info">
									<view class="product-title">{{ item.title }}</view>
									<view class="product-desc" v-if="item.description">{{ item.description }}</view>
									<view class="price-section">
										<text class="current-price">¥{{ item.seckill_price }}</text>
										<text class="original-price" v-if="item.original_price">¥{{ item.original_price }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getActivityList } from "@/api/api.js";

export default {
	data() {
		return {
			activityType: '', // 活动类型：seckill-限时秒杀, newuser-新人专享, newproduct-新品推荐, hotproduct-爆品活动
			countdown: {
				hours: 0,
				minutes: 0,
				seconds: 0
			},
			timer: null,
			endTime: null,
			productList: [], // 商品列表
			statusBarHeight: 0, // 状态栏高度
			// 接口请求参数
			requestParams: {
				activityId: 2,
				type: 1, // 1-折扣，2-秒杀，3-新人
				limit: 10, // 每页数量
				page: 1 // 页码
			}
		}
	},
	onLoad(options) {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;

		// 根据传入参数确定活动类型和活动ID
		this.activityType = options.type || 'seckill';

		// 如果传入了activityId参数，则使用传入的值
		if (options.activityId) {
			this.requestParams.activityId = parseInt(options.activityId);
			console.log('使用传入的activityId：', this.requestParams.activityId);
		}

		// 根据活动类型调用不同的接口或数据
		if (this.activityType === 'seckill' || this.activityType === 'newuser' || this.activityType === 'newproduct') {
			// 根据活动类型设置请求参数（如果没有传入activityId，则使用默认值）
			this.setRequestParams();

			// 调用接口获取活动商品列表
			this.getActivityListData();
		} else {
			// 其他活动类型使用原有的模拟数据
			this.initData();
		}
	},
	

	onUnload() {
		// 清除定时器
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},

		// 根据活动类型设置请求参数
		setRequestParams() {
			// 设置活动类型参数
			switch(this.activityType) {
				case 'seckill':
					this.requestParams.type = 2; // 2-秒杀
					// 如果没有传入activityId，使用默认值
					if (!this.requestParams.activityId) {
						this.requestParams.activityId = 2;
					}
					break;
				case 'newuser':
					this.requestParams.type = 3; // 3-新人
					// 如果没有传入activityId，使用默认值
					if (!this.requestParams.activityId) {
						this.requestParams.activityId = 68;
					}
					break;
				case 'newproduct':
					this.requestParams.type = 1; // 1-折扣
					// 如果没有传入activityId，使用默认值
					if (!this.requestParams.activityId) {
						this.requestParams.activityId = 64;
					}
					break;
				case 'hotproduct':
				default:
					this.requestParams.type = 1; // 1-折扣
					// 如果没有传入activityId，使用默认值
					if (!this.requestParams.activityId) {
						this.requestParams.activityId = 200;
					}
					break;
			}

			console.log('最终请求参数：', this.requestParams);
		},

		// 调用接口获取活动商品列表
		async getActivityListData() {
			try {
				console.log('开始调用接口，请求参数：', this.requestParams);

				const response = await getActivityList(this.requestParams);

				console.log('接口返回结果：', response);

				if (response && response.data) {
					// 设置倒计时结束时间
					if (response.data.overTime) {
						this.setCountdownEndTime(response.data.overTime);
					}

					// 渲染商品列表
					if (response.data.list && response.data.list.list) {
						this.renderProductList(response.data.list.list);
					}
				}

			} catch (error) {
				console.error('获取活动列表失败：', error);
				uni.showToast({
					title: '获取活动数据失败',
					icon: 'none'
				});
				// 如果接口失败，使用原有的模拟数据
				this.initData();
			}
		},

		// 设置倒计时结束时间
		setCountdownEndTime(overTime) {
			try {
				// overTime格式：2025-07-04 01:50:50，直接转换为时间戳
				const endDate = new Date(overTime);
				this.endTime = endDate.getTime();

				console.log('活动结束时间：', overTime);
				console.log('转换后的Date对象：', endDate);
				console.log('转换后的时间戳：', this.endTime);
				console.log('当前时间：', new Date().getTime());
				console.log('当前时间Date对象：', new Date());

				// 秒杀和新品活动启动倒计时，新人活动没有倒计时
				if (this.activityType === 'seckill' || this.activityType === 'newproduct') {
					if (this.timer) {
						clearInterval(this.timer);
					}
					this.updateCountdown();
					this.timer = setInterval(() => {
						this.updateCountdown();
					}, 1000);
				}
			} catch (error) {
				console.error('设置倒计时时间失败：', error);
			}
		},

		// 渲染商品列表
		renderProductList(list) {
			this.productList = list.map(item => ({
				id: item.id,
				title: item.name,
				description: item.intro,
				image: item.image,
				seckill_price: parseFloat(item.activityPrice),
				original_price: parseFloat(item.otPrice)
			}));

			console.log('渲染后的商品列表：', this.productList);
		},

		initData() {
			// 根据活动类型设置商品数据
			if (this.activityType === 'newuser') {
				// 新人专享商品
				this.productList = [
					{
						id: 101,
						title: '新人专享 入门级近视镜',
						description: '新手首选，价格实惠，品质保证',
						image: '/static/images/glasses.png',
						seckill_price: 59.00,
						original_price: 159.00
					},
					{
						id: 102,
						title: '新人福利 防疲劳眼镜',
						description: '缓解眼部疲劳，新用户专属优惠',
						image: '/static/images/model_img.png',
						seckill_price: 89.00,
						original_price: 229.00
					},
					{
						id: 103,
						title: '新人特价 时尚框架镜',
						description: '潮流设计，新人专享超低价',
						image: '/static/images/glasses.png',
						seckill_price: 39.00,
						original_price: 129.00
					},
					{
						id: 104,
						title: '新手推荐 学生款眼镜',
						description: '适合学生使用，新人限时优惠',
						image: '/static/images/model_img.png',
						seckill_price: 69.00,
						original_price: 189.00
					},
					{
						id: 105,
						title: '新人礼包 护眼套装',
						description: '眼镜+护理套装，新人专属组合',
						image: '/static/images/glasses.png',
						seckill_price: 99.00,
						original_price: 299.00
					},
					{
						id: 106,
						title: '新用户专享 高端眼镜',
						description: '高品质镜框，新人体验价',
						image: '/static/images/model_img.png',
						seckill_price: 159.00,
						original_price: 399.00
					}
				];
			} else if (this.activityType === 'newproduct') {
				// 新品推荐商品
				this.productList = [
					{
						id: 201,
						title: '2024新款 智能防蓝光眼镜',
						description: '最新科技防蓝光技术，护眼更进一步',
						image: '/static/images/glasses.png',
						seckill_price: 199.00,
						original_price: 399.00
					},
					{
						id: 202,
						title: '新品首发 超轻钛合金镜框',
						description: '航空级钛合金材质，轻盈坚固',
						image: '/static/images/model_img.png',
						seckill_price: 299.00,
						original_price: 599.00
					},
					{
						id: 203,
						title: '限量新品 变色近视镜',
						description: '智能变色技术，室内外自由切换',
						image: '/static/images/glasses.png',
						seckill_price: 359.00,
						original_price: 699.00
					},
					{
						id: 204,
						title: '新品上市 运动防护眼镜',
						description: '专业运动设计，防冲击防紫外线',
						image: '/static/images/model_img.png',
						seckill_price: 169.00,
						original_price: 329.00
					},
					{
						id: 205,
						title: '新款潮流 大框文艺眼镜',
						description: '2024流行款式，彰显个性品味',
						image: '/static/images/glasses.png',
						seckill_price: 129.00,
						original_price: 259.00
					},
					{
						id: 206,
						title: '创新新品 智能显示眼镜',
						description: '集成智能显示技术，科技感十足',
						image: '/static/images/model_img.png',
						seckill_price: 899.00,
						original_price: 1599.00
					}
				];
			} else {
				// 秒杀活动商品
				this.productList = [
					{
						id: 1,
						title: '超值眼镜框架 时尚潮流款',
						description: '轻盈设计，舒适佩戴，时尚百搭',
						image: '/static/images/glasses.png',
						seckill_price: 99.00,
						original_price: 299.00
					},
					{
						id: 2,
						title: '防蓝光眼镜 护眼神器',
						description: '有效过滤蓝光，保护视力健康',
						image: '/static/images/model_img.png',
						seckill_price: 158.00,
						original_price: 398.00
					},
					{
						id: 3,
						title: '复古圆框眼镜 文艺范',
						description: '复古经典设计，彰显文艺气质',
						image: '/static/images/glasses.png',
						seckill_price: 79.00,
						original_price: 199.00
					},
					{
						id: 4,
						title: '商务金属框架 精英必备',
						description: '精致金属材质，商务场合首选',
						image: '/static/images/model_img.png',
						seckill_price: 288.00,
						original_price: 588.00
					},
					{
						id: 5,
						title: '经典黑框眼镜 百搭款式',
						description: '经典黑色设计，百搭实用',
						image: '/static/images/glasses.png',
						seckill_price: 128.00,
						original_price: 268.00
					},
					{
						id: 6,
						title: '轻奢金属镜框 高端定制',
						description: '高端材质，精工细作，品质之选',
						image: '/static/images/model_img.png',
						seckill_price: 399.00,
						original_price: 799.00
					}
				];
			}
		},
		
		initCountdown() {
			// 只有在没有从接口获取到结束时间时才使用默认时间
			if (!this.endTime) {
				// 设置秒杀结束时间（示例：3小时后）
				this.endTime = new Date().getTime() + 3 * 60 * 60 * 1000;
			}

			// 启动倒计时
			this.updateCountdown();
			this.timer = setInterval(() => {
				this.updateCountdown();
			}, 1000);
		},
		
		updateCountdown() {
			const now = new Date().getTime();
			const distance = this.endTime - now; // 活动结束时间 - 当前时间

			console.log('倒计时计算：', {
				endTime: this.endTime,
				now: now,
				distance: distance,
				endTimeDate: new Date(this.endTime),
				nowDate: new Date(now)
			});

			if (distance > 0) {
				// 计算总小时数（包含天数转换为小时）
				const totalHours = Math.floor(distance / (1000 * 60 * 60));
				this.countdown.hours = totalHours;
				this.countdown.minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
				this.countdown.seconds = Math.floor((distance % (1000 * 60)) / 1000);

				console.log('倒计时结果：', {
					totalHours: totalHours,
					hours: this.countdown.hours,
					minutes: this.countdown.minutes,
					seconds: this.countdown.seconds,
					distance: distance
				});
			} else {
				// 倒计时结束
				console.log('活动已结束');
				this.countdown = { hours: 0, minutes: 0, seconds: 0 };
				if (this.timer) {
					clearInterval(this.timer);
				}
			}
		},
		
		formatTime(time) {
			return time < 10 ? '0' + time : time;
		},
		
		goToDetail(item) {
			uni.navigateTo({
				url: `/pages/goods/goods_details/index?id=${item.id}`
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.activity-container {
	min-height: 100vh;
	position: relative;
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
	
	.status-bar {
		background: transparent;
	}
	
	.nav-bar {
		height: 44px;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		
		.nav-back {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(0, 0, 0, 0.3);
			border-radius: 50%;
			
			.back-icon {
				color: #ffffff;
				font-size: 36rpx;
				font-weight: bold;
			}
		}
	}
}

.header-bg {
	position: relative;
	width: 100%;
	
	.bg-image {
		width: 100%;
		display: block;
	}
}

.countdown-section {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	width: 90%;
	border-radius: 20rpx;
	padding: 20rpx 30rpx;
	z-index: 20;
	top: 32vh; /* 调整到背景图片和商品区域的中间 */

	/* 秒杀活动样式 */
	&.seckill {
		background-color: #FF621E;
		box-shadow: 0 8rpx 30rpx rgba(255, 98, 30, 0.4);
	}

	/* 新品活动样式 */
	&.newproduct {
		background: linear-gradient(90deg, #48cffe 3%, #1a8eff);
		box-shadow: 0 8rpx 30rpx rgba(26, 142, 255, 0.4);
	}
	
	.countdown-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.countdown-title {
			color: #000000;
			font-size: 28rpx;
			font-weight: bold;
		}
		
		.countdown-timer {
			display: flex;
			align-items: center;
			
			.time-item {
				background-color: #020202;
				border-radius: 6rpx;
				padding: 8rpx 12rpx;
				margin: 0 3rpx;
				
				.time-number {
					color: #ffffff;
					font-size: 28rpx;
					font-weight: bold;
				}
			}
			
			.time-separator {
				color: #ffffff;
				font-size: 28rpx;
				font-weight: bold;
				margin: 0 6rpx;
			}
		}
	}
}

.newuser-image-section {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	width: 90%;
	z-index: 20;
	top: 56vh; /* 因为新人活动顶部图片更高，所以位置调整 */
	
	.huaban-image {
		width: 100%;
		display: block;
	}
}

.products-section {
	background: linear-gradient(178deg, #f7ebe2 10%, #fc7d20 98%);
	padding: 40rpx 20rpx 40rpx;
	min-height: calc(100vh - 400rpx);
	
	&.newuser-bg {
		background: linear-gradient(178deg, #fad1d3 10%, #f7ebe2 98%);
	}
	
	&.newproduct-bg {
		background: #F1F7FF;
		padding-top: 85rpx;
	}
	
	.products-container {
		.products-wrapper {
			background-color: #ffffff;
			border-radius: 20rpx;
			padding: 30rpx;
		}
		
		.products-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20rpx;
			
			.product-item {
				.product-card {
					background-color: transparent;
					border-radius: 0;
					overflow: hidden;
					
					.product-image-wrapper {
						position: relative;
						width: 100%;
						height: 300rpx;
						
						.product-image {
							width: 100%;
							height: 100%;
						}
						

					}
					
					.product-info {
						padding: 20rpx;
						
						.product-title {
							font-size: 28rpx;
							color: #333;
							line-height: 1.4;
							margin-bottom: 8rpx;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
						}
						
						.product-desc {
							font-size: 24rpx;
							color: #666666;
							line-height: 1.3;
							margin-bottom: 15rpx;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
							overflow: hidden;
						}
						
						.price-section {
							display: flex;
							align-items: center;
							margin-bottom: 15rpx;
							
							.current-price {
								color: #ff4444;
								font-size: 32rpx;
								font-weight: bold;
								margin-right: 15rpx;
							}
							
							.original-price {
								color: #999;
								font-size: 24rpx;
								text-decoration: line-through;
							}
						}
						

					}
				}
			}
		}
	}
}
</style> 