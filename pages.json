{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white",
				"app-plus": {
					"scrollIndicator": false //禁用原生导航栏
					// #ifdef APP-PLUS
					// "titleNView": {
					// 	"type": "default"
					// }
					// #endif
				}
			}
		},
		{
			"path": "pages/order_addcart/order_addcart",
			"style": {
				"navigationBarTitleText": "购物车"
			}
		},
		{
			"path": "pages/user/index",
			"style": {
				"navigationBarTitleText": "",
				// 个人中心
				// #ifdef MP || APP-PLUS
				"navigationStyle": "custom",
				// "navigationBarTextStyle": "black",
				// "navigationBarBackgroundColor": "#fff",
				// #endif
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		{
			"path": "pages/goods_cate/index",
			"style": {
				"navigationBarTitleText": "商品分类",
				"navigationStyle": "custom",
				"app-plus": {
					// #ifdef APP-PLUS
					"titleNView": {
						"type": "default"
					}
					// #endif
				}
			}
		},
		{
			"path": "pages/activity/index",
			"style": {
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [{
			"root": "pages/users",
			"name": "users",
			"pages": [{
					"path": "invite_friends/index",
					"style": {
						"navigationBarTitleText": "邀请好友",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": {
								"type": "transparent"
							}
						}
					}
				},
				{
					"path": "web_page/index",
					"style": {
						//"navigationBarTitleText": "客服",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_info/index",
					"style": {
						"navigationBarTitleText": "个人资料",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_setting/index",
					"style": {
						// "navigationBarTitleText": "个人信息",
						"navigationBarTitleText": "设置",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "agreement_rules/index",
					"style": {
						"navigationBarTitleText": "隐私协议",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "agreement_info/index",
					"style": {
						"navigationBarTitleText": "",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_get_coupon/index",
					"style": {
						"navigationBarTitleText": "领取优惠券",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_goods_collection/index",
					"style": {
						"navigationBarTitleText": "我的收藏",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_merchant_collection/index",
					"style": {
						"navigationBarTitleText": "我的关注",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_sgin/index",
					"style": {
						"navigationBarTitleText": "签到",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_sgin_list/index",
					"style": {
						"navigationBarTitleText": "签到记录",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				}, {
					"path": "user_sgin_info/index",
					"style": {
						"navigationBarTitleText": "签到说明",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_money/index",
					"style": {
						"navigationBarTitleText": "我的钱包",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "pay_success/index",
					"style": {
						"navigationBarTitleText": "充值至余额",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "my_commission/index",
					"style": {
						"navigationBarTitleText": "我的佣金",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "withdraw_cash/index",
					"style": {
						"navigationBarTitleText": "提现",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "withdraw_log/index",
					"style": {
						"navigationBarTitleText": "提现记录",
						"onReachBottomDistance": 100,
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "withdraw_status/index",
					"style": {
						"navigationBarTitleText": "提现结果",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "withdraw_details/index",
					"style": {
						"navigationBarTitleText": "提现明细",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "commission_details/index",
					"style": {
						"navigationBarTitleText": "",
						"onReachBottomDistance": 100,
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "add_bank/index",
					"style": {
						"navigationBarTitleText": "添加银行卡",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "bank_info/index",
					"style": {
						"navigationBarTitleText": "银行卡",
						"enablePullDownRefresh": false,
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_bill/index",
					"style": {
						"navigationBarTitleText": "账单明细",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_integral/index",
					"style": {
						"navigationBarTitleText": "积分详情",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": {
								"type": "transparent"
							}
						}
					}
				},
				{
					"path": "user_integral_detail/index",
					"style": {
						"navigationBarTitleText": "积分明细",
						"navigationBarBackgroundColor": "#ffffff",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "user_integral_exchange/index",
					"style": {
						"navigationBarTitleText": "兑换记录",
						"navigationBarBackgroundColor": "#ffffff",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "user_integral_details/index",
					"style": {
						"navigationBarTitleText": "积分兑换详情",
						"navigationBarBackgroundColor": "#ffffff",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "user_integral_order/index",
					"style": {
						"navigationBarTitleText": "订单结算",
						"navigationBarBackgroundColor": "#ffffff",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "integral_rules/index",
					"style": {
						"navigationBarTitleText": "积分规则"
					}
				},
				{
					"path": "user_coupon/index",
					"style": {
						"navigationBarTitleText": "我的优惠券",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_spread_user/index",
					"style": {
						"navigationBarTitleText": "我的推广",
						// #ifdef MP || APP-PLUS
						"navigationBarTextStyle": "black",
						"navigationBarBackgroundColor": "#fff",
						// #endif
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_spread_code/index",
					"style": {
						"navigationBarTitleText": "分销海报",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_spread_money/index",
					"style": {
						"navigationBarTitleText": "佣金记录",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_settlement/index",
					"style": {
						"navigationBarTitleText": "结算申请",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_phone/index",
					"style": {
						"navigationBarTitleText": "修改手机号",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_feedback/index",
					"style": {
						"navigationBarTitleText": "意见反馈",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_payment/index",
					"style": {
						"navigationBarTitleText": "余额充值",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_spread_brokerage_out/index",
					"style": {
						"navigationBarTitleText": "佣金转入",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_pwd_edit/index",
					"style": {
						"navigationBarTitleText": "修改密码"
							// #ifdef MP || APP-PLUS
							,
						"navigationBarTextStyle": "#fff"
							// #endif
							,
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "promoter-list/index",
					"style": {
						"navigationBarTitleText": "推广人列表",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "promoter-order/index",
					"style": {
						"navigationBarTitleText": "推广人订单",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "promoter_rank/index",
					"style": {
						"navigationBarTitleText": "推广人排行",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "commission_rank/index",
					"style": {
						"navigationBarTitleText": "佣金排行",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "goods_logistics/index",
					"style": {
						"navigationBarTitleText": "物流信息",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "login/index",
					"style": {
						"navigationBarTitleText": "登录",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "wechat_login/index",
					"style": {
						"navigationBarTitleText": "账户登录",
						"navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "app_login/index",
					"style": {
						"navigationBarTitleText": "绑定手机号",
						"navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				}, {
					"path": "alipay_return/alipay_return",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}
				}, {
					"path": "alipay_invoke/index",
					"style": {
						"navigationBarTitleText": "",
						"enablePullDownRefresh": false
					}

				}, {
					"path": "app_update/app_update",
					"style": {
						"navigationBarTitleText": "检查更新",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				}
			]
		},
		{
			"root": "pages/merchant",
			"name": "merchant",
			"pages": [{
					"path": "home/index",
					"style": {
						"navigationBarTitleText": "商户首页",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false
					}

				},
				{
					"path": "settled/index",
					"style": {
						"navigationBarTitleText": "商户入驻",
						"enablePullDownRefresh": false,
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}

				},
				{
					"path": "application_record/index",
					"style": {
						"navigationBarTitleText": "申请记录",
						"enablePullDownRefresh": false,
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}

				},
				{
					"path": "detail/index",
					"style": {
						"navigationBarTitleText": "商户详情",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}

				},
				{
					"path": "street/index",
					"style": {
						"navigationBarTitleText": "店铺街",
						"enablePullDownRefresh": false,
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}

				}
			]
		},
		{
			"root": "pages/goods",
			"path": "goods",
			"pages": [{
					"path": "goods_details/index",
					"style": {
						"navigationBarTitleText": "商品详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "goods_list/index",
					"style": {
						"navigationBarTitleText": "商品列表",
						//"navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "coupon_goods_list/index",
					"style": {
						"navigationBarTitleText": "商品列表",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "goods_search/index",
					"style": {
						"navigationBarTitleText": "搜索商品",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "news_list/index",
					"style": {
						"navigationBarTitleText": "商学院",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "news_details/index",
					"style": {
						"navigationBarTitleText": "资讯详情",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "browsing_history/index",
					"style": {
						"navigationBarTitleText": "我的足迹",
						"backgroundColor": "#FFFFFF",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_list/index",
					"style": {
						"navigationBarTitleText": "我的订单",
						"navigationBarBackgroundColor": "#F5F5F5",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_details/detail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_logistics/index",
					"style": {
						"navigationBarTitleText": "查看物流",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_logistics/logistics",
					"style": {
						"navigationBarTitleText": "查看物流",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "select_refund_goods/index",
					"style": {
						"navigationBarTitleText": "选择退货商品",
						"navigationBarBackgroundColor": "#f5f5f5",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "refund_apply/index",
					"style": {
						"navigationBarTitleText": "申请售后",
						"navigationBarBackgroundColor": "#f5f5f5",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_confirm/index",
					"style": {
						"navigationBarTitleText": "订单结算",
						// "navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_payment/index",
					"style": {
						"navigationBarTitleText": "支付订单",
						// #ifdef MP || APP-PLUS
						"navigationBarTextStyle": "black",
						"navigationBarBackgroundColor": "#fff",
						// #endif
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "refund_details/index",
					"style": {
						"navigationBarTitleText": "退款订单"
					}
				},
				{
					"path": "aftersale_details/index",
					"style": {
						"navigationBarTitleText": "售后详情",
						"navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "return_logistics_edit/index",
					"style": {
						"navigationBarTitleText": "编辑退货物流",
						"navigationBarBackgroundColor": "#f5f5f5",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "logistics_company_select/index",
					"style": {
						"navigationBarTitleText": "选择物流公司",
						"navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_pay_status/index",
					"style": {
						"navigationBarTitleText": "支付结果",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "order_details/index",
					"style": {
						"navigationBarTitleText": "订单详情",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},

				{
					"path": "user_return_list/index",
					"style": {
						"navigationBarTitleText": "退货列表",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "goods_return/index",
					"style": {
						"navigationBarTitleText": "申请退货",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "goods_comment_con/index",
					"style": {
						"navigationBarTitleText": "商品评价",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "evaluation_list/index",
					"style": {
						"navigationBarTitleText": "评价列表",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "goods_comment_list/index",
					"style": {
						"navigationBarTitleText": "商品评分",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},

				{
					"path": "glasses_virtual_wear/index",
					"style": {
						"navigationBarTitleText": "眼镜试戴",
						"enablePullDownRefresh": false
					}
				},

				{
					"path": "virtual_wear_list/index",
					"style": {
						"navigationBarTitleText": "试戴记录",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/address",
			"name": "address",
			"pages": [{
					"path": "user_address_list/index",
					"style": {
						"navigationBarTitleText": "收货地址",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_address/index",
					"style": {
						"navigationBarTitleText": "添加地址",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				}

			]
		},
		{
			"root": "pages/coupon",
			"name": "coupon",
			"pages": [{
				"path": "coupon_center/index",
				"style": {
					"navigationBarTitleText": "优惠券",
					"app-plus": {
						// #ifdef APP-PLUS
						"titleNView": {
							"type": "default"
						}
						// #endif
					}
				}
			}]
		},
		{
			"root": "pages/optometry",
			"name": "optometry",
			"pages": [{
					"path": "user_optometry_list/index",
					"style": {
						"navigationBarTitleText": "验光单管理",

						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_optometry_details/index",
					"style": {
						"navigationBarTitleText": "验光单详情",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				},
				{
					"path": "user_optometry/index",
					"style": {
						"navigationBarTitleText": "添加验光单",
						"navigationStyle": "custom",
						"app-plus": {
							// #ifdef APP-PLUS
							"titleNView": {
								"type": "default"
							}
							// #endif
						}
					}
				}
			]
		},
		//#ifdef H5
		{
			"root": "pages/auth",
			"name": "pages/auth/index",
			"pages": [{
				"path": "index",
				"style": {
					"navigationBarTitleText": "CRMEB"
				}
			}]
		},
		//#endif
		{
			"root": "pages/home",
			"name": "home",
			"pages": [{
					"path": "merchant-home/merchant-home",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white"
					}
				},
				{
					"path": "merchant-details/merchant-details",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white"
					}
				},
				{
					"path": "search-product/search-product",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"onReachBottomDistance": 150
					}
				},
				{
					"path": "merchant-class/merchant-class",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				}
			]
		}

	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "ooshijie",
		"navigationBarBackgroundColor": "#fff",
		"backgroundColor": "#F8F8F8",
		"titleNView": false,
		"rpxCalcMaxDeviceWidth": 960,
		"rpxCalcBaseDeviceWidth": 375,
		"rpxCalcIncludeWidth": 750
	},
	"tabBar": {
		"color": "#282828",
		"selectedColor": "#42ca4d",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [{
				"pagePath": "pages/index/index",
				"iconPath": "static/tabBar/shouwei.png",
				"selectedIconPath": "static/tabBar/shouxuan.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/goods_cate/index",
				"iconPath": "static/tabBar/fenwei.png",
				"selectedIconPath": "static/tabBar/fenxuan.png",
				"text": "分类"
			},
			{
				"pagePath": "pages/order_addcart/order_addcart",
				"iconPath": "static/tabBar/gouwei.png",
				"selectedIconPath": "static/tabBar/gouxuan.png",
				"text": "购物车"
			},
			{
				"pagePath": "pages/user/index",
				"iconPath": "static/tabBar/wowei.png",
				"selectedIconPath": "static/tabBar/woxuan.png",
				"text": "我的"
			}
		]
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}