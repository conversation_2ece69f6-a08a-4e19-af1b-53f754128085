<template>
	<view :data-theme="theme">
		<view class="container_money">
			<view class="container_money_bg"></view>
			<view class="add_bank">
				<view style="padding: 30rpx 0;font-size: 32rpx;font-weight: 600;">添加银行卡 继续提现</view>
				<view style="font-size: 26rpx;color: #999999;">提现更方便</view>
				<view class="bank_info">
					<form @submit="formSubmit">
					<view class="flex bank_info_list">
						<text>银行卡号</text>
						<input class="uni-input" name="bankNo" type="number" placeholder="请输入银行卡号" />
					</view>
					<view class="flex bank_info_list">
						<text>所属银行</text>
						<view style="width: 400rpx;text-align: right;">
							<picker @change="bindPickerChange" :value="arrayIndex" :range="array">
								<view class="uni-input">{{arrayIndex==null?'请选择所属银行':array[arrayIndex]}}</view>
							</picker>
						</view>
						<input :hidden="true" class="uni-input" name="bankName" :value="array[arrayIndex]" />
					</view>
					<view class="flex bank_info_list">
						<text>开户行支行</text>
						<input class="uni-input" name="bankBranch" placeholder="请输入开户行支行" />
					</view>
					<view class="flex bank_info_list" style="border: none;">
						<text>开户行姓名</text>
						<input class="uni-input" name="bnakUserName" placeholder="请输入此卡开户人姓名" />
					</view>
					<view class="container_money_footer">
						<button class='but' formType="submit">确定</button>
					</view>
					</form>
				</view>
			</view>
		</view>
		<view style="padding: 20rpx;font-size: 26rpx;color: #999999;">注意：请准确填写储蓄卡信息（仅限银联，非信用卡），确保佣金即时到账。</view>
	</view>
</template>

<script>
	import {
		getProductHot
	} from '@/api/product.js';
	import {
		userActivity,
		getMyAccountApi,
		getBillList,
		getRechargeApi,
		rechargeCreateApi,
		bankList,
		addSettlement
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		alipayQueryPayResult
	} from '@/api/order.js';
	import recommend from "@/components/base/recommend.vue";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		components: {
			recommend
		},
		data() {
			return {
				statistics: {},
				array: [],
				arrayIndex: null
			};
		},
		computed: mapGetters(['isLogin', 'userInfo']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.userDalance();
					}
				},
				deep: true
			}
		},
		async onLoad() {
			let that = this;
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#B8FE4E',
			});
			await that.getBankList()
		},
		onShow() {
			// if (this.isLogin) {
			// 	this.userDalance();
			// } else {
			// 	toLogin();
			// }
		},
		methods: {
			withdrawCash(url) {
				uni.navigateTo({
					url: url
				})
			},
			bindPickerChange: function(e) {
				this.arrayIndex = e.detail.value
			},
			async getBankList() {
				await bankList().then(res => {
					this.array = res.data;
				})
			},
			async formSubmit(e) {
				let that = this;
				uni.showLoading({
					title: '加载中...'
				});
				// console.log(e.detail.value)
				await addSettlement({...e.detail.value}).then(res=>{
					uni.hideLoading();
					that.$util.Tips({
						title: '保存成功',
						icon: 'success'
					});
					setTimeout(function() {
						uni.navigateBack()
					}, 2000);
				}).catch(msg => {
					uni.hideLoading();
					return that.$util.Tips({
						title: msg || '保存失败，您并没有修改'
					});
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.flex{
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.container_money{
		position: relative;
		.container_money_bg{
			width: 100%;
			height: 292rpx;
			background-image: linear-gradient(#B8FE4E,#F5F5F5);
			position: absolute;
			left: 0;
			top: 0;
		}
		.add_bank{
			position: relative;
			z-index: 5;
			padding: 0 20rpx;
			.bank_info{
				margin-top: 30rpx;
				background-color: #fff;
				padding-left: 20rpx;
				border-radius: 10rpx;
				.bank_info_list{
					padding: 30rpx 20rpx 30rpx 0;
					border-bottom: 1px solid #F5F5F5;
					.uni-input{
						text-align: right;
					}
				}
			}
		}
	}
	.container_money_footer{
		position: fixed;
		bottom: 68rpx;
		left: 0;
		right: 0;
		z-index: 10;
		background-color: #F5F5F5;
		.but{
			color: #222222;
			font-size: 30rpx;
			width: 90vw;
			height: 86rpx;
			border-radius: 20rpx;
			margin: 50rpx auto 0 auto;
			background-color: #BDFD5B;
			line-height: 86rpx;
		}
	}
</style>
