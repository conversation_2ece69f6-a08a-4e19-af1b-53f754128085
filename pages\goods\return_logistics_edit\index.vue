<template>
	<view class="return-logistics-edit-page" :data-theme="theme">
		<!-- 物流信息卡片 -->
		<view class="logistics-info-card">
			<view class="card-title">物流信息</view>
			
			<!-- 物流单号 -->
			<view class="info-item">
				<text class="item-label">物流单号</text>
				<view class="item-content">
					<input 
						type="text" 
						v-model="logisticsNo" 
						placeholder="请填写" 
						class="logistics-input"
					/>
					<view class="scan-icon" @click="scanCode">
						<image src="/static/images/scan_icon.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 物流公司 -->
			<view class="info-item">
				<text class="item-label">物流公司</text>
				<view class="item-content" @click="selectCompany">
					<text class="company-text" :class="{ 'placeholder': !selectedCompany }">
						{{ loading ? '加载中...' : (selectedCompany || '请选择') }}
					</text>
					<text class="iconfont icon-xiangyou" v-if="!loading"></text>
				</view>
			</view>
		</view>
		
		<!-- 确定按钮 -->
		<view class="confirm-btn" @click="confirmLogistics">确定</view>
	</view>
</template>

<script>
	import { getExpressList, submitRefundLogistics } from '@/api/order.js';

	export default {
		data() {
			return {
				theme: 'default',
				refundOrderNo: '', // 退款订单号
				logisticsNo: '', // 物流单号
				selectedCompany: '', // 选择的物流公司名称
				selectedCompanyId: '', // 选择的物流公司ID
				// 物流公司列表（从API获取）
				companiesList: [],
				// 加载状态
				loading: false
			};
		},
		onLoad(options) {
			console.log('页面加载，接收参数：', options);

			// 接收退款订单号参数
			if (options.refundOrderNo) {
				this.refundOrderNo = options.refundOrderNo;
				console.log('接收到退款订单号：', this.refundOrderNo);
			} else {
				console.error('未接收到退款订单号参数');
				uni.showToast({
					title: '参数错误',
					icon: 'none'
				});
			}

			// 页面加载时获取物流公司列表
			this.loadExpressList();
		},
		onShow() {
			// 监听从物流公司选择页面返回的数据
			try {
				const selectedCompanyData = uni.getStorageSync('selectedLogisticsCompany');
				if (selectedCompanyData) {
					const companyData = JSON.parse(selectedCompanyData);
					this.selectedCompany = companyData.name;
					this.selectedCompanyId = companyData.id;

					console.log('从选择页面返回的物流公司：', companyData);

					// 清除存储的数据，避免重复处理
					uni.removeStorageSync('selectedLogisticsCompany');
				}
			} catch (e) {
				console.log('获取选择的物流公司数据失败：', e);
			}
		},
		methods: {
			// 加载物流公司列表
			loadExpressList() {
				if (this.loading) return;

				this.loading = true;
				console.log('加载物流公司列表...');

				getExpressList({
					limit: 100, // 获取足够多的物流公司
					page: 1
				}).then(res => {
					this.loading = false;
					console.log('物流公司列表接口返回数据：', res);

					if (res.code === 200 && res.data && res.data.list) {
						console.log('原始物流公司数据：', res.data.list);
						console.log('数据长度：', res.data.list.length);

						// 先检查数据结构
						if (res.data.list.length > 0) {
							console.log('第一个物流公司数据结构：', res.data.list[0]);
						}

						// 暂时不过滤，直接使用所有数据来测试
						this.companiesList = res.data.list.map(item => ({
							id: item.id,
							name: item.name || '未知物流公司',
							code: item.code || '',
							isShow: item.isShow,
							status: item.status
						}));

						console.log('处理后的物流公司列表：', this.companiesList);
						console.log('物流公司数量：', this.companiesList.length);
					} else {
						uni.showToast({
							title: res.message || '获取物流公司列表失败',
							icon: 'none'
						});

						// 如果接口失败，使用默认的物流公司列表
						this.setDefaultCompaniesList();
					}
				}).catch(err => {
					this.loading = false;
					console.error('物流公司列表接口调用失败：', err);

					uni.showToast({
						title: '网络异常，使用默认列表',
						icon: 'none'
					});

					// 如果接口失败，使用默认的物流公司列表
					this.setDefaultCompaniesList();
				});
			},

			// 设置默认物流公司列表（备用方案）
			setDefaultCompaniesList() {
				this.companiesList = [
					{ id: 1, name: '顺丰速运', code: 'SF' },
					{ id: 2, name: '圆通速递', code: 'YTO' },
					{ id: 3, name: '中通快递', code: 'ZTO' },
					{ id: 4, name: '申通快递', code: 'STO' },
					{ id: 5, name: '韵达速递', code: 'YD' },
					{ id: 6, name: '百世汇通', code: 'HTKY' },
					{ id: 7, name: '德邦快递', code: 'DBL' },
					{ id: 8, name: '京东物流', code: 'JD' },
					{ id: 9, name: '邮政EMS', code: 'EMS' }
				];
			},

			// 扫码获取物流单号
			scanCode() {
				console.log('扫码获取物流单号');
				// #ifdef APP-PLUS
				// 可以调用扫码功能
				// #endif
				
				// 模拟扫码结果
				uni.showToast({
					title: '扫码功能',
					icon: 'none'
				});
			},
			
			// 选择物流公司
			selectCompany() {
				console.log('点击选择物流公司');
				console.log('当前物流公司列表：', this.companiesList);
				console.log('物流公司列表长度：', this.companiesList.length);

				if (this.companiesList.length === 0) {
					console.log('物流公司列表为空');
					uni.showToast({
						title: '物流公司列表为空，请稍后重试',
						icon: 'none'
					});
					return;
				}

				// 直接显示常用物流公司选择
				this.showCompanyListModal();
			},

			// 备用方案：显示物流公司列表弹窗
			showCompanyListModal() {
				console.log('开始显示物流公司选择弹窗');

				// 显示常用的几个物流公司供快速选择
				const commonCompanies = this.companiesList.filter(item =>
					['顺丰速运', '中通快递', '圆通速递', '申通快递', '韵达快递', '百世快递'].includes(item.name)
				);

				console.log('找到的常用物流公司：', commonCompanies);
				console.log('常用物流公司数量：', commonCompanies.length);

				if (commonCompanies.length > 0) {
					const companyNames = commonCompanies.map(item => item.name);
					console.log('常用物流公司名称列表：', companyNames);

					uni.showActionSheet({
						itemList: [...companyNames, '查看更多物流公司'],
						success: (res) => {
							console.log('用户选择了索引：', res.tapIndex);
							if (res.tapIndex < companyNames.length) {
								// 选择了常用物流公司
								const selectedItem = commonCompanies[res.tapIndex];
								this.selectedCompany = selectedItem.name;
								this.selectedCompanyId = selectedItem.id;

								console.log('选择的物流公司：', {
									id: this.selectedCompanyId,
									name: this.selectedCompany,
									code: selectedItem.code
								});
							} else {
								// 选择了"查看更多"，跳转到物流公司选择页面
								console.log('用户选择查看更多，跳转到选择页面');
								this.goToCompanySelectPage();
							}
						},
						fail: (res) => {
							console.log('显示ActionSheet失败：', res);
							// 如果ActionSheet失败，直接跳转到选择页面
							this.goToCompanySelectPage();
						}
					});
				} else {
					console.log('没有找到常用物流公司，直接跳转到选择页面');
					// 如果没有常用物流公司，直接跳转到选择页面
					this.goToCompanySelectPage();
				}
			},

			// 跳转到物流公司选择页面
			goToCompanySelectPage() {
				console.log('准备跳转到物流公司选择页面');
				console.log('要传递的物流公司数据长度：', this.companiesList.length);

				// 将物流公司列表数据传递到选择页面
				const companiesData = encodeURIComponent(JSON.stringify(this.companiesList));
				console.log('编码后的数据长度：', companiesData.length);

				uni.navigateTo({
					url: `/pages/goods/logistics_company_select/index?companies=${companiesData}`,
					success: () => {
						console.log('跳转成功');
					},
					fail: (err) => {
						console.log('跳转失败：', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 确定提交
			async confirmLogistics() {
				// 验证退款订单号
				if (!this.refundOrderNo) {
					uni.showToast({
						title: '退款订单号不能为空',
						icon: 'none'
					});
					return;
				}

				// 验证物流单号
				if (!this.logisticsNo.trim()) {
					uni.showToast({
						title: '请输入物流单号',
						icon: 'none'
					});
					return;
				}

				// 验证物流公司
				if (!this.selectedCompany) {
					uni.showToast({
						title: '请选择物流公司',
						icon: 'none'
					});
					return;
				}

				try {
					uni.showLoading({
						title: '提交中...'
					});

					const submitData = {
						expressName: this.selectedCompany,
						refundOrderNo: this.refundOrderNo,
						trackingNumber: this.logisticsNo.trim()
					};

					console.log('提交物流信息：', submitData);

					await submitRefundLogistics(submitData);

					uni.hideLoading();
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});

					// 返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);

				} catch (error) {
					uni.hideLoading();
					console.error('提交物流信息失败：', error);
					uni.showToast({
						title: error.message || '提交失败',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.return-logistics-edit-page {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 30rpx;
	}
	
	.logistics-info-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 50rpx;
		
		.card-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 40rpx;
		}
		
		.info-item {
			display: flex;
			align-items: center;
			margin-bottom: 40rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.item-label {
				font-size: 28rpx;
				color: #333;
				width: 160rpx;
				flex-shrink: 0;
			}
			
			.item-content {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.logistics-input {
					flex: 1;
					font-size: 28rpx;
					color: #333;
                    text-align: right;
					
					&::placeholder {
						color: #999;
					}
				}
				
				.scan-icon {
					width: 40rpx;
					height: 40rpx;
					margin-left: 20rpx;
					
					image {
						width: 100%;
						height: 100%;
					}
				}
				
				.company-text {
					flex: 1;
					font-size: 28rpx;
					color: #333;
					text-align: right;
					margin-right: 20rpx;
					
					&.placeholder {
						color: #999;
					}
				}
				
				.iconfont {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.confirm-btn {
		position: fixed;
		bottom: 50rpx;
		left: 30rpx;
		right: 30rpx;
		height: 90rpx;
		background-color: #BDFD5B;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #333;
		font-weight: 600;
	}
</style> 