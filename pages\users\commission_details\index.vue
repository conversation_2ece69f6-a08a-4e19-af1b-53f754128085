<template>
	<view :data-theme="theme">
		<view class="container_money">
			<view>
				<picker mode="date" :value="date" :start="startDate" :end="endDate" fields="month" @change="bindDateChange">
					<view style="display: flex;font-weight: 600;font-size: 44rpx;">
						<view class="uni-input">{{yearMonth.substring(5,yearMonth.length)}}<text style="font-weight: 600;font-size: 28rpx;">月</text></view>
						<view class="bottom-icon"></view>
					</view>
				</picker>
			</view>
			<view v-if="pageType==1" class="flex" style="font-weight: 600;font-size: 26rpx;margin-top: 20rpx;">
				<view>佣金总收入：¥{{allPrice}}</view>
				<view>今日佣金：¥{{nowDayPrice}}</view>
			</view>
			<view v-if="pageType==2" class="flex" style="font-weight: 600;font-size: 26rpx;margin-top: 20rpx;">
				<view>总销售额：¥{{allSales}}</view>
				<view>今日销售额：¥{{nowDaySales}}</view>
			</view>
			<view class="order_list" v-for="item in list" :key="item.id">
				<view class="flex" style="padding: 20rpx 0;border-bottom: 1px solid #EFEFEF;">
					<view style="font-size: 26rpx;">{{item.linkNo}}</view>
					<view style="font-size: 24rpx;color: #999999;">{{item.createTime}}</view>
				</view>
				<view class="order_list_goods" v-for="goods in item.order['orderDetailList']" :key="goods.id">
					<view class="flex" style="border-radius: 20rpx;overflow: hidden;"><img style="width: 180rpx;height: 180rpx;" :src="goods.image" alt="" /></view>
					<view style="flex: 1;margin-left: 20rpx;">
						<view style="font-size: 26rpx;">{{goods.productName}}</view>
						<view class="flex" style="font-size: 24rpx;margin-top: 15rpx;">
							<view class="sku">{{goods.sku}}</view>
							<view>X{{goods.deliveryNum}}</view>
						</view>
						<view style="font-size: 28rpx;font-weight: 600;margin-top: 25rpx;">¥{{goods.price||0}}</view>
					</view>
				</view>
				<view class="flex order_list_user">
					<view class="flex">
						<view class="flex" style="border-radius: 50%;overflow: hidden;"><img style="width: 60rpx;height: 60rpx;" :src="item.avatar" alt="" /></view>
						<view style="margin-left: 20rpx;font-size: 26rpx;">{{item.phone}}</view>
					</view>
					<view style="font-size: 26rpx;font-weight: 600;">销售总计：¥{{item.price}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getProductHot
	} from '@/api/product.js';
	import {
		userActivity,
		getMyAccountApi,
		getBillList,
		getRechargeApi,
		rechargeCreateApi,
		brokerageLog
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import recommend from "@/components/base/recommend.vue";
	let app = getApp();
	export default {
		components: {
			recommend
		},
		data() {
			return {
				loading: true,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 20,
				yearMonth: '',
				list: [],
				allPrice: 0,
				allSales: 0,
				nowDayPrice: 0,
				nowDaySales: 0,
				pageType: null
			};
		},
		computed: {
			...mapGetters({
				isLogin: 'isLogin',
				userInfo: 'userInfo'
			}),
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		onLoad(options) {
			let that = this;
			that.pageType=options.type
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#f5f5f5'
			});
			uni.setNavigationBarTitle({
				title: options.type==1?'佣金明细':'销售明细'
			});
			that.month()
		},
		onShow() {
			if (this.isLogin) {
				this.getUserBillList();
			} else {
				toLogin();
			}
		},
		async onReachBottom() {
		// 触发加载更多逻辑
			await this.getUserBillList()
		},
		methods: {
			async month() {
				var now = new Date();
				var year = now.getFullYear();
				var month = now.getMonth()+1; //月份从0开始，所以需要加1
				month = month > 9 ? month : '0' + month;
				this.yearMonth = year + '-'+ month
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();
	
				if (type === 'start') {
					year = year - 1;
				} else if (type === 'end') {
					// year = year + 10;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			bindDateChange: function(e) {
				console.log(e)
				if(this.yearMonth == e.detail.value) return
				this.yearMonth = e.detail.value;
				this.page = 1;
				this.loadend = false;
				this.list = []
				this.getUserBillList();
			},
			/**
			 * 获取明细
			 */
			getUserBillList: function() {
				let that = this;
				if (that.loadend) return;
				that.loading = true;
				that.loadTitle = "";
				let data = {
					page: that.page,
					limit: that.limit,
					month: that.yearMonth
				}
				brokerageLog(data).then(function(res) {
					const { allPrice, allSales, nowDayPrice, nowDaySales, page } = res.data
					that.allPrice= allPrice||0
					that.allSales= allSales||0
					that.nowDayPrice= nowDayPrice||0
					that.nowDaySales= nowDaySales||0
					that.list = that.list.concat(page.list)
					that.loadend = page.pages <= that.page;
					that.page += 1;
					that.loading = false;
					that.loadTitle = that.loadend ? "我也是有底线的~" : "加载更多";
				}, function(res) {
					that.loading = false;
					that.loadTitle = '加载更多';
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.flex{
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.bottom-icon{
		margin-top: 24rpx;
		margin-left: 10rpx;
		width: 15rpx;
		height: 15rpx;
		border-top: 2px solid #666666;
		border-right: 2px solid #666666;
		transform: rotate(135deg);
	}
	.container_money{
		padding: 30rpx;
		.order_list{
			background-color: #fff;
			margin-top: 20rpx;
			border-radius: 20rpx;
			padding: 0 20rpx 20rpx 20rpx;
			.order_list_goods{
				padding: 20rpx 0;
				display: flex;
				justify-content: space-between;
				.sku{
					background-color: #F5F5F5;
					color: #666666;
					border-radius: 10rpx;
					padding: 4rpx 8rpx;
					font-size: 22rpx;
				}
			}
			.order_list_user{
				// margin-top: 20rpx;
				background-color: #F5F5F5;
				border-radius: 10rpx;
				padding: 20rpx;
			}
		}
	}
</style>
