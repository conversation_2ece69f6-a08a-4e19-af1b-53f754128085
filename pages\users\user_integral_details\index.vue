<template>
  <view class="integral-details">
    <!-- 商品轮播图 -->
    <view class="swiper-container">
      <swiper class="swiper" :indicator-dots="true" :autoplay="productImages.length > 1" :interval="3000" :duration="1000">
        <swiper-item v-for="(image, index) in productImages" :key="index">
          <image :src="image" mode="aspectFill" class="swiper-image" @error="handleImageError"></image>
        </swiper-item>
      </swiper>
      <view class="image-counter" v-if="productImages.length > 0">{{ currentImageIndex + 1 }}/{{ productImages.length }}</view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="product-info-card">
      <!-- 渐变背景区域：包含标签页切换、价格、标题、描述 -->
      <view class="gradient-section">
        <!-- 标签页切换 -->
        <view class="tab-header">
          <view class="tab-left">
            <view class="tab-item" :class="activeTab === 'info' ? 'active' : ''" @click="switchTab('info')">
              商品信息
            </view>
            <view class="tab-item" :class="activeTab === 'reviews' ? 'active' : ''" @click="switchTab('reviews')" v-if="showReviewTab">
              相关评论
            </view>
          </view>
        </view>

        <!-- 商品信息内容 -->
        <view v-if="activeTab === 'info'" class="info-content">
          <!-- 积分价格区域 -->
          <view class="price-section">
            <view class="price-row">
              <view class="price-left">
                <view class="integral-price">
                  <text class="price-number">{{ productInfo.integralPrice }}</text>
                  <text class="price-unit">积分</text>
                  <text class="original-integral" v-if="productInfo.integral">可用积分{{ productInfo.integral }}</text>
                </view>
              </view>
              <view class="stock-info">
                {{ productInfo.type === 1 ? `仅剩${productInfo.stock}张` : `仅剩${productInfo.stock}件` }}
              </view>
            </view>
          </view>

          <!-- 商品标题 -->
          <view class="product-title">
            {{ productInfo.title }}
          </view>

          <!-- 商品描述 -->
          <view class="product-desc">
            {{ productInfo.subtitle }}
          </view>
        </view>

        <!-- 相关评论内容 -->
        <view v-if="activeTab === 'reviews' && showReviewTab" class="comment-content">
          <view class="comment-summary">
            <view class="praise-rate">
              好评率 {{ replyChance || 0 }}%
            </view>
            <navigator v-if="productDetail.product && productDetail.product.id" class="view-all"
              :url='"/pages/goods/goods_comment_list/index?productId=" + productDetail.product.id'>
              查看全部评论
              <text class="iconfont icon-jiantou"></text>
            </navigator>
          </view>
          <view class="comment-list">
            <!-- 有评论时显示评论内容 -->
            <view class="comment-item-wrapper" v-if="reply && reply.length > 0">
              <view class="comment-item">
                <!-- 头像和用户信息行 -->
                <view class="user-header-row">
                  <view class="user-avatar">
                    <image :src="reply[0].avatar || '/static/images/mermoren.png'"
                      mode="aspectFill"></image>
                  </view>
                  <view class="user-info-right">
                    <view class="user-first-line">
                      <view class="username">{{ reply[0].nickname }}</view>
                      <view class="user-specs">规格：{{ reply[0].sku }}</view>
                    </view>
                    <view class="comment-date">{{ reply[0].add_time }}</view>
                  </view>
                </view>

                <!-- 星标评分 -->
                <view class="star-rating">
                  <text class="star" v-for="n in 5" :key="n"
                    :class="n <= (reply[0].product_score || 3) ? 'active' : ''">★</text>
                </view>

                <!-- 评论内容 -->
                <view class="comment-text">
                  {{ reply[0].comment }}
                </view>

                <!-- 商家回复 -->
                <view class="merchant-reply" v-if="reply[0].merchantReplyContent">
                  <view class="reply-label">商家回复：</view>
                  <view class="reply-content">{{ reply[0].merchantReplyContent }}</view>
                </view>
              </view>
            </view>

            <!-- 无评论状态 -->
            <view class="no-comment-wrapper" v-else>
              <view class="no-comment-text">暂无评论</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他模块区域：详情 -->
      <view v-if="activeTab === 'info'" class="other-modules">
        <!-- 详情模块 -->
        <view class="info-module detail-info-module">
          <view class="detail-header-section">
            <view class="detail-header-row">
              <view class="detail-title">详情</view>
            </view>
          </view>

          <!-- 详情内容 -->
          <view class="detail-content">
            <rich-text class="detail-content-rich-text" :nodes="productInfo.detail"></rich-text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button 
        class="exchange-btn" 
        :class="{ 'insufficient': productInfo.integral < productInfo.integralPrice }" 
        @click="handleExchange"
        :disabled="productInfo.integral < productInfo.integralPrice"
      >
        {{ productInfo.integral < productInfo.integralPrice ? '积分不足' : '立即兑换' }}
      </button>
    </view>

    <!-- 规格选择弹窗 -->
    <integralProductWindow 
      v-if="!isGlassesProduct"
      :isVisible="showSpecWindow"
      :productInfo="productInfo"
      :productValue="productValue"
      :productAttr="productAttr"
      @confirm="onSpecConfirm"
      @close="showSpecWindow = false"
    />
    
    <integralGlassesWindow 
      v-if="isGlassesProduct"
      :isVisible="showSpecWindow"
      :productInfo="productInfo"
      :productValue="productValue"
      :productAttr="productAttr"
      :merId="productDetail.product ? productDetail.product.merId : 0"
      @confirm="onSpecConfirm"
      @close="showSpecWindow = false"
    />
  </view>
</template>

<script>
import { getIntegralDetail } from '@/api/user.js'
import { getCategoryList, getReplyProduct, getReplyConfig } from '@/api/product.js'
import integralProductWindow from '@/components/integralProductWindow/index.vue'
import integralGlassesWindow from '@/components/integralGlassesWindow/index.vue'
import { mapGetters } from "vuex"

export default {
  components: {
    integralProductWindow,
    integralGlassesWindow
  },
  data() {
    return {
      productId: '',
      activeTab: 'info',
      currentImageIndex: 0,
      replyChance: 95, // 好评率
      productImages: [],
      productInfo: {
        title: '',
        subtitle: '',
        integralPrice: 0,
        integral: 0, // 用户可用积分
        originalIntegral: 0,
        stock: 0,
        detail: '',
        type: 0, // 1-优惠券 2-实物商品
        name: '',
        image: ''
      },
      reply: [], // 评论列表
      replyCount: 0, // 总评论数量
      showReviewTab: true, // 控制是否显示相关评论tab
      loading: false,
      // 规格选择相关
      showSpecWindow: false,
      productDetail: {},
      productValue: {},
      productAttr: [],
      isGlassesProduct: false,
      glassesSubCategoryIdList: []
    }
  },
  computed: {
    ...mapGetters(['productCategoryTree']),
  },
  watch: {
    productCategoryTree: {
      handler: function (categoryList) {
        if (categoryList) {
          let glassesSubCategoryIds = [];
          for (var i = 0; i < categoryList.length; i++) {
            if (categoryList[i].name.trim() == "眼镜") {
              let childList = categoryList[i].childList;
              for (var j = 0; j < childList.length; j++) {
                if (childList[j].name.trim() == "光学眼镜架") {
                  let subChildList = childList[j].childList;
                  for (var k = 0; k < subChildList.length; k++) {
                    glassesSubCategoryIds.push(subChildList[k].id)
                  }
                }
              }
            }
          }
          this.glassesSubCategoryIdList = glassesSubCategoryIds;
          this.checkIfGlassesProduct();
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (!this.productCategoryTree) {
      getCategoryList().then(res => {
        this.$store.commit('PRODUCTCATEGORYTREE', res.data)
      })
    }
  },
  onLoad(options) {
    if (options.id) {
      this.productId = options.id;
      this.loadProductDetails();
    }
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '积分兑换详情'
    });
  },
  methods: {
    // 切换标签页
    switchTab(tab) {
      // 如果是优惠券类型且点击的是评论tab，则不允许切换
      if (!this.showReviewTab && tab === 'reviews') {
        return;
      }
      this.activeTab = tab;
    },
    
    // 加载商品详情
    async loadProductDetails() {
      if (!this.productId) return;
      
      try {
        this.loading = true;
        const res = await getIntegralDetail(this.productId);
        
        if (res.code === 200 && res.data) {
          const data = res.data;
          
          // 保存完整数据
          this.productDetail = data;
          
          // 设置基本信息
          this.productInfo.title = data.name || '';
          this.productInfo.subtitle = data.intro || '';
          this.productInfo.integralPrice = data.price || 0;
          this.productInfo.detail = data.description || '';
          this.productInfo.type = data.type || 0;
          this.productInfo.integral = data.integral || 0;
          this.productInfo.name = data.name || '';
          this.productInfo.image = data.image || '';

          // 设置规格相关数据
          if (data.type === 2 && data.productValue) {
            this.productValue = data.productValue;
            this.productAttr = data.productAttr || [];
            
            // 处理属性数据格式
            this.productAttr.forEach(attr => {
              if (typeof attr.attrValues === 'string') {
                attr.attrValues = attr.attrValues.split(',');
              }
            });
            
            // 检查是否为眼镜产品
            this.checkIfGlassesProduct();
          }
          // 处理轮播图
          if (data.image) {
            // 如果是字符串，尝试按逗号分割，否则作为单张图片
            if (typeof data.image === 'string') {
              this.productImages = data.image.includes(',') 
                ? data.image.split(',').filter(img => img.trim())
                : [data.image];
            } else if (Array.isArray(data.image)) {
              this.productImages = data.image;
            }
          }
          
          // 确保至少有一张图片
          if (!this.productImages.length) {
            this.productImages = ['/static/images/logo2.png']; // 默认图片
          }
          
          // 根据类型设置库存和显示逻辑
          if (data.type === 1) {
            // 优惠券类型
            this.productInfo.stock = data.coupon?.lastTotal || 0;
            this.showReviewTab = false; // 不显示相关评论tab
            this.activeTab = 'info'; // 强制显示商品信息tab
          } else if (data.type === 2) {
            // 实物商品类型
            this.productInfo.stock = data.product?.stock || 0;
            this.showReviewTab = true; // 显示相关评论tab
          }
          
          console.log('积分商品详情:', data);
          
          // 如果是实物商品且有商品ID，获取评论信息
          if (data.type === 2 && data.product && data.product.id) {
            this.getProductReplyList();
            this.getProductReplyCount();
          }
        } else {
          uni.showToast({
            title: res.message || '获取详情失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取积分商品详情失败:', error);
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 处理兑换
    handleExchange() {
      // 检查积分是否足够
      if (this.productInfo.integral < this.productInfo.integralPrice) {
        uni.showToast({
          title: '积分不足',
          icon: 'none'
        });
        return;
      }
      
      // 如果是实物商品且有规格，显示规格选择弹窗
      if (this.productInfo.type === 2 && this.productAttr.length > 0) {
        this.showSpecWindow = true;
      } else {
        // 优惠券或无规格商品直接跳转
        this.goToOrder();
      }
    },

    // 检查是否为眼镜产品
    checkIfGlassesProduct() {
      if (this.glassesSubCategoryIdList.length > 0 && this.productDetail.product) {
        this.glassesSubCategoryIdList.forEach((item) => {
          if (this.productDetail.product.categoryId == item) {
            this.isGlassesProduct = true;
          }
        });
      }
    },

    // 处理规格选择确认
    onSpecConfirm(selectionData) {
      console.log('选择的规格数据:', selectionData);
      this.showSpecWindow = false;
      
      // 跳转到订单结算页面，携带规格信息
      const params = {
        productId: this.productId,
        integralPrice: selectionData.totalIntegral,
        sku: JSON.stringify(selectionData.sku),
        quantity: selectionData.quantity,
        selectedAttrs: JSON.stringify(selectionData.selectedAttrs)
      };
      
      // 如果是眼镜产品且有自定义数据，添加到参数中
      if (selectionData.customData) {
        params.customData = JSON.stringify(selectionData.customData);
      }
      
      const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
      
      uni.navigateTo({
        url: `/pages/users/user_integral_order/index?${queryString}`
      });
    },

    // 跳转到订单页面
    goToOrder() {
      uni.navigateTo({
        url: `/pages/users/user_integral_order/index?productId=${this.productId}&integralPrice=${this.productInfo.integralPrice}`
      });
    },
    
    // 执行兑换 (保留原方法，供订单结算页面调用)
    performExchange() {
      // 这里调用兑换API
      uni.showToast({
        title: '兑换成功',
        icon: 'success'
      });
      
      // 延迟跳转到兑换记录页面
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/users/user_integral_exchange/index'
        });
      }, 1500);
    },
    
    // 图片加载错误处理
    handleImageError() {
      console.log('图片加载失败');
    },

    // 获取商品评论列表
    getProductReplyList() {
      if (!this.productDetail.product || !this.productDetail.product.id) {
        return;
      }
      
      getReplyProduct(this.productDetail.product.id).then(res => {
        this.reply = res.data.productReply ? [res.data.productReply] : [];
      }).catch(err => {
        console.error('获取评论失败:', err);
        this.reply = [];
      });
    },

    // 获取评论统计信息
    getProductReplyCount() {
      if (!this.productDetail.product || !this.productDetail.product.id) {
        return;
      }
      
      getReplyConfig(this.productDetail.product.id).then(res => {
        this.replyChance = res.data.replyChance * 100;
        this.replyCount = res.data.sumCount;
      }).catch(err => {
        console.error('获取评论统计失败:', err);
        this.replyChance = 0;
        this.replyCount = 0;
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.integral-details {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 120upx;
}

/* 轮播图样式 */
.swiper-container {
  position: relative;
  height: 750upx;

  .swiper {
    height: 100%;

    .swiper-image {
      width: 100%;
      height: 100%;
    }
  }

  .image-counter {
    position: absolute;
    bottom: 20upx;
    right: 20upx;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 8upx 16upx;
    border-radius: 20upx;
    font-size: 24upx;
  }
}

/* 商品信息卡片 */
.product-info-card {
  border-radius: 24rpx 24rpx 0 0;
  margin-top: -24rpx;
  position: relative;
  z-index: 10;
  overflow: hidden;
}

// 渐变背景区域：包含标签页切换、价格、标题、描述
.gradient-section {
  background: linear-gradient(180deg, #ffffff 24%, #f5f5f5);
}

// 其他模块区域：详情
.other-modules {
  background: #f5f5f5;
  padding: 0 30rpx;
}

.tab-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 30rpx 20rpx;
  position: relative;

  .tab-left {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;


    .tab-item {
      padding: 12rpx 24rpx;
      font-size: 28rpx;
      color: #222222;
      position: relative;
      margin: 0 10rpx;
      background: #F5F5F5;
      border-radius: 30rpx;

      &.active {
        background: #222222;
        color: #ffffff;
        font-weight: 500;
      }
    }
  }
}

.info-content {
  padding: 0 30rpx 30rpx;
}

.price-section {
  margin-bottom: 30rpx;

  .price-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    flex-direction: column;

    .price-left {
      flex: 1;

      .integral-price {
        display: flex;
        align-items: baseline;
        flex-wrap: wrap;

        .price-number {
          font-size: 48rpx;
          color: #FF2222;
          font-weight: bold;
          line-height: 1;
        }

        .price-unit {
          font-size: 28rpx;
          color: #FF2222;
          margin-left: 8rpx;
          line-height: 1;
        }

        .original-integral {
          font-size: 24rpx;
          color: #999999;
          margin-left: 16rpx;
          line-height: 1;
        }
      }
    }

    .stock-info {
      font-size: 24rpx;
      color: #FF2222;
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      margin-top: 20rpx;
      border: 2rpx solid #FF2222;;
    }
  }
}

.product-title {
  font-size: 32rpx;
  color: #222222;
  line-height: 44rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.product-desc {
  font-size: 28rpx;
  color: #999999;
  line-height: 40rpx;
  margin-bottom: 30rpx;
}

/* 评论内容样式 */
.comment-content {
  padding: 0 30rpx 30rpx;
}

.comment-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;

  .praise-rate {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
  }

  .view-all {
    font-size: 24rpx;
    color: #999999;
    display: flex;
    align-items: center;

    .iconfont {
      font-size: 22rpx;
      margin-left: 8rpx;
    }
  }
}

.comment-list {
  .comment-item-wrapper {
    background: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;

    .comment-item {
      padding: 30rpx;

      .user-header-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20rpx;

        .user-avatar {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;

          image {
            width: 100%;
            height: 100%;
            border-radius: 40rpx;
          }
        }

        .user-info-right {
          flex: 1;

          .user-first-line {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8rpx;

            .username {
              font-size: 28rpx;
              color: #333333;
              font-weight: 500;
            }

            .user-specs {
              font-size: 24rpx;
              color: #999999;
            }
          }

          .comment-date {
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      .star-rating {
        margin-bottom: 20rpx;
        margin-left: 100rpx;

        .star {
          font-size: 28rpx;
          color: #E0E0E0;
          margin-right: 4rpx;

          &.active {
            color: #FFD700;
          }
        }
      }

      .comment-text {
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        margin-bottom: 20rpx;
        margin-left: 100rpx;
      }

      .merchant-reply {
        background: #F5F5F5;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-left: 100rpx;

        .reply-label {
          font-size: 24rpx;
          color: #333333;
          font-weight: bold;
          margin-bottom: 8rpx;
        }

        .reply-content {
          font-size: 24rpx;
          color: #666666;
          line-height: 32rpx;
        }
      }
    }
  }

  .no-comment-wrapper {
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 60rpx 0;

    .no-comment-text {
      text-align: center;
      color: #999999;
      font-size: 28rpx;
    }
  }
}

/* 详情模块样式 */
.info-module {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.detail-info-module {
  .detail-header-section {
    padding: 30rpx 30rpx 0;

    .detail-header-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 20rpx;

      .detail-title {
        font-size: 32rpx;
        color: #222222;
        font-weight: 500;
      }
    }
  }

  .detail-content {
    padding: 0 30rpx 30rpx;

    .detail-content-rich-text {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    // 详情内容样式
    /deep/ .wscnph {
      max-width: 100% !important;
      height: auto !important;
    }
  }
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #E5E5E5;
  z-index: 999;

  .exchange-btn {
    width: 100%;
    height: 80rpx;
    background: #BDFD5B;
    color: #222222;
    border: none;
    border-radius: 40rpx;
    font-size: 32rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;

    &.insufficient {
      background: #CCCCCC;
      color: #ffffff;
    }

    &:disabled {
      opacity: 1;
    }
  }
}
</style>
