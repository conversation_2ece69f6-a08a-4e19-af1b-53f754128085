<template>
	<view :data-theme="theme">
		<view class='productList'>
			<view class='search bg_color acea-row row-between-wrapper'>
				<!-- #ifdef H5 -->
				<view class="iconfont icon-xiang<PERSON>o" @click="goback()"></view>
				<!-- #endif -->
				<view :class="tabIndex !== 2?'searchIpt':''" class='input acea-row row-middle'><text class='iconfont icon-sousuo mr20'></text>
					<input :placeholder="tabIndex===1?'搜索商品名称':'搜索店铺名称'" placeholder-class='placeholder'
						confirm-type='search' name="search" v-model="keyword" @confirm="searchSubmit"
						maxlength="20"></input>
				</view>
				<view v-if="tabIndex === 2" class='iconfont icon-shaixuan' @click='open(2)'></view>
			</view>
			<view class="nav-wrapper" v-if="merId===0 && where.cid === 0">
				<view class="tab-bar" :class="{'merTab':tabIndex===2}">
					<view class="tab-item" :class="{on:tabIndex===1}" @click="changetab(1)">商品</view>
					<view class="tab-item" :class="{on:tabIndex===2}" @click="changetab(2)">店铺</view>
				</view>
			</view>
			<view v-if="tabIndex===1" class='nav acea-row row-middle' :class="(merId > 0 || where.cid > 0) ? 'mer-nav' : ''">
				<view :class="{ active: navActive === 0 }" class='item' @click='set_where(1,tabIndex)'>
					{{title ? title:'默认'}}
				</view>
				<view :class="{ active: navActive === 1 }" class='item' @click='set_where(2,tabIndex)'>
					价格
					<image v-if="price==1" :src='upPng'></image>
					<image v-else-if="price==2" :src='downPng'></image>
					<image v-else src='@/static/images/horn.png'></image>
				</view>
				<view :class="{ active: navActive === 2 }" class='item' @click='set_where(3,tabIndex)'>
					销量
					<image v-if="stock==1" :src='upPng'></image>
					<image v-else-if="stock==2" :src='downPng'></image>
					<image v-else src='@/static/images/horn.png'></image>
				</view>
				<view class='item iconfont' :class="is_switch?'icon-pailie':'icon-tupianpailie'"
					@click='Changswitch()'></view>
			</view>
			<view class="proList" v-if="tabIndex===1" :class="[(merId > 0 || where.cid > 0) ? 'mer-listBox' : '']">
				<view v-if="productList.length > 0">
					<view v-if="!is_switch" class='list acea-row row-between-wrapper goods-row'>
						<view class='item' :class='is_switch==true?"":"on"' hover-class='none'
							v-for="(item,index) in productList" :key="index" @click="godDetail(item)">
							<view class='pictrue' :class='is_switch==true?"":"on"'>
								<easy-loadimage :image-src="item.image" :class='is_switch==true?"":"on"'>
								</easy-loadimage>
								<!-- <image :src='item.image' :class='is_switch==true?"":"on"'></image> -->
								<span class="pictrue_log_class"
									:class="is_switch === true ? 'pictrue_log_big' : 'pictrue_log'"
									v-if="item.activityH5 && item.activityH5.type === '1'">秒杀</span>
								<span class="pictrue_log_class"
									:class="is_switch === true ? 'pictrue_log_big' : 'pictrue_log'"
									v-if="item.activityH5 && item.activityH5.type === '2'">砍价</span>
								<span class="pictrue_log_class"
									:class="is_switch === true ? 'pictrue_log_big' : 'pictrue_log'"
									v-if="item.activityH5 && item.activityH5.type === '3'">拼团</span>
							</view>
							<view class='text' :class='is_switch==true?"":"on"'>
								<view class='name line2'>{{item.name}}</view>
								<view class='x-money' :class='is_switch==true?"":"on"'>￥<text
										class='num'>{{item.price}}</text></view>
								<view v-show="item.merTypeId"><text
										class="font-bg-red mr10 merType">{{item.merTypeId | merchantTypeFilter}}</text>
								</view>
								<view class='vip acea-row row-between-wrapper' :class='is_switch==true?"":"on"'>
									<view class='vip-money' v-if="item.vip_price && item.vip_price > 0">
										￥{{item.vip_price}}
										<image src='@/static/images/vip.png'></image>
									</view>
									<view>已售{{Number(item.sales) + Number(item.ficti) || 0}}{{item.unitName}}</view>
								</view>
								<view v-if="item.merName" class="company" @click.stop="goStore(item.merId)">
									<text class='name line1'>{{item.merName}}</text>
									<view class="flex">
										进店
										<text class="iconfont icon-xiangyou"></text>
									</view>
								</view>
							</view>

						</view>
					</view>
					<view v-else class="goods-column">
						<WaterfallsFlow :wfList='productList' :type="1" :isStore="1" />
					</view>
				</view>
				<view class='loadingicon acea-row row-center-wrapper'>
					<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
				</view>
			</view>
			<view class="merList" v-if="tabIndex == 2">
				<view v-if="merchantList.length===0" class='nav acea-row row-middle' :class="(merId > 0 || where.cid > 0) ? 'mer-nav' : ''">
				</view>
				<merchant-list :merchantList="merchantList"></merchant-list>
				<view class='loadingicon acea-row row-center-wrapper'>
					<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
				</view>
			</view>
		</view>
		<view class='noCommodity' :class="(merId > 0 || where.cid > 0) ? 'mer-noCommodity' : ''"
			v-if="(productList.length==0 && isShow && where.page > 1 && tabIndex===1 && !loading )  || (merchantList.length==0 && isShow && where.page > 1 && tabIndex===2 && !loading)">
			<view class='pictrue'>
				<image src='@/pages/aastatictoT/static/images/noSearch.png'></image>
			</view>
			<text class="text">{{tabIndex===1?'暂无商品~':'暂无店铺~'}}</text>
			<recommend></recommend>
		</view>

		<tui-drawer mode="right" :visible="rightDrawer" @close="closeDrawer">
			<merSeach :whereMer="whereMer" @close="closeDrawer" @confirm="confirm"></merSeach>
		</tui-drawer>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		productList
	} from '@/api/product.js';
	import {
		getMerSearchApi,
		getMerProListApi
	} from '@/api/merchant.js';
	import {
		mapGetters
	} from "vuex";
	import {
		goShopDetail
	} from '@/libs/order.js'
	import merSeach from '../components/merSeach/index.vue'
	import merchantList from '@/components/merchantList/index.vue'
	import WaterfallsFlow from '@/components/WaterfallsFlow/WaterfallsFlow.vue' 
	import tuiDrawer from '@/components/base/tui-drawer.vue'
	import recommend from "@/components/base/recommend.vue";
	import easyLoadimage from '@/components/base/easy-loadimage.vue';
	let app = getApp();
	export default {
		computed: mapGetters(['uid', 'currentMerId', 'userAddress']),
		components: {
			merchantList,
			merSeach,
			WaterfallsFlow,
			tuiDrawer,
			recommend,
			easyLoadimage
		},
		mounted() {
			this.$store.dispatch('MerCategoryList');
			this.$store.dispatch('MerTypeList');
		},
		data() {
			return {
				keyword: '',
				drawers: 0,
				navActive: 0,
				tabIndex: 1,
				productList: [],
				is_switch: true,
				where: {
					keyword: '',
					priceOrder: '',
					salesOrder: '',
					news: 0,
					page: 1,
					limit: 20,
					cid: 0,
					merId: null
				},
				price: 0,
				stock: 0,
				nows: false,
				loadend: false,
				loading: false,
				loadTitle: '加载更多',
				title: '',
				theme: app.globalData.theme,
				upPng: '../../../static/images/up_red.png',
				downPng: '../../../static/images/down_red.png',
				whereMer: {
					categoryId: '',
					isSelf: '',
					keywords: '',
					page: 1,
					limit: 20,
					typeId: ''
				},
				rightDrawer: false,
				merchantList: [],
				merId: 0,
				goodScroll: true,
				isShow: false
			};
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		},
		onLoad: function(options) {
			this.merId = options.merId ? Number(options.merId) : 0;
			if (this.merId == 0) {
				this.merId = this.currentMerId;
			}
			this.$set(this.where, 'catId', Number(options.catId) || 0);
			this.$set(this.where, 'cid', Number(options.cid) || 0);
			this.title = options.title || '';
			this.$set(this, 'keyword', options.searchValue || '');

			if (this.tabIndex === 1) this.get_product_list();
			switch (this.theme) {
				case 'theme2':
					this.upPng = '../../../static/images/up_orange.png';
					this.downPng = '../../../static/images/down_orange.png';
					break;
				case 'theme3':
					this.upPng = '../../../static/images/up_green.png';
					this.downPng = '../../../static/images/down_green.png'
					break;
				case 'theme4':
					this.upPng = '../../../static/images/up_blue.png';
					this.downPng = '../../../static/images/down_blue.png'
					break;
				case 'theme5':
					this.upPng = '../../../static/images/up_pink.png';
					this.downPng = '../../../static/images/down_pink.png'
					break;
				default:
					this.upPng = '../../../static/images/up_red.png';
					this.downPng = '../../../static/images/down_red.png'
					break;
			}
		},
		methods: {
			goStore(id) {
				uni.navigateTo({
					url: `/pages/merchant/home/<USER>
				})
			},
			confirm(data) {
				this.whereMer.typeId = data.typeId;
				this.whereMer.categoryId = data.categoryId;
				this.whereMer.keywords = this.where.keyword;
				this.loadend = false;
				this.$set(this.whereMer, 'page', 1)
				this.merchantList = [];
				this.rightDrawer = false
				this.getMerStreet();
			},
			changetab(n) {
				this.tabIndex = n;
				this.nows = 1;
				this.stock = 0;
				this.loadend = false;
				this.loading = false;
				if (n === 2) {
					this.merchantList = [];
					this.$set(this.whereMer, 'page', 1);
					this.getMerStreet();
					this.title = '';
				} else {
					this.whereMer.typeId = '';
					this.whereMer.categoryId = '';
					this.productList = [];
					this.$set(this.where, 'page', 1);
					this.get_product_list();
				}
			},
			open(n) {
				if (this.tabIndex === 2) {
					this.rightDrawer = true;
					this.drawers = n;
				}
			},
			closeDrawer(e) {
				this.rightDrawer = false
				if (!e) {
					this.rightDrawer = false
				}
			},
			goback() {
				// #ifdef H5
				return history.back();
				// #endif
				// #ifndef H5
				return uni.navigateBack({
					delta: 1
				});
				// #endif
			},
			getMerStreet: function(isPage) {
				this.skeletonShow = true
				let that = this;
				that.setWhere();
				if (that.loadend) return;
				if (that.loading) return;
				that.loading = true;
				if (isPage === true) that.$set(that, 'merchantList', []);

				that.loadTitle = '';
				that.whereMer.keywords = encodeURIComponent(that.keyword);
				getMerSearchApi(that.whereMer).then(res => {
					let list = res.data.list;
					let loadend = list.length < that.whereMer.limit;
					that.loadend = loadend;
					that.loadTitle = loadend ? '已全部加载' : '加载更多';
					
					let merchantList = that.$util.SplitArray(list, that.merchantList);
					that.$set(that, 'merchantList', merchantList);
					that.$set(that.whereMer, 'page', that.whereMer.page + 1);
					that.loading = false;
					this.skeletonShow = false;
					this.isShow = true
				}).catch(err => {
					that.loading = false;
					that.goodScroll = false;
					that.loadTitle = '加载更多';
				});
			},
			// 去详情页
			godDetail(item) {
				goShopDetail(item.id);
			},
			Changswitch: function() {
				let that = this;
				that.is_switch = !that.is_switch
			},
			searchSubmit: function(e) {
				let that = this;
				that.loadend = false;
				that.$set(that, 'keyword', e.detail.value || '');
				if (that.tabIndex === 1) {
					that.$set(that.where, 'page', 1)
					this.get_product_list(true);
				} else {
					that.$set(that, 'merchantList', []);
					this.$set(this.whereMer, 'page', 1)
					this.getMerStreet();
				}
			},
			//点击事件处理
			set_where: function(e, n) {
				switch (e) {
					case 1:
						this.price = 0;
						this.stock = 0;
						this.navActive = 0;
						break;
					case 2:
						if (this.price == 0) this.price = 1;
						else if (this.price == 1) this.price = 2;
						else if (this.price == 2) this.price = 0;
						this.stock = 0;
						this.navActive = 1;
						break;
					case 3:
						if (this.stock == 0) this.stock = 1;
						else if (this.stock == 1) this.stock = 2;
						else if (this.stock == 2) this.stock = 0;
						this.price = 0
						this.navActive = 2;
						break;
					case 4:
						this.price = 0;
						this.stock = 0;
						this.navActive = 3;
						break;
				}
				if (n === 1) {
					this.loadend = false;
					this.$set(this.where, 'page', 1);
					this.get_product_list(true);
				} else {
					this.nows = 1;
				}
			},
			//设置where条件
			setWhere: function() {
				if (this.price == 0) this.where.priceOrder = '';
				else if (this.price == 1) this.where.priceOrder = 'asc';
				else if (this.price == 2) this.where.priceOrder = 'desc';
				if (this.stock == 0) this.where.salesOrder = '';
				else if (this.stock == 1) this.where.salesOrder = 'asc';
				else if (this.stock == 2) this.where.salesOrder = 'desc';
				this.where.news = this.nows ? 1 : 0;
			},
			//查找产品
			get_product_list: function(isPage) {
				let that = this;
				that.setWhere();
				if (that.loadend) return;
				if (that.loading) return;
				if (isPage === true) that.$set(that, 'productList', []);
				that.loading = true;
				that.loadTitle = '';
				if (this.merId > 0) {
					that.where.merId = that.merId
				}
				that.where.keyword = encodeURIComponent(that.keyword);

				this.merId === 0 ? productList(that.where).then(res => {
					let list = res.data.list;
					let loadend = list.length < that.where.limit;
					that.loadend = loadend;
					that.loading = false;
					that.loadTitle = loadend ? '已全部加载' : '加载更多';
					
					let productList = that.$util.SplitArray(list, that.productList);
					that.$set(that, 'productList', productList);
					that.$set(that.where, 'page', that.where.page + 1);
					this.isShow = true
				}).catch(err => {
					that.loading = false;
					that.loadTitle = '加载更多'
				}) : getMerProListApi(that.where).then(res => {
					let list = res.data.list;
					let loadend = list.length < that.where.limit;
					that.loadend = loadend;
					that.loading = false;
					that.loadTitle = loadend ? '已全部加载' : '加载更多';
					
					let productList = that.$util.SplitArray(list, that.productList);
					that.$set(that, 'productList', productList);
					that.$set(that.where, 'page', that.where.page + 1);
				}).catch(err => {
					that.loading = false;
					that.loadTitle = '加载更多'
				})
			},
		},
		onReachBottom() {
			if (this.tabIndex == 1) {
				if (this.productList.length > 0) {
					this.get_product_list();
				}
			} else if (this.tabIndex == 2) {
				if (this.merchantList.length > 0) {
					this.getMerStreet();
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.searchIpt{
		/* #ifdef H5 */
		width: 91% !important;
		/* #endif */
		/* #ifndef H5 */
		 width: 100% !important;
		/* #endif */
	}
	.icon-xiangzuo {
		color: #fff;
	}
	
	.bg_color {
		@include main_bg_color(theme);
	}
	
	.font_color {
		@include main_color(theme);
	}

	.nav-wrapper {
		z-index: 9;
		position: fixed;
		left: 0;
		top: 86rpx;
		width: 100%;
		@include main_bg_color(theme);
	
		.tab-bar {
			display: flex;
			align-items: center;
			height: 131rpx;
	
			.tab-item {
				position: relative;
				flex: 1;
				display: flex;
				justify-content: center;
				align-items: center;
				padding-bottom: 40rpx;
				color: #fff;
				font-size: 28rpx;
				font-weight: bold;
				margin-top: -36rpx;
	
				&::after {
					content: ' ';
					position: absolute;
					left: 50%;
					bottom: 34rpx;
					width: 60rpx;
					height: 3rpx;
					background: transparent;
					transform: translateX(-50%);
				}
	
				&.on {
					&::after {
						background: #fff;
					}
				}
			}
		}
	}
	
	.proList {
		position: relative;
		top: 240rpx;
		padding-bottom: 10rpx;
		padding-bottom: calc(10rpx + constant(safe-area-inset-bottom) / 3);
		padding-bottom: calc(10rpx + env(safe-area-inset-bottom) / 3);
	}

	.merList {
		position: relative;
		top: 155rpx;
		z-index: 66;
		padding-bottom: 10rpx;
		padding-bottom: calc(10rpx + constant(safe-area-inset-bottom) / 3);
		padding-bottom: calc(10rpx + env(safe-area-inset-bottom) / 3);
	}

	.mer {
		&-nav {
			top: 80rpx !important;
			border-radius: inherit !important;
		}

		&-listBox {
			position: relative;
			top: 166rpx !important;
		}
		
		&-noCommodity {
			top: 155rpx !important;
		}
	}

	.goods-row {
		background-color: #fff;
	}

	.goods-column {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		background-color: #F5F5F5;
		width: 100%;
		padding-top: 20rpx;
	}

	.x-money {
		font-size: 26rpx;
		@include price_color(theme);
	}

	.productList .search {
		width: 100%;
		height: 86rpx;
		padding: 0 30rpx;
		box-sizing: border-box;
		position: fixed;
		top: 0 !important;
		left: 0;
		z-index: 9;
		color: #fff;

	}

	.productList .search .input {
		width: 570rpx;
		height: 60rpx;
		background-color: #fff;
		border-radius: 50rpx;
		padding: 0 20rpx;
		box-sizing: border-box;
		color: #333;
	}

	.productList .search .input input {
		width: 88%;
		height: 100%;
		font-size: 26rpx;
	}

	.productList .search .input .placeholder {
		color: #CCCCCC;
	}

	.productList .search .input .iconfont {
		font-size: 26rpx;
		color: #999999;
	}

	.productList .search .icon-shaixuan {
		color: #fff;
		width: 62rpx;
		font-size: 40rpx;
		height: 86rpx;
		line-height: 86rpx;
		text-align: right;
	}

	.productList .nav {
		height: 86rpx;
		color: #454545;
		position: fixed;
		left: 0;
		width: 100%;
		font-size: 28rpx;
		background-color: #fff;
		top: 154rpx;
		z-index: 9;
		border-radius: 16rpx 16rpx 0rpx 0rpx;
	}

	.productList .nav .item {
		width: 25%;
		text-align: center;
	}

	.productList .nav .item.font-color {
		font-weight: bold;
	}

	.productList .nav .item image {
		width: 15rpx;
		height: 19rpx;
		margin-left: 10rpx;
	}
	
	.productList .nav .active {
		background-color: #FFFFFF;
		font-weight: bold;
		@include main_color(theme);
	}

	.company {
		display: flex;
		align-items: center;
		color: #737373 !important;
		font-size: 26rpx;
	
		.name {
			display: inline-block;
			height: auto !important;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	
		.flex {
			display: flex;
			align-items: center;
			margin-left: 10rpx;
			color: #282828 !important;
			width: 100rpx;
	
			.iconfont {
				font-size: 16rpx;
				margin-top: 4rpx;
			}
		}
	}

	.productList .list {
		padding: 30rpx;
	}

	.productList .list.on {
		margin-top: 30rpx !important;
		background-color: #fff;
		border-top: 1px solid #F5F5F5;
	}

	.productList .list .item {
		width: 335rpx;
		background-color: #fff;
		border-radius: 14rpx;
		margin-bottom: 20rpx;
	}

	.productList .list .item.on {
		width: 100%;
		display: flex;
		padding: 0 0rpx 60rpx 0rpx;
		margin: 0;
		border-radius: 14rpx;
	}

	.productList .list .item .pictrue {
		position: relative;
	}

	.productList .list .item .pictrue /deep/.easy-loadimage {
		width: 240rpx;
		height: 240rpx;
		border-radius: 14rpx;
		overflow: hidden;
	}

	.productList .list .item .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 20rpx 20rpx 0 0;
		overflow: hidden;
	}

	.productList .list .item .pictrue image.on {
		border-radius: 6rpx;
	}

	.productList .list .item .text {
		padding: 18rpx 20rpx;
		font-size: 30rpx;
		color: #222;
	}

	.productList .list .item .text.on {
		width: 456rpx;
		padding: 0 0 0 20rpx;
		position: relative;

		&::after {
			content: '';
			display: block;
			width: 94%;
			border-bottom: 1rpx solid #f6f6f6;
			position: absolute;
			right: 2rpx;
			bottom: -24rpx;
		}

		.name {
			height: 84rpx;
			line-height: 42rpx;
		}
	}

	.productList .list .item .text .money {
		font-size: 26rpx;
		font-weight: bold;
		margin-top: 8rpx;
	}

	.productList .list .item .text .money.on {
		margin-top: 50rpx;
	}

	.productList .list .item .text .money .num {
		font-size: 34rpx;
	}

	.productList .list .item .text .vip {
		font-size: 20rpx;
		color: #aaa;
		margin-top: 7rpx;
	}

	.productList .list .item .text .vip .vip-money {
		font-size: 24rpx;
		color: #282828;
		font-weight: bold;
	}

	.productList .list .item .text .vip .vip-money image {
		width: 46rpx;
		height: 21rpx;
		margin-left: 4rpx;
	}

	.noCommodity {
		background-color: #fff;
		width: 100%;
		min-height: fit-content;
		position: absolute;
		top: 240rpx;
	}
	.row-between-wrapper .input {
		height: 60rpx;
		margin: 10rpx 0 16rpx;
	}
</style>
