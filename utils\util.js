// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import {
	TOKENNAME,
	HTTP_REQUEST_URL
} from '../config/app.js';
import store from '../store';
import {
	pathToBase64
} from '@/plugin/image-tools/index.js';
// #ifdef APP-PLUS
import permision from "./permission.js"
// #endif
export default {
	/**
	 * opt  object | string
	 * to_url object | string
	 * 例:
	 * this.Tips('/pages/test/test'); 跳转不提示
	 * this.Tips({title:'提示'},'/pages/test/test'); 提示并跳转
	 * this.Tips({title:'提示'},{tab:1,url:'/pages/index/index'}); 提示并跳转值table上
	 * tab=1 一定时间后跳转至 table上
	 * tab=2 一定时间后跳转至非 table上
	 * tab=3 一定时间后返回上页面
	 * tab=4 关闭所有页面跳转至非table上
	 * tab=5 关闭当前页面跳转至table上
	 */
	Tips: function(opt, to_url) {
		if (typeof opt == 'string') {
			to_url = opt;
			opt = {};
		}
		let title = opt.title || '',
			icon = opt.icon || 'none',
			endtime = opt.endtime || 2000,
			success = opt.success;
		if (title) uni.showToast({
			title: title,
			icon: icon,
			duration: endtime,
			success
		})
		if (to_url != undefined) {
			if (typeof to_url == 'object') {
				let tab = to_url.tab || 1,
					url = to_url.url || '';
				switch (tab) {
					case 1:
						//一定时间后跳转至 table
						setTimeout(function() {
							uni.switchTab({
								url: url
							})
						}, endtime);
						break;
					case 2:
						//跳转至非table页面
						setTimeout(function() {
							uni.navigateTo({
								url: url,
							})
						}, endtime);
						break;
					case 3:
						//返回上页面
						setTimeout(function() {
							// #ifndef H5
							uni.navigateBack({
								delta: parseInt(url),
							})
							// #endif
							// #ifdef H5
							history.back();
							// #endif
						}, endtime);
						break;
					case 4:
						//关闭当前所有页面跳转至非table页面
						setTimeout(function() {
							uni.reLaunch({
								url: url,
							})
						}, endtime);
						break;
					case 5:
						//关闭当前页面跳转至非table页面
						setTimeout(function() {
							uni.redirectTo({
								url: url,
							})
						}, endtime);
						break;
				}

			} else if (typeof to_url == 'function') {
				setTimeout(function() {
					to_url && to_url();
				}, endtime);
			} else {
				//没有提示时跳转不延迟
				setTimeout(function() {
					uni.navigateTo({
						url: to_url,
					})
				}, title ? endtime : 0);
			}
		}
	},
	/**
	 * 移除数组中的某个数组并组成新的数组返回
	 * @param array array 需要移除的数组
	 * @param int index 需要移除的数组的键值
	 * @param string | int 值
	 * @return array
	 * 
	 */
	ArrayRemove: function(array, index, value) {
		const valueArray = [];
		if (array instanceof Array) {
			for (let i = 0; i < array.length; i++) {
				if (typeof index == 'number' && array[index] != i) {
					valueArray.push(array[i]);
				} else if (typeof index == 'string' && array[i][index] != value) {
					valueArray.push(array[i]);
				}
			}
		}
		return valueArray;
	},
	/**
	 * 生成海报获取文字
	 * @param string text 为传入的文本
	 * @param int num 为单行显示的字节长度
	 * @return array 
	 */
	textByteLength: function(text, num) {

		let strLength = 0;
		let rows = 1;
		let str = 0;
		let arr = [];
		for (let j = 0; j < text.length; j++) {
			if (text.charCodeAt(j) > 255) {
				strLength += 2;
				if (strLength > rows * num) {
					strLength++;
					arr.push(text.slice(str, j));
					str = j;
					rows++;
				}
			} else {
				strLength++;
				if (strLength > rows * num) {
					arr.push(text.slice(str, j));
					str = j;
					rows++;
				}
			}
		}
		arr.push(text.slice(str, text.length));
		return [strLength, arr, rows] //  [处理文字的总字节长度，每行显示内容的数组，行数]
	},

	/**
	 * 获取分享海报
	 * @param array arr2 海报素材
	 * @param string store_name 素材文字
	 * @param string price 价格
	 * @param string ot_price 原始价格
	 * @param string productIntro 商品描述
	 * @param function successFn 回调函数
	 *
	 *
	 */
	PosterCanvas: function(arr2, store_name, price, ot_price, productIntro, successFn) {
		let that = this;
		const ctx = uni.createCanvasContext('firstCanvas');
		
		// 获取屏幕宽度并计算80%作为海报宽度
		const sysInfo = uni.getSystemInfoSync();
		const screenWidth = sysInfo.screenWidth;
		const targetWidth = Math.floor(screenWidth * 0.8);
		const targetHeight = Math.floor(targetWidth * 1.44); // 保持原始比例
		
		// 获取设备像素比，确保高分辨率设备上的清晰度
		const dpr = sysInfo.pixelRatio || 2;
		
		// 计算缩放比例（基于原始设计尺寸750x1080）
		const scaleX = targetWidth / 750;
		const scaleY = targetHeight / 1080;
		const scale = Math.min(scaleX, scaleY); // 使用较小的比例保持比例
		
		ctx.clearRect(0, 0, targetWidth, targetHeight);

		// 设置整体海报圆角 - 先绘制圆角路径并裁剪
		ctx.save();
		that.drawRoundRectPath(ctx, 0, 0, targetWidth, targetHeight, 24 * scale);
		ctx.clip();

		// 绘制背景图片
		const bgImagePath = '/static/images/bg_poster.png';
		ctx.drawImage(bgImagePath, 0, 0, targetWidth, targetHeight);

		// 白色圆角卡片区域
		const cardPadding = 30 * scale;
		const cardX = cardPadding;
		const cardY = cardPadding;
		const cardWidth = targetWidth - cardPadding * 2;
		const cardHeight = 890 * scale; 
		that.drawRoundRect(ctx, cardX, cardY, cardWidth, cardHeight, 24 * scale, '#FFFFFF');

		// 商品图片区域
		const imageX = cardX + 30 * scale;
		const imageY = cardY + 30 * scale;
		const imageWidth = cardWidth - 60 * scale;
		const imageHeight = 580 * scale; 

		uni.getImageInfo({
			src: arr2[1], // 商品图片
			success: function(res) {
				// 绘制商品图片（圆角）
				ctx.save();
				that.drawRoundRectPath(ctx, imageX, imageY, imageWidth, imageHeight, 16 * scale);
				ctx.clip();
				ctx.drawImage(arr2[1], imageX, imageY, imageWidth, imageHeight);
				ctx.restore();

				// 价格信息
				const priceY = imageY + imageHeight + 80 * scale;
				ctx.setTextAlign('left');

				// 价格
				const priceStr = price.toString();
				const priceParts = priceStr.split('.');
				const integerPart = priceParts[0];
				const decimalPart = priceParts[1] || '00';

				ctx.setFontSize(56 * scale);
				ctx.setFillStyle('#FF2222');
				const currencyAndInteger = '¥' + integerPart;
				ctx.fillText(currencyAndInteger, imageX, priceY);
				const integerWidth = ctx.measureText(currencyAndInteger).width;

				ctx.setFontSize(36 * scale); 
				ctx.setFillStyle('#FF2222');
				const decimalText = '.' + decimalPart;
				ctx.fillText(decimalText, imageX + integerWidth, priceY);
				const decimalWidth = ctx.measureText(decimalText).width;

				// 原价
				if (ot_price && parseFloat(ot_price) > parseFloat(price)) {
					const otPriceStr = ot_price.toString();
					const otPriceParts = otPriceStr.split('.');
					const otIntegerPart = otPriceParts[0];
					const otDecimalPart = otPriceParts[1] || '00';
					
					const otPriceX = imageX + integerWidth + decimalWidth + 20 * scale;
					const otPriceY = priceY; 

					ctx.setFontSize(36 * scale); 
					ctx.setFillStyle('#999999');
					const otPriceText = '¥' + otIntegerPart + '.' + otDecimalPart;
					ctx.fillText(otPriceText, otPriceX, otPriceY);

					const otPriceWidth = ctx.measureText(otPriceText).width;
					ctx.beginPath();
					ctx.setStrokeStyle('#999999');
					ctx.setLineWidth(2 * scale);
					ctx.moveTo(otPriceX, otPriceY - 18 * scale);
					ctx.lineTo(otPriceX + otPriceWidth, otPriceY - 18 * scale);
					ctx.stroke();
				}

				// 商品标题
				const titleY = priceY + 60 * scale;
				ctx.setFontSize(38 * scale);
				ctx.setFillStyle('#222222');

				const maxTitleWidth = imageWidth;
				// 使用截断方法处理标题，超过一行显示省略号
				const truncatedTitle = that.truncateText(ctx, store_name, maxTitleWidth);
				ctx.fillText(truncatedTitle, imageX, titleY);

				// 商品描述
				const descY = titleY + 55 * scale; // 固定单行间距
				ctx.setFontSize(28 * scale);
				ctx.setFillStyle('#666666');
				// 使用截断方法处理描述，超过一行显示省略号
				const truncatedDesc = that.truncateText(ctx, productIntro, maxTitleWidth);
				ctx.fillText(truncatedDesc, imageX, descY);

				// 底部文字
				const bottomTextY = targetHeight - 80 * scale;
				ctx.setFontSize(26 * scale);
				ctx.setFillStyle('#222222');
				ctx.fillText('商品具有时效性', imageX, bottomTextY);
				ctx.fillText('长按二维码进入小程序下单', imageX, bottomTextY + 40 * scale);

				// 二维码
				const drawQRCode = function() {
					if (arr2[2]) {
						const qrSize = 120 * scale;
						const qrPadding = 12 * scale; 
						const qrX = targetWidth - qrSize - 50 * scale;
						const qrY = bottomTextY - 10 * scale;
						
						const adjustedQrX = Math.min(qrX, targetWidth - qrSize - qrPadding - 10 * scale);
						const adjustedQrY = Math.min(qrY, targetHeight - qrSize - qrPadding - 10 * scale);
						
						const bgX = adjustedQrX - qrPadding;
						const bgY = adjustedQrY - qrPadding;
						const bgSize = qrSize + qrPadding * 2;

						ctx.setFillStyle('#FFFFFF');
						ctx.fillRect(bgX, bgY, bgSize, bgSize);

						if (arr2[2].startsWith('data:image')) {
							const fs = uni.getFileSystemManager();
							const base64Data = arr2[2].split(',')[1];
							const tempFilePath = `${uni.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;

							try {
								fs.writeFileSync(tempFilePath, base64Data, 'base64');
								ctx.drawImage(tempFilePath, adjustedQrX, adjustedQrY, qrSize, qrSize);
								finishDrawing();

							} catch (error) {
								finishDrawing();
							}
						} else {
							ctx.drawImage(arr2[2], adjustedQrX, adjustedQrY, qrSize, qrSize);
							finishDrawing();
						}
					} else {
						finishDrawing();
					}
				};

				const finishDrawing = function() {
					ctx.restore();
					ctx.draw(true, function() {
						setTimeout(() => {
							uni.canvasToTempFilePath({
								canvasId: 'firstCanvas',
								x: 0,
								y: 0,
								width: targetWidth,
								height: targetHeight,
								destWidth: targetWidth * dpr,
								destHeight: targetHeight * dpr,
								fileType: 'png',
								quality: 1, // 最高质量
								success: function(res) {
									successFn && successFn(res.tempFilePath);
								},
								fail: function(err) {
									console.log('生成海报失败', err);
									that.Tips({
										title: '生成海报失败'
									});
								}
							});
						}, 1000);
					});
				};
				drawQRCode();
			},
			fail: function(err) {
				console.log('商品图片获取失败', err);
				that.Tips({
					title: '无法获取商品图片'
				});
			}
		});
	},

	/**
	 * 绘制圆角矩形
	 */
	drawRoundRect: function(ctx, x, y, width, height, radius, fillColor) {
		ctx.save();
		this.drawRoundRectPath(ctx, x, y, width, height, radius);
		if (fillColor) {
			ctx.setFillStyle(fillColor);
			ctx.fill();
		}
		ctx.restore();
	},

	/**
	 * 绘制圆角矩形路径
	 */
	drawRoundRectPath: function(ctx, x, y, width, height, radius) {
		ctx.beginPath();
		ctx.moveTo(x + radius, y);
		ctx.lineTo(x + width - radius, y);
		ctx.arc(x + width - radius, y + radius, radius, -Math.PI / 2, 0);
		ctx.lineTo(x + width, y + height - radius);
		ctx.arc(x + width - radius, y + height - radius, radius, 0, Math.PI / 2);
		ctx.lineTo(x + radius, y + height);
		ctx.arc(x + radius, y + height - radius, radius, Math.PI / 2, Math.PI);
		ctx.lineTo(x, y + radius);
		ctx.arc(x + radius, y + radius, radius, Math.PI, -Math.PI / 2);
		ctx.closePath();
	},

	/**
	 * 绘制文字自动换行
	 * @param array arr2 海报素材
	 * @param Number x , y 绘制的坐标
	 * @param Number maxWigth 绘制文字的宽度
	 * @param Number lineHeight 行高
	 * @param Number maxRowNum 最大行数
	 */
	canvasWraptitleText(canvas, text, x, y, maxWidth, lineHeight, maxRowNum) {
		if (typeof text != 'string' || typeof x != 'number' || typeof y != 'number') {
			return;
		}
		// canvas.font = '20px Bold PingFang SC'; //绘制文字的字号和大小
		// 字符分隔为数组
		var arrText = text.split('');
		var line = '';
		var rowNum = 1
		for (var n = 0; n < arrText.length; n++) {
			var testLine = line + arrText[n];
			var metrics = canvas.measureText(testLine);
			var testWidth = metrics.width;
			if (testWidth > maxWidth && n > 0) {
				if (rowNum >= maxRowNum) {
					var arrLine = testLine.split('')
					arrLine.splice(-9)
					var newTestLine = arrLine.join("")
					newTestLine += "..."
					canvas.fillText(newTestLine, x, y);
					//如果需要在省略号后面添加其他的东西，就在这个位置写（列如添加扫码查看详情字样）
					//canvas.fillStyle = '#2259CA';
					//canvas.fillText('扫码查看详情',x + maxWidth-90, y);
					return
				}
				canvas.fillText(line, x, y);
				line = arrText[n];
				y += lineHeight;
				rowNum += 1
			} else {
				line = testLine;
			}
		}
		canvas.fillText(line, x, y);
	},
	/**
	 * 获取活动分享海报
	 * @param array arr2 海报素材
	 * @param string storeName 素材文字
	 * @param string price 价格
	 * @param string people 人数
	 * @param string count 剩余人数
	 * @param function successFn 回调函数
	 */
	activityCanvas: function(arrImages, storeName, price, people, count, num, successFn) {
		let that = this;
		let rain = 2;
		const context = uni.createCanvasContext('activityCanvas');
		context.clearRect(0, 0, 0, 0);
		/**
		 * 只能获取合法域名下的图片信息,本地调试无法获取
		 * 
		 */
		context.fillStyle = '#fff';
		context.fillRect(0, 0, 594, 850);
		uni.getImageInfo({
			src: arrImages[0],
			success: function(res) {
				context.drawImage(arrImages[0], 0, 0, 594, 850);
				context.setFontSize(14 * rain);
				context.setFillStyle('#333333');
				that.canvasWraptitleText(context, storeName, 110 * rain, 110 * rain, 230 * rain, 30 *
					rain, 1)
				context.drawImage(arrImages[2], 68 * rain, 194 * rain, 160 * rain, 160 * rain);
				context.save();

				context.setFontSize(14 * rain);
				context.setFillStyle('#fc4141');
				context.fillText('￥', 157 * rain, 145 * rain);

				context.setFontSize(24 * rain);
				context.setFillStyle('#fc4141');
				context.fillText(price, 170 * rain, 145 * rain);

				context.setFontSize(10 * rain);
				context.setFillStyle('#fff');
				context.fillText(people, 118 * rain, 143 * rain);


				context.setFontSize(12 * rain);
				context.setFillStyle('#666666');
				context.setTextAlign('center');
				context.fillText(count, (167 - num) * rain, 166 * rain);

				that.handleBorderRect(context, 27 * rain, 94 * rain, 75 * rain, 75 * rain, 6 * rain);
				context.clip();
				context.drawImage(arrImages[1], 27 * rain, 94 * rain, 75 * rain, 75 * rain);
				context.draw(true, function() {
					uni.canvasToTempFilePath({
						canvasId: 'activityCanvas',
						fileType: 'png',
						destWidth: 594,
						destHeight: 850,
						success: function(res) {
							// uni.hideLoading();
							successFn && successFn(res.tempFilePath);
						}
					})
				});

			},
			fail: function(err) {
				console.log('失败', err)
				uni.hideLoading();
				that.Tips({
					title: '无法获取图片信息'
				});
			}
		})
	},

	/**
	 * 图片圆角设置
	 * @param string x x轴位置
	 * @param string y y轴位置
	 * @param string w 图片宽
	 * @param string y 图片高
	 * @param string r 圆角值
	 */
	handleBorderRect(ctx, x, y, w, h, r) {
		ctx.beginPath();
		// 左上角
		ctx.arc(x + r, y + r, r, Math.PI, 1.5 * Math.PI);
		ctx.moveTo(x + r, y);
		ctx.lineTo(x + w - r, y);
		ctx.lineTo(x + w, y + r);
		// 右上角
		ctx.arc(x + w - r, y + r, r, 1.5 * Math.PI, 2 * Math.PI);
		ctx.lineTo(x + w, y + h - r);
		ctx.lineTo(x + w - r, y + h);
		// 右下角
		ctx.arc(x + w - r, y + h - r, r, 0, 0.5 * Math.PI);
		ctx.lineTo(x + r, y + h);
		ctx.lineTo(x, y + h - r);
		// 左下角
		ctx.arc(x + r, y + h - r, r, 0.5 * Math.PI, Math.PI);
		ctx.lineTo(x, y + r);
		ctx.lineTo(x + r, y);

		ctx.fill();
		ctx.closePath();
	},

	/*
	 * 单图上传
	 * @param object opt
	 * @param callable successCallback 成功执行方法 data 
	 * @param callable errorCallback 失败执行方法 
	 */
	uploadImageOne: function(opt, successCallback, errorCallback) {
		let that = this;
		if (typeof opt === 'string') {
			let url = opt;
			opt = {};
			opt.url = url;
		}
		let count = opt.count || 1,
			sizeType = opt.sizeType || ['compressed'],
			sourceType = opt.sourceType || ['album', 'camera'],
			is_load = opt.is_load || true,
			uploadUrl = opt.url || '',
			inputName = opt.name || 'pics',
			pid = opt.pid,
			model = opt.model;

		uni.chooseImage({
			count: count, //最多可以选择的图片总数  
			sizeType: sizeType, // 可以指定是原图还是压缩图，默认二者都有  
			sourceType: sourceType, // 可以指定来源是相册还是相机，默认二者都有  
			success: function(res) {
				//启动上传等待中...  
				uni.showLoading({
					title: '图片上传中',
				});
				let urlPath = HTTP_REQUEST_URL + '/api/front/upload/image' + "?model=" + model +
					"&pid=" + pid
				let localPath = res.tempFilePaths[0];
				uni.uploadFile({
					url: urlPath,
					filePath: localPath,
					name: inputName,

					header: {
						// #ifdef MP
						"Content-Type": "multipart/form-data",
						// #endif
						[TOKENNAME]: store.state.app.token
					},
					success: function(res) {
						uni.hideLoading();
						if (res.statusCode == 403) {
							that.Tips({
								title: res.data
							});
						} else {
							let data = res.data ? JSON.parse(res.data) : {};
							if (data.code == 200) {
								data.data.localPath = localPath;
								successCallback && successCallback(data)
							} else {
								errorCallback && errorCallback(data);
								that.Tips({
									title: data.message
								});
							}
						}
					},
					fail: function(res) {
						uni.hideLoading();
						that.Tips({
							title: '上传图片失败'
						});
					}
				})
			}
		})
	},
	/**
	 * 小程序头像获取上传
	 * @param uploadUrl 上传接口地址
	 * @param filePath 上传文件路径 
	 * @param successCallback success回调 
	 * @param errorCallback err回调
	 */
	uploadImgs(filePath, opt, successCallback, errorCallback) {
		let that = this;
		if (typeof opt === 'string') {
			let url = opt;
			opt = {};
			opt.url = url;
		}
		let count = opt.count || 1,
			sizeType = opt.sizeType || ['compressed'],
			sourceType = opt.sourceType || ['album', 'camera'],
			is_load = opt.is_load || true,
			uploadUrl = opt.url || '',
			inputName = opt.name || 'pics',
			pid = opt.pid,
			model = opt.model;
		let urlPath = HTTP_REQUEST_URL + '/api/front/upload/image' + "?model=" + model +
			"&pid=" + pid
		uni.uploadFile({
			url: urlPath,
			filePath: filePath,
			name: inputName,
			formData: {
				'filename': inputName
			},
			header: {
				// #ifdef MP
				"Content-Type": "multipart/form-data",
				// #endif
				[TOKENNAME]: store.state.app.token
			},
			success: function(res) {
				uni.hideLoading();
				if (res.statusCode == 403) {
					that.Tips({
						title: res.data
					});
				} else {
					let data = res.data ? JSON.parse(res.data) : {};
					if (data.code == 200) {
						successCallback && successCallback(data)
					} else {
						errorCallback && errorCallback(data);
						that.Tips({
							title: data.message
						});
					}
				}
			},
			fail: function(res) {
				uni.hideLoading();
				that.Tips({
					title: '上传图片失败'
				});
			}
		})
	},
	/**
	 * 处理服务器扫码带进来的参数
	 * @param string param 扫码携带参数
	 * @param string k 整体分割符 默认为：&
	 * @param string p 单个分隔符 默认为：=
	 * @return object
	 * 
	 */
	// #ifdef MP
	getUrlParams: function(param, k, p) {
		if (typeof param != 'string') return {};
		k = k ? k : '&'; //整体参数分隔符
		p = p ? p : '='; //单个参数分隔符
		var value = {};
		if (param.indexOf(k) !== -1) {
			param = param.split(k);
			for (var val in param) {
				if (param[val].indexOf(p) !== -1) {
					var item = param[val].split(p);
					value[item[0]] = item[1];
				}
			}
		} else if (param.indexOf(p) !== -1) {
			var item = param.split(p);
			value[item[0]] = item[1];
		} else {
			return param;
		}
		return value;
	},
	/**根据格式组装公共参数
	 * @param {Object} value
	 */
	formatMpQrCodeData(value) {
		let values = value.split(',');
		let result = {}
		values.forEach(item => {
			let arr = item.split(':');
			result[arr[0]] = arr[1];
		})
		result['spread'] = result['pid'];
		return result;
	},
	// #endif
	/*
	 * 合并数组
	 */
	SplitArray(list, sp) {
		if (typeof list != 'object') return [];
		if (sp === undefined) sp = [];
		for (var i = 0; i < list.length; i++) {
			sp.push(list[i]);
		}
		return sp;
	},
	trim(str) {
		return String.prototype.trim.call(str);
	},
	$h: {
		//除法函数，用来得到精确的除法结果
		//说明：javascript的除法结果会有误差，在两个浮点数相除的时候会比较明显。这个函数返回较为精确的除法结果。
		//调用：$h.Div(arg1,arg2)
		//返回值：arg1除以arg2的精确结果
		Div: function(arg1, arg2) {
			arg1 = parseFloat(arg1);
			arg2 = parseFloat(arg2);
			var t1 = 0,
				t2 = 0,
				r1, r2;
			try {
				t1 = arg1.toString().split(".")[1].length;
			} catch (e) {}
			try {
				t2 = arg2.toString().split(".")[1].length;
			} catch (e) {}
			r1 = Number(arg1.toString().replace(".", ""));
			r2 = Number(arg2.toString().replace(".", ""));
			return this.Mul(r1 / r2, Math.pow(10, t2 - t1));
		},
		//加法函数，用来得到精确的加法结果
		//说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
		//调用：$h.Add(arg1,arg2)
		//返回值：arg1加上arg2的精确结果
		Add: function(arg1, arg2) {
			arg2 = parseFloat(arg2);
			var r1, r2, m;
			try {
				r1 = arg1.toString().split(".")[1].length
			} catch (e) {
				r1 = 0
			}
			try {
				r2 = arg2.toString().split(".")[1].length
			} catch (e) {
				r2 = 0
			}
			m = Math.pow(100, Math.max(r1, r2));
			return (this.Mul(arg1, m) + this.Mul(arg2, m)) / m;
		},
		//减法函数，用来得到精确的减法结果
		//说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的减法结果。
		//调用：$h.Sub(arg1,arg2)
		//返回值：arg1减去arg2的精确结果
		Sub: function(arg1, arg2) {
			arg1 = parseFloat(arg1);
			arg2 = parseFloat(arg2);
			var r1, r2, m, n;
			try {
				r1 = arg1.toString().split(".")[1].length
			} catch (e) {
				r1 = 0
			}
			try {
				r2 = arg2.toString().split(".")[1].length
			} catch (e) {
				r2 = 0
			}
			m = Math.pow(10, Math.max(r1, r2));
			//动态控制精度长度
			n = (r1 >= r2) ? r1 : r2;
			return ((this.Mul(arg1, m) - this.Mul(arg2, m)) / m).toFixed(n);
		},
		//乘法函数，用来得到精确的乘法结果
		//说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
		//调用：$h.Mul(arg1,arg2)
		//返回值：arg1乘以arg2的精确结果
		Mul: function(arg1, arg2) {
			arg1 = parseFloat(arg1);
			arg2 = parseFloat(arg2);
			var m = 0,
				s1 = arg1.toString(),
				s2 = arg2.toString();
			try {
				m += s1.split(".")[1].length
			} catch (e) {}
			try {
				m += s2.split(".")[1].length
			} catch (e) {}
			return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
		},
	},
	// 获取地理位置;
	$L: {
		async getLocation() {
			let status
			// #ifdef APP-PLUS
			status = await this.checkPermission();
			if (status !== 1) {
				return;
			}
			// #endif
			// #ifdef MP-WEIXIN || MP-TOUTIAO || MP-QQ
			status = await this.getSetting();
			if (status === 2) {
				this.openSetting();
				return;
			}
			// #endif

			this.doGetLocation();
		},
		doGetLocation() {
			uni.getLocation({
				success: (res) => {
					uni.removeStorageSync('CACHE_LONGITUDE');
					uni.removeStorageSync('CACHE_LATITUDE');
					uni.setStorageSync('CACHE_LONGITUDE', res.longitude);
					uni.setStorageSync('CACHE_LATITUDE', res.latitude);
				},
				fail: (err) => {
					// #ifdef MP-BAIDU
					if (err.errCode === 202 || err.errCode === 10003) { // 202模拟器 10003真机 user deny
						this.openSetting();
					}
					// #endif
					// #ifndef MP-BAIDU
					if (err.errMsg.indexOf("auth deny") >= 0) {
						uni.showToast({
							title: "访问位置被拒绝"
						})
					} else {
						uni.showToast({
							title: err.errMsg
						})
					}
					// #endif
				}
			})
		},
		getSetting: function() {
			return new Promise((resolve, reject) => {
				uni.getSetting({
					success: (res) => {
						if (res.authSetting['scope.userLocation'] === undefined) {
							resolve(0);
							return;
						}
						if (res.authSetting['scope.userLocation']) {
							resolve(1);
						} else {
							resolve(2);
						}
					}
				});
			});
		},
		openSetting: function() {
			uni.openSetting({
				success: (res) => {
					if (res.authSetting && res.authSetting['scope.userLocation']) {
						this.doGetLocation();
					}
				},
				fail: (err) => {}
			})
		},
		async checkPermission() {
			let status = permision.isIOS ? await permision.requestIOS('location') :
				await permision.requestAndroid('android.permission.ACCESS_FINE_LOCATION');

			if (status === null || status === 1) {
				status = 1;
			} else if (status === 2) {
				uni.showModal({
					content: "系统定位已关闭",
					confirmText: "确定",
					showCancel: false,
					success: function(res) {}
				})
			} else if (status.code) {
				uni.showModal({
					content: status.message
				})
			} else {
				uni.showModal({
					content: "需要定位权限",
					confirmText: "设置",
					success: function(res) {
						if (res.confirm) {
							permision.gotoAppSetting();
						}
					}
				})
			}
			return status;
		},
	},

	toStringValue: function(obj) {
		if (obj instanceof Array) {
			var arr = [];
			for (var i = 0; i < obj.length; i++) {
				arr[i] = toStringValue(obj[i]);
			}
			return arr;
		} else if (typeof obj == 'object') {
			for (var p in obj) {
				obj[p] = toStringValue(obj[p]);
			}
		} else if (typeof obj == 'number') {
			obj = obj + '';
		}
		return obj;
	},

	/*
	 * 替换域名
	 */
	setDomain: function(url) {
		url = url ? url.toString() : '';
		if (url.indexOf("https://") > -1) return url;
		else return url.replace('http://', 'https://');
	},



	/**
	 * 姓名除了姓显示其他
	 */
	formatName: function(str) {
		return str.substr(0, 1) + new Array(str.length).join('*');
	},

	/**
	 * 微信地址导入
	 * @param uploadUrl 上传接口地址
	 * @param filePath 上传文件路径 
	 * @param successCallback success回调 
	 * @param errorCallback err回调
	 */
	addressWxImport() {
		let that = this;
		uni.showLoading({
			title: '加载中...'
		});

		return new Promise((resolve, reject) => {
			uni.authorize({
				scope: 'scope.address',
				success: function(res) {
					uni.hideLoading();
					uni.chooseAddress({
						success(resd) {
							resolve(resd);
						},
						fail: function(err) {
							if (err.errMsg == 'chooseAddress:cancel') return that.Tips({
								title: '取消选择'
							});
						},
					})
				},
				fail: function(err) {
					uni.hideLoading();
					uni.showModal({
						title: '您已拒绝导入微信地址权限',
						content: '是否进入权限管理，调整授权？',
						success(err) {
							if (err.confirm) {
								uni.openSetting({
									success: function(err) {
										console.log(err.authSetting)
									}
								});
							} else if (err.cancel) {
								return that.Tips({
									title: '已取消！'
								});
							}
						}
					})
				}
			})
		});
	},

	/**
	 * 文本换行处理
	 * @param {Object} ctx - canvas上下文
	 * @param {String} text - 要换行的文本
	 * @param {Number} maxWidth - 最大宽度
	 * @returns {Array} 换行后的文本数组
	 */
	wrapText: function(ctx, text, maxWidth) {
		const words = text.split('');
		const lines = [];
		let currentLine = '';

		for (let i = 0; i < words.length; i++) {
			const testLine = currentLine + words[i];
			const metrics = ctx.measureText(testLine);
			const testWidth = metrics.width;

			if (testWidth > maxWidth && currentLine !== '') {
				lines.push(currentLine);
				currentLine = words[i];
			} else {
				currentLine = testLine;
			}
		}

		if (currentLine !== '') {
			lines.push(currentLine);
		}

		return lines;
	},

	/**
	 * 单行文本截断处理（超出宽度显示省略号）
	 * @param {Object} ctx - canvas上下文
	 * @param {String} text - 要处理的文本
	 * @param {Number} maxWidth - 最大宽度
	 * @returns {String} 处理后的文本（如果超出则添加省略号）
	 */
	truncateText: function(ctx, text, maxWidth) {
		if (!text) return '';
		
		// 测量原始文本宽度
		const metrics = ctx.measureText(text);
		if (metrics.width <= maxWidth) {
			return text;
		}
		
		// 如果超出宽度，逐步减少字符并添加省略号
		const ellipsis = '...';
		const ellipsisWidth = ctx.measureText(ellipsis).width;
		
		let truncatedText = text;
		while (truncatedText.length > 0) {
			const testText = truncatedText + ellipsis;
			const testMetrics = ctx.measureText(testText);
			
			if (testMetrics.width <= maxWidth) {
				return testText;
			}
			
			truncatedText = truncatedText.slice(0, -1);
		}
		
		return ellipsis;
	}
}
