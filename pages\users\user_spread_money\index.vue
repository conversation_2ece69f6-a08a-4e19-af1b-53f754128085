<template>
	<view :data-theme="theme">
		<view class='commission-details'>
			<view class='promoterHeader'>
				<view class='headerCon acea-row row-between-wrapper'>
					<view>
						<view class='name'>{{name}}</view>
						<view class='money' v-if="recordType == 4">￥<text class='num'>{{extractCount}}</text></view>
						<view class='money' v-else>￥<text class='num'>{{commissionCount}}</text></view>
					</view>
					<view class='iconfont icon-jinbi1'></view>
				</view>
			</view>
			<view class='sign-record' v-if="recordType == 4">
				<block v-for="(item,index) in recordList" :key="index" v-if="recordList.length>0">
					<view class='list borderPad'>
						<view class='item'>
							<view class='data'>{{item.month}}</view>
							<view class='listn borRadius14'>
								<block v-for="(child,indexn) in item.list" :key="indexn">
									<view class='itemn acea-row row-between-wrapper'>
										<view>
											<view class='name line1'>{{child.auditStatus | statusFilter(child.accountStatus)}}</view>
											<view>{{child.createTime}}</view>
										</view>
										<view class='num font_color'>{{child.closingPrice}}</view>
									</view>
								</block>
							</view>
						</view>
					</view>
				</block>
				<view class="no-record" v-if="recordList.length == 0">
					<emptyPage title='暂无结算记录~'></emptyPage>
				</view>
			</view>
			<view class='sign-record' v-else>
				<block v-for="(item,index) in recordList" :key="index" v-if="recordList.length>0">
					<view class='list borderPad'>
						<view class='item'>
							<view class='data'>{{item.month}}</view>
							<view class='listn borRadius14'>
								<block v-for="(child,indexn) in item.list" :key="indexn">
									<view class='itemn acea-row row-between-wrapper'>
										<view>
											<view class='name line1'>{{child.title}}</view>
											<view>{{child.updateTime}}</view>
										</view>
										<view class='num font_color' v-if="child.type == 1">+{{child.price}}
										</view>
										<view class='num' v-else>-{{child.price}}</view>
									</view>
								</block>
							</view>
						</view>
					</view>
				</block>
				<view class="no-record" v-if="recordList.length == 0">
					<emptyPage title='暂无佣金记录~'></emptyPage>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getCommissionInfo,
		getClosingRecordApi,
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import emptyPage from '@/components/emptyPage.vue'
	import {setThemeColor} from '@/utils/setTheme.js'
	const app = getApp();
	export default {
		components: {
			emptyPage
		},
		filters: {
			statusFilter(auditStatus, accountStatus) {
				if (accountStatus == 1) {
					return '已提现'
				}
				const statusMap = {
					'0': '待审核',
					'1': '审核通过',
					'2': '审核失败',
				}
				return statusMap[auditStatus]
			}
		},
		data() {
			return {
				name: '',
				type: 0,
				page: 1,
				limit: 20,
				recordList: [],
				recordType: 0,
				statuss: false,
				extractCount: 0,
				theme:app.globalData.theme,
				commissionCount:0,
				bgColor:'#e93323'
			};
		},
		computed: mapGetters(['isLogin']),
		onLoad(options) {
			if (this.isLogin) {
				this.type = options.type;
				this.extractCount = options.extractCount;
				this.commissionCount = options.commissionCount;
			} else {
				toLogin();
			}
			let that = this;
			that.bgColor = setThemeColor();
			uni.setNavigationBarColor({
				frontColor: '#ffffff',
				backgroundColor: that.bgColor,
			});
		},
		onShow: function() {
			let type = this.type;
			if (type == 1) {
				uni.setNavigationBarTitle({
					title: "结算记录"
				});
				this.name = '结算总额';
				this.recordType = 4;
				this.getList();
			} else if (type == 2) {
				uni.setNavigationBarTitle({
					title: "佣金记录"
				});
				this.name = '佣金余额';
				this.recordType = 3;
				this.getRecordList();
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none',
					duration: 1000,
					mask: true,
					success: function(res) {
						setTimeout(function() {
							// #ifndef H5
							uni.navigateBack({
								delta: 1,
							});
							// #endif
							// #ifdef H5
							history.back();
							// #endif

						}, 1200)
					},
				});
			}

		},
		methods: {
			getList: function() {
				let that = this;
				let page = that.page;
				let limit = that.limit;
				let statuss = that.statuss;
				let recordList = that.recordList;
				
				if (statuss == true) return;
				getClosingRecordApi({
					page: page,
					limit: limit
				}).then(res => {
					if (res.data) {
						let moreList = res.data || [];
						let len = 0;
						moreList.forEach(moreItem => {
							len += moreItem.list.length;
							let newList = true;
							
							recordList.forEach((item, index) => {
								if (moreItem.month == item.month) {
									item.list = item.list.concat(moreItem.list);
									newList = false; 
								}
							});
							if (newList) {
								recordList.push(moreItem)
							}
						});
						
						that.statuss = limit > len;
						that.page = page + 1;
						that.$set(that, 'recordList', recordList);
					}
				});
			},
			getRecordList: function() {
				let that = this;
				let page = that.page;
				let limit = that.limit;
				let statuss = that.statuss;
				let recordList = that.recordList;

				if (statuss == true) return;
				getCommissionInfo({
					page: page,
					limit: limit
				}).then(res => {
					if (res.data.list) {
						let moreList = res.data.list || [];
						let len = 0;
						moreList.forEach(moreItem => {
							len += moreItem.list.length;
							let newList = true;
							
							recordList.forEach((item, index) => {
								if (moreItem.month == item.month) {
									item.list = item.list.concat(moreItem.list);
									newList = false; 
								}
							});
							if (newList) {
								recordList.push(moreItem)
							}
						});
						
						that.statuss = limit > len;
						that.page = page + 1;
						that.$set(that, 'recordList', recordList);
					}
				});
			}
		},
		onReachBottom: function() {
			if (this.recordType == 3) {
				this.getRecordList();
			}
			if (this.recordType == 4) {
				this.getList();
			}
		}
	}
</script>

<style scoped lang="scss">
	.commission-details .promoterHeader .headerCon .money {
		font-size: 36rpx;
	}
	.promoterHeader{
		@include main_bg_color(theme);
	}
	.commission-details .promoterHeader .headerCon .money .num {
		font-family: 'Guildford Pro';
	}
	.font_color{
		color: #E93323 !important;
	}
	.sign-record {
		padding-bottom: 20rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom) / 2);
	}
	.no-record {
		width: 100%;
		height: calc(100% - 220rpx);
		position: fixed;
		display: flex;
		align-items: center;
		justify-content: center;
		
		/deep/.empty-box {
			margin: 0 !important;
			padding: 0 !important;
			top: -150rpx;
		}
	}
</style>
