// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import {
	getMerCollectAddApi,
	getMerCollectCancelApi
} from '@/api/merchant.js';

import $store from "@/store"

/**
 * 关注、取消关注商户
 */
export function followMer(follow, id) {
	return new Promise((resolve, reject) => {
		if (follow) {
			getMerCollectCancelApi(id).then(res => {
				resolve(res);
			}).catch(err => {
				this.$util.Tips({
					title: err
				});
			});
		} else {
			getMerCollectAddApi(id).then(res => {
				resolve(res);
			}).catch(err => {
				this.$util.Tips({
					title: err
				});
			});
		}
	});
}


/**
 * 自动关注当前门店
 */
export function autoFollow(merId) {
	let isLogin = $store.getters.isLogin;
	return Promise.resolve((isLogin && merId) ? followMer(false, merId) : null);
}