<template>
	<view class="page-wrapper history" :data-theme="theme">
		<view class="history_count">
			<block v-if="list.length>0">
				<!-- <view class="history_header acea-row">
					<text>共{{total}}条，最多为您保存100条</text>
				</view> -->
				<view class="list" v-for="(item,index) in list">
					<view class="item_time">
						<view>{{item.date}}</view>
					</view>
					<!-- <view class="item_main acea-row">
						<view class="item acea-row hot-recommend-item-box" :class="{gary :(!itemn.isDel && itemn.isShow ==1) }"
							v-for="(itemn,indexn) in item.list">
							<view class="item item_count" @click="goPage(itemn)">
								<easy-loadimage class="easy-img" mode="widthFix" :image-src="itemn.image">
								</easy-loadimage>
								<view class="info">
									<view class="msg">
										<block v-if="!itemn.isDel && itemn.isShow">
											<view class="price line2"><text></text>￥{{itemn.price}}</view>
										</block>
										<block v-else-if="itemn.isDel">
											<view class="tips">该商品已删除</view>
										</block>
										<block v-else>
											<view class="tips">该商品已下架</view>
										</block>
									</view>
								</view>
							</view>
						</view>
					</view> -->
					<custom-waterfalls-flow ref="waterfallsFlowRef" :value="item.list" :column="column"
						:columnSpace="2.6" hideImageKey="image" :seat="2" @wapperClick="wapperClick" @imageClick="imageClick"
						@loaded="loaded">
						<view class="hot-recommend-item-box" v-for="(item,index) in item.list" 
							slot="slot{{index}}">
							<view class="" v-if="!itemn.isDel && itemn.isShow">
								<view class="hot-recommend-item-name">{{item.name}}</view>
								<view class="hot-recommend-item-intro">{{item.intro}}</view>
								<view class="hot-recommend-item-price-box">
									<view class="item-price">¥{{item.price}}</view>
									<view class="item-old-price" v-if="item.otPrice">¥{{item.otPrice}}</view>
									<image v-if="item.type == '2'" class="item-icon" src="/static/imgs/home/<USER>"
										mode="aspectFit">
									</image>
									<image v-else-if="item.type == '1'" class="item-icon"
										src="/static/imgs/home/<USER>" mode="aspectFit">
									</image>
									<image v-else-if="item.type == '3'" class="item-icon" src="/static/imgs/home/<USER>"
										mode="aspectFit">
									</image>
								</view>
							</view>
							<!-- <block v-else-if="itemn.isDel">
								<view class="tips">该商品已删除</view>
							</block>
							<block v-else>
								<view class="tips">该商品已下架</view>
							</block> -->
							
						</view>
					</custom-waterfalls-flow>
					
					
				</view>
				
					
					
				
				
			</block>
			<view class="no-history" v-if="(list.length==0 && !loading )">
				<emptyPage title="暂无浏览记录~"></emptyPage>
			</view>
			<view class='loadingicon acea-row row-center-wrapper'>
				<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import emptyPage from '@/components/emptyPage.vue'
	import {
		browseRecordApi,
		historyDelete,
		historyBatchDelete,
		historyBatchCollect
	} from '@/api/user.js'
	import {
		goShopDetail
	} from '@/libs/order.js'
	import {
		mapGetters
	} from "vuex";
	import animationType from '@/utils/animationType.js'
	import easyLoadimage from '@/components/base/easy-loadimage.vue';
	let app = getApp();
	export default {
		components: {
			emptyPage,
			easyLoadimage
		},
		computed: mapGetters(['uid', 'globalData']),
		data() {
			return {
					column: 2, // 瀑布流列数
				list: [],
				allArr: [],
				total: 0,
				theme: app.globalData.theme,
				loading: true
			}
		},
		onLoad() {
			this.getList();
		},
		methods: {
			// 瀑布流加载完成
			loaded() {
				console.log('瀑布流加载完成')
			},
			// 瀑布流单项点击事件
			wapperClick(item) {
				// console.log('瀑布流单项点击事件', item);
				// uni.setStorageSync('storeId', 0)
				// uni.navigateTo({
				// 	url: `/pages/goods/goods_details/index?id=${item.productId}`
				// })
				goShopDetail(item.productId);
			},
			// 瀑布流图片点击事件
			imageClick(item) {
				// console.log('瀑布流图片点击事件', item);
				// uni.setStorageSync('storeId', 0)
				// uni.navigateTo({
				// 	url: `/pages/goods/goods_details/index?id=${item.productId}`
				// })
				goShopDetail(item.productId);
			},
			getList() {
				this.list = [];
				browseRecordApi().then(({
					data
				}) => {
					this.list = data;
				    data.map(item => {
						this.total += item.list.length;
						item.date = item.date.slice(5, 7)+'月'+item.date.slice(8, 10)+'日' ;
					})
					this.loading = false;
				}).catch(err => {
					this.loading = false;
					return this.$util.Tips({
						title: err
					});
				});
			},
			goPage(item) {
				goShopDetail(item.productId);
			}
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		}
	}
</script>

<style lang="scss">
	page {
		background: #ffffff;
	}

	.history {
		margin-bottom: 96rpx;

		.history_count {
			padding: 0 30rpx;
		}

		.history_header {
			justify-content: space-between;
			margin: 30rpx 0;

			text {
				font-size: 26rpx;
				color: #666666;
			}

			.text {
				color: var(--view-theme);
			}
		}
	
	.list {
			margin-top: 20rpx;

			.item_time {
				font-size: 36rpx;
				color: #282828;
				font-weight: bold;
				display: flex;
				align-items: center;
			}

			.item_main {
				margin-top: 30rpx;
			}
		}

		.item {
			width: 217rpx;
			border-radius: 16rpx;
			margin: 0 20rpx 20rpx 0;
			position: relative;

			&:nth-child(3n) {
				margin-right: 0;
			}

			/deep/image,
			/deep/.easy-loadimage,
			uni-image {
				width: 217rpx;
				height: 217rpx;
				border-radius: 16rpx;
			}

			.info {
				margin-top: 20rpx;
				color: var(--view-priceColor);
				font-size: 24rpx;
			}
		}
	}

	/deep/.loadfail-img,
	.easy-img {
		width: 217rpx;
		height: 217rpx;
		border-radius: 16rpx;
		display: block;
	}

	.item_time .checkbox .iconfont {
		font-size: 38rpx;
		color: #999999;
		margin-right: 10rpx;

		&.icon-xuanzhong1 {
			color: var(--view-theme);
		}
	}

	.item_main .checkbox .iconfont {
		font-size: 40rpx;
		color: #DEDEDE;
		position: absolute;
		right: 12rpx;
		top: 12rpx;
		z-index: 10;
		border-radius: 100%;

		&.icon-weixuanzhong {
			&::after {
				content: "";
				display: block;
				width: 36rpx;
				height: 36rpx;
				background: rgba(0, 0, 0, .38);
				position: absolute;
				top: 2rpx;
				left: 2rpx;
				border-radius: 100%;
				border: 1rpx solid #DEDEDE;
			}
		}
	}

	.history .item .icon-xuanzhong1 {
		color: var(--view-theme);
	}

	.history .footer {
		z-index: 99;
		width: 100%;
		height: 96rpx;
		background-color: #ffffff;
		position: fixed;
		padding: 0 20rpx;
		box-sizing: border-box;
		border-top: 1rpx solid #eee;
		bottom: var(--window-bottom);
	}

	.area-edit {
		justify-content: center;

		.area-item {
			width: 50%;
			text-align: center;
			position: relative;
			align-items: center;
			color: #333333;

			.text {
				position: relative;
				top: -1px;
			}

			.iconfont {
				color: #333333;
			}

			&:nth-child(1) {
				&::after {
					content: '';
					display: inline-block;
					width: 2rpx;
					height: 42rpx;
					background: #CCCCCC;
					position: absolute;
					top: 0;
					right: 0;
			}
				}
		}
	}

	.history .footer .checkAll {
		font-size: 28rpx;
		color: #282828;
		margin-left: 16rpx;
	}

	.allcheckbox .iconfont {
		margin-right: 11px;
		font-size: 40rpx;
		color: #cccccc;
	}

	.allcheckbox .icon-xuanzhong1 {
		color: var(--view-theme);
	}

	.history .footer .button .bnt {
		font-size: 28rpx;
		color: #999;
		border-radius: 50rpx;
		border: 1px solid #999;
		width: 160rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;

		&.collect_btn {
			color: var(--view-theme);
			border-color: var(--view-theme);
		}
	}
	
	.no-history {
		width: calc(100% - 60rpx);
		height: 100%;
		position: fixed;
		display: flex;
		align-items: center;
		justify-content: center;
		top: -100rpx;

		/deep/.empty-box {
			margin: 0 !important;
			padding: 0 !important;
		}
	}
	
	
	.hot-recommend-item-box {
		width: 100%;
		padding: 26rpx 20rpx 10rpx 20rpx;
	
		.hot-recommend-item-name {
			margin-bottom: 10rpx;
			width: 100%;
			font-size: 28rpx;
			font-weight: 700;
			color: #333333;
			line-clamp: 2;
			overflow: hidden;
			text-overflow: ellipsis;
			text-overflow: -webkit-ellipsis-lastline;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			-webkit-box-orient: vertical;
		}
	
		.hot-recommend-item-intro {
			width: 100%;
			font-size: 24rpx;
			color: rgba(102, 102, 102, 0.60);
			line-clamp: 2;
			overflow: hidden;
			text-overflow: ellipsis;
			text-overflow: -webkit-ellipsis-lastline;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			-webkit-box-orient: vertical;
		}
	
		.hot-recommend-item-price-box {
			margin-top: 10rpx;
			display: flex;
			align-items: center;
			width: 100%;
	
			.item-price {
				margin-right: 10rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #333333;
			}
	
			.item-old-price {
				font-size: 28rpx;
				text-decoration: line-through;
				color: #999999;
			}
	
			.item-icon {
				width: 64rpx;
				height: 24rpx;
			}
		}
	
		.desc {
			font-size: 24rpx;
			color: #666;
		}
	
	}
</style>
