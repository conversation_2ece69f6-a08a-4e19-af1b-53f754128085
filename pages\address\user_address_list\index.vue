<template>
	<view class="user-address-list" :data-theme="theme">
		<view class="list">
			<view v-for="(item, index) in addressList" :key="index" class="swipe-item-wrapper">
				<view
					class="swipe-item-content"
					:style="{ transform: `translateX(${item.translateX || 0}rpx)` }"
					@touchstart="touchStart($event, index)"
					@touchmove="touchMove($event, index)"
					@touchend="touchEnd($event, index)"
				>
					<view class='item'>
						<view class='address-details' @click='checkAddress(index)'>
							<view class='info'>
								<text class='name'>{{item.realName}}</text>
								<text class='phone'>{{item.phone}}</text>
							</view>
							<view class='address'>
								<text class='tag' v-if="item.isDefault">默认</text>
								<text class='text'>{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</text>
							</view>
						</view>
						<view class='edit-icon' @click.stop='editAddress(item.id)'>
							<text class='iconfont icon-bianji'></text>
						</view>
					</view>
				</view>
				<view v-if="!item.isDefault" class="swipe-delete-btn" @click="delAddress(index)">
					<text class="swipe-delete-text">删除</text>
				</view>
			</view>
		</view>
		<view class='loadingicon acea-row row-center-wrapper' v-if="addressList.length > 0">
			<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
		</view>
		<view class="no-address" v-if="addressList.length == 0 && !loading">
			<emptyPage v-if="addressList.length == 0 && !loading" src="/pages/aastatictoT/static/images/noAddress.png" title="暂无添加地址~">
			</emptyPage>
		</view>
		<view class='footer'>
			<view class='add-btn' @click='addAddress'>新建收货地址</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getAddressList,
		setAddressDefault,
		delAddress,
		editAddress,
		postAddress
	} from '@/api/user.js';
	import emptyPage from '@/components/emptyPage.vue'
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		components: {
			emptyPage,
		},
		data() {
			return {
				isCheck: false,
				addressList: [],
				loading: false,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 20,
				theme: app.globalData.theme,
				touchData: {
					startX: 0,
					startY: 0,
					isMoving: false,
					deleteWidth: 160 // rpx
				}
			};
		},
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getUserAddress(true);
					}
				},
				deep: true
			}
		},
		onLoad(options) {
			// 支持两种参数：isCheck 和 type=select
			this.isCheck = options.isCheck || options.type === 'select' || false;
			if (this.isLogin) {
				this.getAddressList(true);
			} else {
				toLogin();
			}
		},
		onShow: function() {
			let that = this;
			that.getAddressList(true);
		},
		methods: {
			touchStart(e, index) {
				this.touchData.isMoving = false;
				let item = this.addressList[index];
				if (item.isDefault) return;
				
				let touch = e.touches[0];
				this.touchData.startX = touch.clientX;
				this.touchData.startY = touch.clientY;
				this.resetAllSwipeState(index);
			},
			
			touchMove(e, index) {
				let item = this.addressList[index];
				if (item.isDefault) return;
			
				this.touchData.isMoving = true;
			
				let touch = e.touches[0];
				let deltaX = this.touchData.startX - touch.clientX;
				let deltaY = this.touchData.startY - touch.clientY;
			
				if (Math.abs(deltaY) > Math.abs(deltaX)) {
					return;
				}
			
				if (deltaX > 0) {
					let translateX = -Math.min(deltaX * 2, this.touchData.deleteWidth);
					this.$set(item, 'translateX', translateX);
				} else {
					this.$set(item, 'translateX', 0);
				}
			},
			
			touchEnd(e, index) {
				let item = this.addressList[index];
				if (item.isDefault || !this.touchData.isMoving) {
					return;
				};
				
				this.touchData.isMoving = false;
			
				let translateX = item.translateX || 0;
				let deleteWidth = this.touchData.deleteWidth;
			
				if (Math.abs(translateX) > deleteWidth / 2) {
					this.$set(item, 'translateX', -deleteWidth);
				} else {
					this.$set(item, 'translateX', 0);
				}
			},

			resetAllSwipeState(currentIndex) {
				this.addressList.forEach((item, index) => {
					if (index !== currentIndex && item.translateX !== 0) {
						this.$set(item, 'translateX', 0);
					}
				});
			},

			checkAddress: function(index) {
				if(this.touchData.isMoving) return;
				if (this.isCheck) {
					let address = this.addressList[index];
					
					// 构造地址数据，统一字段名
					const addressData = {
						id: address.id,
						name: address.realName,
						phone: address.phone,
						province: address.province,
						city: address.city,
						district: address.district,
						detail: address.detail,
						isDefault: address.isDefault
					};
					
					// 保存选中的地址到缓存
					uni.setStorageSync('selectedAddress', addressData);
					
					// 使用事件机制（兼容原有逻辑）
					uni.$emit('checkaddress', address);
					
					uni.navigateBack({
						delta: 1
					});
				}
			},
			/**
			 * 获取地址列表
			 * 
			 */
			getAddressList: function(isPage) {
				if (this.loading) return;
				this.loading = true;
				this.loadTitle = '';
				getAddressList().then(res => {
					const list = res.data.map(item => {
						item.translateX = 0;
						return item;
					});
					this.addressList = list;
					this.loading = false;
					this.addressList.length > 0 ? this.loadTitle = '我也是有底线的' : this.loadTitle = '';
				});
			},
			/**
			 * 编辑地址
			 */
			editAddress: function(id) {
				uni.navigateTo({
					url: '/pages/address/user_address/index?id=' + id
				})
			},
			/**
			 * 删除地址
			 */
			delAddress: function(index) {
				this.resetAllSwipeState();
				let that = this,
					address = this.addressList[index];
				if (address == undefined) return that.$util.Tips({
					title: '您删除的地址不存在!'
				});
				uni.showModal({
					content: '确定删除该地址',
					cancelText: "取消", // 取消按钮的文字  
					confirmText: "确定", // 确认按钮文字  
					showCancel: true, // 是否显示取消按钮，默认为 true
					confirmColor: '#f55850',
					success: (res) => {
						if (res.confirm) {
							delAddress(address.id).then(res => {
								that.addressList.splice(index, 1);
								if (that.addressList.length == 0) {
									that.loadTitle = '';
								}
								that.$set(that, 'addressList', that.addressList);
								that.$util.Tips({
									title: '删除成功',
									icon: 'success'
								});
							}).catch(err => {
								return that.$util.Tips({
									title: err
								});
							});
						} else {

						}
					},
				})
			},
			/**
			 * 新增地址
			 */
			addAddress: function() {
				uni.navigateTo({
					url: '/pages/address/user_address/index'
				})
			}
		},
		onReachBottom: function() {
			this.getAddressList();
		}
	}
</script>

<style lang="scss" scoped>
	.user-address-list {
		padding: 20rpx 30rpx 140rpx;
		background-color: #F5F5F5;
		min-height: 100vh;
	}

	.bg_color {
		@include main_bg_color(theme);
	}

	.list {}

	.swipe-item-wrapper {
		position: relative;
		overflow: hidden;
		border-radius: 14rpx;
		margin-bottom: 20rpx;
	}
	
	.swipe-item-content {
		position: relative;
		z-index: 2;
		background-color: #fff;
		transition: transform 0.3s ease;
	}
	
	.swipe-delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 160rpx;
		height: 100%;
		background-color: #BDFD5B;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1;
		color: #222222;
	}
	
	.swipe-delete-text {
		color: #222222;
		font-size: 28rpx;
	}

	.list .item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background-color: #fff;
	}

	.address-details {
		flex: 1;
	}

	.address-details .info {
		display: flex;
		align-items: baseline;
		margin-bottom: 10rpx;
	}

	.address-details .info .name {
		font-size: 30rpx;
		font-weight: bold;
		margin-right: 20rpx;
		color: #333;
	}

	.address-details .info .phone {
		font-size: 28rpx;
		color: #333;
	}

	.address-details .address {
		display: flex;
		align-items: center;
	}

	.address-details .address .tag {
		background: #BDFD5B;
		color: #222222;
		font-size: 20rpx;
		padding: 4rpx 10rpx;
		border-radius: 4rpx;
		margin-right: 10rpx;
		flex-shrink: 0;
	}

	.address-details .address .text {
		font-size: 26rpx;
		color: #666;
	}

	.edit-icon {
		padding: 10rpx;
		margin-left: 20rpx;
		border-left: 1px solid #EFEFEF;
		padding-left: 20rpx;
	}

	.edit-icon .iconfont {
		font-size: 36rpx;
		color: #888;
	}

	.footer {
		position: fixed;
		width: 100%;
		bottom: 0;
		left: 0;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
		background-color: #F5F5F5;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	}

	.footer .add-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		color: #222222;
		background-color: #BDFD5B;
		border-radius: 20rpx;
		font-size: 30rpx;
	}
	
	.no-address {
		margin-top: 100rpx;
	}
</style>
