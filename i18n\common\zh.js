// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
  userDrawer:{ //抽屉导航
    data:[
      {name:'首页'},
      {name:'搜索'},
      {name:'分类'},
      {name:'购物车'},
      {name:'收藏'},
      {name:'我的'},
      {name:'语言'},
    ],
    language:[
      {name:'中文'},
      {name:'英文'},
      {name:'法文'},
      {name:'泰语'},
      {name:'老挝语'},
    ]
  },
  page:{
    index:{ //首页
      mainNav:[
        {name:'分类'},
        {name:'收藏'},
        {name:'订单'},
        {name:'地址'},
      ],
      coupons:'优惠券',
      titProduct:'主打商品',
      titNow:'热卖商品',
      titCate:'特色分类',
      titBtn:'查看所有',
      titAlso:'可能喜欢',
      couponDesc:'订单金额满',
      received:'已领取',
      orMore:'更多',
      viewDetails:'查看详情',
      recommended: '特色商品',
      promotions: '促销商品',
      onView:'查看商品',
      collectSite: '收藏本站',
      applyFor: '申请入驻',
      mobileMall: '手机商城',
      shopStreet: '店铺街',
      customerService: '客服',
      top: '回到顶部',
      topOne: '排行榜',
      footer1: '品种齐全，购物轻松',
      footer2: '多仓直发，极速配送',
      footer3: '正品行货，精致服务',
      footer4: '天天低价，畅选无忧',
      address: '陕西省西安市未央区西咸大道启航时代广场A座1101-04室',
      icp: '西安众邦网络科技有限公司陕ICP备14011498号-3',
      
    },
    goodsDetail:{ //商品详情
      search:'搜索商品',
      sales:'销量',
      oldPrice:'原价',
      inventory:'库存',
      coupons:'优惠券',
      choose:'已选择',
      choose1:'请选择',
      Reviews:'用户评价',
      good:'好评',
      details:'商品详情',
      recommend:'优品推荐',
      home:'首页',
      like:'收藏',
      cart:'购物车',
      addCart:'加入购物车',
      buyNow:'立即购买',
      soldOut:'已售罄',
      sold: '已售',
      type:'种类型可选',
      navList:[
        {name:'商品'},
        {name:'评价'},
        {name:'推荐'},
        {name:'详情'}
      ],
      select:'已选商品',
      clear:'清空',
      settle:'去结算',
      num:'数量',
      confirm:'确定',
      cancel: '取消',
      assure:"保障服务"
    },
    goodsList:{
      navTitle:'商品列表',
      search:'搜索商品名称',
      default:'默认',
      price:'价格',
      sales:'销量',
      new:'新品',
      empty:'暂无商品，去看点别的吧',
      no: '已全部加载',
      more: '加载更多',
      nono: '我也是有底线的',
      nomer: '暂无店铺'
    },
    user:{
      navTitle:'我的',
      like:'我的收藏',
      integral:'我的积分',
      coupons:'优惠券',
      orderCenter:'订单中心',
      myOrder:'我的订单',
      placeLogin:'请点击登录',
      orderStatus:[
        {name:'待付款'},
        {name:'待发货'},
        {name:'待收货'},
        {name:'待评价'},
        {name:'售后/退款'},
        {name:'已完成'},
        {name:'已收货'}
      ],
      services:'其他服务',
      address:'地址管理',
      contact:'联系客服',
      applicationRecord: '店铺收藏',
      goodsCollection: '商品收藏',
      record: '申请记录',
      mineNav: [
        {name: '我的订单'},
        {name: '退款列表'},
        {name: '评价列表'}
      ],
      qunTitle: '商家营业执照信息',
      qunDesc: '根据国家工商总局《网络交易管理办法》要求对网店营业执照信息公示如下：',
      qunCode: '请输入图片验证码',
      submit: '提交',
    },
    goodsSearch:{
      navTitle:'商品搜索',
      place:'点击搜索商品',
      search:'搜索',
      hotSearch:'热门搜索',
      recommend:'热卖推荐',
      emptySearch:'无搜索条件,换个词试试吧',
      placeSearch:'请输入要搜索的商品',
      onSearch:'正在搜索中',
      goods:'商品',
      mer: '店铺',
      merchantType: '店铺类型',
      merchantClassify:'商户分类',
      sure :'确定',
      reset: '重置',
      open: '展开全部',
      up: '收起',
      sort: '排序',
      Default: '默认',
      Seller: '销量',
      Price: '价格',
      searchMer: '抱歉，没有找到您搜索的相关店铺~',
      searchGoods: '抱歉，没有找到您搜索的相关商品~',
    },
    goodsAddcart:{
      navTitle:'购物车',
      labelNav:'100%的产品质量保证和售后服务',
      buyNum:'购买数量',
      management:'管理',
      cancel:'取消',
      attribute:'属性',
      failureGoods:'失效商品',
      empty:'清空',
      failure:'失效',
      fallDesc:'该商品已失效',
      selectAll:'全选',
      buyNow:'立即下单',
      like:'收藏',
      delete:'删除',
      emptyCart:'暂无商品，去添加点什么吧',
      again: '请重新选择商品规格',
      reselect :'重选'
    },
    orderPayStatus:{
      success:'支付成功',
      fail:'支付失败',
      orderId:'订单编号',
      orderTime:'下单时间',// order of time
      payType:'支付方式',
      payPrice:'支付金额', //Pay the amount
      failReason:'失败原因', //The reason for failure
      viewOrder:'查看订单',//To view the order
      backHome:'返回首页'
    },
    orderDetails:{
      navTitle:'订单详情',
      item:'件商品',// item(s)
      Customer:'联系客服',
      orderId:'订单编号',
      copy:'复制',
      orderTime:'下单时间',
      refundTime: '退款时间',
      payStatus:'支付状态',
      payTrue:'已支付',
      payFalse:'未支付',
      payType:'支付方式',
      message:'买家留言',
      name:'收货人',
      phone:'联系电话',
      address:'收货地址',
      returnReason:'退款原因',
      returnMsg:'退款说明',
      returnImg: '退款凭证',
      sendMethod:'配送方式',
      sendOne:'发货', //send
      sendTwo:'送货', //The delivery
      CourierCompany:'快递公司',//Courier company
      CourierNo:'快递单号', //Courier number
      deliveryName:'配送人姓名',
      deliveryPhone:'联系电话',
      Virtual:'虚拟发货',//Virtual delivery
      deliveryMsg:'已发货，请注意查收',
      allPrice:'商品总价',
      freight:'运费',
      coupon:'优惠券抵扣', //Coupon deduction
      point:'积分抵扣', //Points deduction
      actualPayment:'实付款',//actual payment
      cancelOrder:'取消订单',
      nowPay:'立即付款',
      refund:'申请退款',
      logistics:'查看物流',
      confirm:'确认收货',
      delete:'删除订单',
      again:'再次购买',
      confirmInfo: '为保障权益，请收到货确认无误后，再确认收货',
      confirmDel: '确定删除该订单',
      confirmCancel: '确认取消该订单'
    },
    users:{
      userInfo:{
        navTitle:'设置',
        avatar:'头像',
        name:'昵称',
        email:'邮箱',
        password:'密码',
        save:'保存修改',
        logOut:'退出登录',
        phone:'电话号码',
        msg: '用户姓名不能为空',
        personalCenter: '个人中心',
      },
      orderConfirm:{
        setAddress:'设置收货地址',
        navTitle:'下单',
        items:'件商品',
        coupons:'优惠券',
        integral:'积分抵扣',
        freight:'快递费用',
        note:'备注信息',
        payPal:'立即结算',
        allPrice:'商品总价',
        remaining:'剩余积分',
        newIntegral:'当前积分',
        free:'免运费',
        placeNote:'请添加备注(150字以内)',
        CouponDeduction:'优惠券抵扣',
        total:'总计'
      },
      orderList:{
        navTitle:'我的订单',
        orderNum:'消费订单', //Cumulative order
        total:'总消费',//Total consumption
        orderStatus:[
          {name:'待付款'},
          {name:'待发货'},
          {name:'待收货'},
          {name:'待评价'},
          {name:'售后/退款'},
        ],
        item:'件商品',// item(s)
        totalPay:'总金额',//total payment
        cancelOrder:'取消订单',
        viewDetails:'查看详情',
        evaluation:'去评价',//evaluation
        evaluated:'已评价',//evaluation
        delete:'删除订单',
        empty:'暂无订单信息',
        pay:'立即付款',
        complete:'完成'
      },
      userCoupon:{
        navTitle:'我的优惠券',
        available:'未使用',
        expired:'已使用/过期',
        min:'满减',
        can:'可用',
        used:'过期',
        general:'通用',
        goods:'商品',
        cate:'品类',
        receive:'领取',
        received:'已领取',
        receivedInfo:'领取后',
        receivedInfo1:'天内可用',
        navList:[
          {name:'商家'},
          {name:'商品'},
          {name:'品类'},
        ],
        empty:'暂无优惠券可用',
        valid:'有效期',
        day:'天',
        minus: '减',
        getUsers: '去使用'
      },
      userGoodsCollection:{
        navTitle:'我的收藏',
        item:'件商品',
        management:'管理',
        cancel:'取消',
        empty:'还没有收藏，赶快去关注一波吧~'
      },
      userAddressList:{
        navTitle:'地址管理',
        default:'设为默认',
        add:'添加地址',
        emptyAddress:'暂无地址信息',
        edit:'修改',
        del:'删除',
        sureDel: '确定删除该地址吗'
      },
      userAddress:{
        navTitle:'地址管理',
        name:'姓名',
        Email:'邮箱',
        country:'国家/地区',
        address:'详细地址',
        postCode:'邮政编码',
        phone:'电话号码',
        default:'设为默认',
        save:'保存',
        place:'请输入'
      },
      userReturnList:{
        navTitle:'申请退货',
        orderId:'退款单号',
        item:'件商品',
        total:'退款金额 ',
        refunding:'退款中',
        refunded:'已退款',
        examine: '商家审核中',
        examineInfo: '您已成功发起退款申请，请耐心等待商家处理；退款前请与商家协商一致，有助于更好的处理售后问题',
        examine1: '商家已退款',
        refuse: '拒绝退款',
        refuseReason: '拒绝原因：',
        refuseReason2: '退款已成功受理，如商家已寄出商品请尽快退回；感谢您的支持',
      },
      goodsCommentList:{
        navTitle:'商品评论',
        score:'评分',
        good:'好评率',
        all:'全部',
        goodScore:'好评',
        general:'中评',
        bad:'差评',
        attribute:'规格',
        seller:'店小二'
      },
      goodsCommentCon:{
        navTitle:'商品评论',
        score:[
          {name:'星级评分'},
          {name:'服务态度'}
        ],
        place:'商品满足你的期待么？说说你的想法，分享给想买的他们吧~' ,
        upload:'上传图片',
        submit:'立即评价',
        tips: '感谢您的评价',
        tipsWrite: '请填写你对宝贝的心得！',
        SU: '评价成功',
        tipsSU:'我们会继续为您提供更优质的商品及服务'

      },
      goodsReturn:{
        navTitle:'申请退款', //Application for return
        number:'退货数量',
        price:'退款金额',
        reason:'退款原因',
        info:'备注说明',
        voucher:'上传凭证',
        place:'填写备注信息，100字以内',
        submit:'申请退款' ,//Request a refund
        upload:'上传凭证',
        detal:'退款单详情',
        information: '退款信息',
        refundList: '退款列表'
      },
      login:{
        placeEmail:'请输入邮箱',
        placePasd:'请输入密码',
        sign:'登录',//SIGN IN
        create:'创建账户', //Create account
        forget:'忘记密码',
        quick:'快速登录',
        pasdLogin:'密码登录',
        with:'第三方登录',
        Pasdagain:'请确认密码',
        submit:'提交',
        next:'下一步',
        reset:'重置密码',
        resetDesc:"请输入您的电子邮件接收验证 ",
        emailVer:"电子邮件验证",
        verDesc:'为了您的帐户安全，请核实您的电子邮件：',
        tourists:'匿名购买',
        continue:'继续',
        phoneVer:'手机号码验证',
        remember: '想起密码了？'
      },
      register:{
        tabNav:[
          {name:'邮箱'},
          {name:'手机号码'}
        ],
        placeEmail:'请输入邮箱',
        placeCode:'请输入验证码',
        placePhone:'请输入手机号码',
        submit:'注册',
        have:'已有账户？',
        sign:'去登录'
      },
      logistics:{
        navTitle:'物流详情',
        expName:'物流公司',
        expNo:'快递单号',
      },
      userIntegral:{
        now:'当前积分',
        totalInt:'累计积分',
        totalPay:'累计消费',
        freeze:'冻结积分',
        tabNav:[
          {name:"分值明细"},
          {name:"分值提升"},
        ],
        shopAdd:'购买商品可获得积分奖励',
        make:'赚积分',
        empty:'积分暂无记录'
      },
      replyList: {
        navTitle: '评价列表'
      }
    },
    store: {
      search: '搜索店铺',
      followed: '已关注',
      follow: '关注',
      storeRating: '店铺评级',
      storeQualification: '店铺资质',
      storeIntroduction: '店铺简介',
      storeAddress: '店铺地址',
      storePhone: '联系电话',
      storeService: '联系客服',
      storeTime: '开店时间',
      storeName: '商户名称',
      userName: '用户姓名',
      phone: '联系电话',
      emil:'邮箱地址',
      code: '验证码',
      getCode: '获取验证码',
      class:'商户分类',
      type:'店铺类型',
      place1:'请上传营业执照及行业相关资质证明图片',
      place2:'图片最多可上传5张，格式支持JPG、PNG、JPEG',
      agree:'已阅读并同意',
      agreement:'入驻协议',
      submit:'提交申请',
      agreement2:'商户入驻协议',
      Tips1:'恭喜，您的资料提交成功！',
      Tips2:'预计15个工作日内审核完毕，平台客服会及时与您联系！',
      Tips3:'返回首页',
      title1:'商户入驻',
      title2:'来加入我们吧',
      street: '店铺街',
      keyword: '关键字',
      merInfo: '店铺详情',
      index: '首页',
      classify: '分类',
      collar: '领券',
      call: '联系客服',
      people: '人关注',
      delFollow: '取消关注',
      selfSupport: '自营',
      homeNav: [
        {name: '首页'},
        {name: '分类'},
        {name: '领券'}
      ]
    }
  },
  message:{
    login:{
      loginLoading:'正在登录中',
      getting:'获取中',
      emailEmpty:'请填写邮箱',
      correctEmail:'请填写正确的邮箱',
      emptyPassword:'请填写密码',
      againPassword:'请确认密码',
      diffPassword:'两遍密码不一致',
      emptyPhone:'请填写手机号码',
      emptyCaptche:'请填写验证码',
      correctCaptche:'请填写正确的验证码',
      loginSuccess:'登录成功',
      resetSuccess:'密码重置成功',
      name:'请填写收货人姓名',
      detail:'请填写详细地址',
      postCode:'请填写邮政编码',
      save:'正在保存中',
      updateSU:'修改成功',
      saveSU:'保存成功',
      prompt:'提示',
      confirmDel:'确定删除吗?',
      logout:'确定退出登录吗?',
      notExist:'您设置的默认地址不存在',
      setSU:'设置成功',
      delSU:'删除成功',
      calSU:'取消成功',
      send:'发送',
      resend:'重新发送',
      remaining:'剩余',
      operationSU: '操作成功',
      loginSure: '修改之后使用该密码保持登录吗？',
      loginFacebook:'当前用户未登录Facebook或者您的网页',
      registerSU:'注册成功',
      agreement: '请勾选用户隐私协议',
      agree: '我已阅读并同意',
      agreementName: '用户协议'
    },
    pay:{
      orderFail:'支付失败',
      error:'缺少参数无法查看订单支付状态',
      errorOrder: '缺少订单号无法查看'
    },
    orderConfirm:{
      payType:'请选择支付方式',
      emptyAddress:'请选择收货地址',
      payLoading:'订单支付中'
    },
    tips: {
      time: '提交时间',
      adopt:'审核通过',
      noadopt: '审核未通过',
      reason: '原因',
      stay: '待审核',
      edit: '编辑',
      see: '查看',
      again: '重新提交',
      shoppingSU: '添加购物车成功',
      loding: '加载中...',
      selectAddress: '选择地址',
      selectOther: '选择其他地址',
      surePay: '确定支付订单吗',
      sureDel: '确定删除该订单',
      upPic:'最多可上传5张',
      edidM: '点击修改密码',
      sold: '售出',
      changeGoods: '请选择商品',
      picTips: '上传图片只能是 JPG、PNG 格式',
      noEvaluation: '暂无评论~',
      more: '显示更多',
      hide: '隐藏更多',
      noDetal: '暂无商品详情',
      getSU: '领取成功',
      browser: '由于360浏览器功能限制，请按 Ctrl+D 手动收藏!',
      nubrowser: '您的浏览器不支持,请按 Ctrl+D 手动收藏!',
      error1: '抱歉，您访问的页面出错了',
      error2: '加载失败',
      error3:'错误的请求',
      error4:'请检查您的配置文件',
      placeLogin: '请登录'
    },
    settled: {
      emptyName:"请输入企业名称",
      emptyRealName:"请输入姓名",
      emptyCaptcha:"请填写验证码",
      emptyCategory:"请选择商户分类",
      emptyIsAgree:"请勾选并同意入驻协议",
      emptyIsPicture:"请上传资质图片",
      appleTips: '暂无申请记录，快去申请入驻吧'
    }
  }
}
