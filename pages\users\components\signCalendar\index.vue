<template>
	<!-- 打卡日历页面 -->
	<view class='all'>
		<view class="bar">
			<view class="icon">
				<image src="../../static/images/suo.png"></image>
			</view>
			<!-- 上一个月,只能往前翻一个月 -->
			<view class="previous" :class="Number(toMonth-nowMonth)<=0 ? 'newCol' : 'next'" @click="changeMonth(-1)">
				<text class="iconfont icon-xiangzuo" style="transform: rotate(180deg);"></text>
				<!-- <button class="barbtn">{{langType=='ch'?'上一月':'Last'}}</button> -->
			</view>
			<!-- 显示年月 toMonth-->
			<view class="date">{{nowYear || "--"}} 年 {{nowMonth || "--"}} 月</view>
			<!-- 下一个月, 往后翻的月份不能大于当前月份-->
			<view class="previous" :class="nowMonth >= toMonth ? 'next' : 'newCol'" @click="changeMonth(1)">
				<text class="iconfont icon-xiangyou"></text>
				<!-- <button class="barbtn">{{langType=='ch'?'下一月':'Nex/'}}</button> -->
			</view>
		</view>
		<!-- 显示星期 -->
		<view class="week-area">
			<view class="week-txt" v-for="(item,index) in weeksTxt[langType]" :key="index">{{item}}</view>
		</view>

		<view class="myDateTable">
			<view v-for="(item,j) in calendarDays" :key="j" class='dateCell'>
				<view v-if="item.date==undefined||item.date == null" class='cell'></view>
				<template v-else>
					<!-- 今日签到 @click="clickSign(item.date,1)"-->
					<view class="cell whiteColor todayChanged"
						v-if="item.isSign&&item.date==today&&nowMonth==toMonth&&nowYear==toYear">
						{{item.date}}
						<view class="redDot"></view>
					</view>
					<!-- 已签到日期 -->
					<view v-if="item.isSign == true" class='cell changed bgWhite'>
						{{item.date}}
					</view>
					<!-- 漏签 -->
					<!-- <view @click="clickSign(item.date,0)" class="cell outSignStyle"
						v-else-if="item.isBeforeToday&&item.isThisMonth">
						{{item.date}}
						<view class="redDot"></view>
					</view> -->
					
					<!-- 当前日期之后 -->
					<view class="whiteColor cell" v-else>
						{{item.date}}
					</view>
				</template>

			</view>
		</view>
		<view class="pic">
			<image src="../../static/images/fanye.png"></image>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	export default {
		data() {
			return {
				calendarDays: [],
				SignData: [], // 已经签到的数据
				nowYear: 0, //当前选的年
				nowMonth: 0, //当前选的月
				today: parseInt(new Date().getDate()), //系统本日
				toMonth: parseInt(new Date().getMonth() + 1), //系统本月
				toYear: parseInt(new Date().getFullYear()), //系统本年
				weeksTxt: {
					ch: ['日', '一', '二', '三', '四', '五', '六'],
					en: ['Sun', 'Mon', 'Tues', 'Wed', 'Thur', 'Fri', 'Sat'],
				},
			};
		},
		props: {
			isReplenishSign: { // 是否允许过期补签
				type: Boolean,
				default: false
			},
			isFullCalendar: { // 是否需要填满日历，前后空的格子填入上/下个月的日期
				type: Boolean,
				default: true
			},
			yearMonth: { // 2022-01 这种格式，默认当前年月
				type: String,
				default: new Date().getFullYear() + '-' + new Date().getMonth() + 1
			},
			dataSource: { //已签到的数据源，例： 5、6号已签到： ["2022-01-05","2022-01-06"]，兼容个位数前可加可不加0
				type: Array,
				default: () => {
					return []
				}
			},
			langType: { //只是示例一个翻译而已，要想所有都翻译自己可以再加加
				type: String,
				default: "ch" //en
			},
		},
		created() {
			if (!(/en|ch/g.test(this.langType))) {
				this.langType = 'ch'; // 非中英，则固定中文
			}
			const ymArr = this.yearMonth.split('-');
			this.buildCalendar(ymArr[0], ymArr[1]);
			this.onSignDataChange(this.dataSource);
		},
		watch: {
			dataSource: 'onSignDataChange',
		},
		methods: {
			clickSign(date, type) { //type=0补签，type=1当日签到	
				var strTip = "签到";

				if (type == 0) {
					if (!this.isReplenishSign) { // 未开启补签，阻止继续执行
						console.log("————补签功能未开启————");
						return;
					}
					strTip = "补签";
				}
				uni.showToast({
					title: date + "号" + strTip + "成功",
					icon: 'success',
					position: "bottom",
				});
				this.$emit('clickChange', this.nowYear + "-" + this.nowMonth + "-" + date); //传给调用模板页面
				// 父页面要在clickChange里去修改输入的数据源dataSource，否则视图不更新的!
			},
			//构建日历数据
			buildCalendar(y, m) {
				this.nowYear = y;
				this.nowMonth = m;
				this.calculateEmptyGrids(y, m);
				this.calculateDays(y, m);
				if (this.isFullCalendar) {
					this.fullCell()
				}
			},
			// 监听到签到数据源改变
			onSignDataChange(newData, oldData = []) {
				this.SignData = newData;
				this.matchSign();
			},
			//匹配标记已经签到的日子
			matchSign() {
				var signs = this.SignData;
				var daysArr = this.calendarDays;
				for (var i = 0; i < signs.length; i++) {
					var current = new Date(this.toIOSDate(signs[i])).getTime(); // ios只认/格式的日期
					for (var j = 0; j < daysArr.length; j++) {
						if (current == new Date(this.toIOSDate(daysArr[j].fullDate)).getTime()) {
							daysArr[j].isSign = true;
						}
					}
				}
				this.calendarDays = daysArr;
			},
			// 计算当月1号前空了几个格子，把它填充在calendarDays数组的前面
			calculateEmptyGrids(year, month) {
				//计算每个月时要清零
				this.calendarDays = [];
				const firstDayOfWeek = this.getFirstDayOfWeek(year, month);
				if (firstDayOfWeek > 0) {
					for (let i = 0; i < firstDayOfWeek; i++) {
						this.calendarDays.push({
							date: null, // 显示的日期
							fullDate: null, // 日期yyyy-mm-dd格式
							isBeforeToday: true, // 今日之前
							isSign: false, // 是否签到
							isThisMonth: false, // 是本月
						});
					}
				}
			},
			// 绘制当月天数占的格子，并把它放到days数组中
			calculateDays(year, month) {
				const thisMonthDays = this.getMonthDayLength(year, month);
				const toDate = new Date(this.toYear + '/' + this.toMonth + '/' + this.today);
				for (let i = 1; i <= thisMonthDays; i++) {
					const fullDate = year + '-' + month + '-' + i;
					const isBeforeToday = new Date(this.toIOSDate(fullDate)) < toDate;
					this.calendarDays.push({
						date: i,
						fullDate,
						isBeforeToday,
						isSign: false,
						isThisMonth: true,
					});
				}
			},
			// 切换控制年月，上一个月，下一个月
			changeMonth(type) {
				// 往后翻的月份不能大于当前月份,往前只能翻一个月
				if(type===1 & this.nowMonth >= this.toMonth) return;
				if(type===-1 & (this.toMonth - this.nowMonth)>0) return;
				const nowYear = parseInt(this.nowYear);
				const nowMonth = parseInt(this.nowMonth);
				const newObj = this.getOperateMonthDate(nowYear, nowMonth, type);
				this.buildCalendar(newObj.year, newObj.month); // 重新构建日历数据
				this.$emit('dateChange', this.nowYear + "-0" + this.nowMonth); //传给调用模板页面去拿新数据				
			},
			// 获取当月共多少天，也就是获取月的最后一天
			getMonthDayLength(year, month) {
				return new Date(year, month, 0).getDate()
			},
			// 获取当月第一天星期几
			getFirstDayOfWeek(year, month, day = 1) {
				return new Date(Date.UTC(year, month - 1, day)).getDay();
			},
			toIOSDate(strDate) { // iso不认识"-"拼接的日期，所以转/
				return strDate ? strDate.replace(/-/g, '/') : strDate;
			},
			// 需要填满格子，上/下个月的日期拉出来填满格子
			fullCell() {
				// const endDay = this.getMonthDayLength(this.nowYear, this.nowMonth);
				// const beforeEmptyLength = this.getFirstDayOfWeek(this.nowYear, this.nowMonth);
				// const afterEmptyLength = 6 - this.getFirstDayOfWeek(this.nowYear, this.nowMonth, endDay);


				// const last = this.getOperateMonthDate(this.nowYear, this.nowMonth, -1);
				// const lastMonthEndDay = this.getMonthDayLength(last.year, last.month);
				// for (let i = 0; i < beforeEmptyLength; i++) {
				// 	const date = lastMonthEndDay - beforeEmptyLength + i + 1;
				// 	this.calendarDays[i].date = date;
				// 	this.calendarDays[i].fullDate = last.year + "-" + last.month + "-" + date;
				// }
				// const next = this.getOperateMonthDate(this.nowYear, this.nowMonth, 1);
				// for (let i = 1; i <= afterEmptyLength; i++) {
				// 	this.calendarDays.push({
				// 		date: i, // 显示的日期
				// 		fullDate: next.year + "-" + next.month + "-" + i, // 日期yyyy-mm-dd格式
				// 		isBeforeToday: false, // 今日之前
				// 		isSign: false, // 是否签到
				// 		isThisMonth: false, // 是本月
				// 	});
				// }
			},
			// 获取加/减一个月的日期
			getOperateMonthDate(yy, mm, num) {
				let month = parseInt(mm) + parseInt(num);
				let year = parseInt(yy);
				if (month > 12) {
					month = 1;
					year++;
				} else if (month < 1) {
					month = 12;
					year--;
				}
				return {
					month,
					year,
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.next{
		background-color: #CCCCCC;
	}
	.newCol{
		@include main_bg_color(theme);
	}
	.previous{
		width: 40rpx;
		height: 40rpx;
		opacity: 1;
		border-radius: 50%;
		text-align: center;
		line-height: 40rpx;
		color: #fff;
	}
	.icon-xiangyou, .icon-xiangzuo{
		font-size: 20rpx;
	}
	.all {
		padding: 0 30rpx;
		position: relative;
		top: -84rpx;
		z-index: 1;

		.bar {
			width: 100%;
			height: 152rpx;
			border-radius: 24rpx 24rpx 0px 0px;
			opacity: 1;
			// background: url('') no-repeat;
			background-size: 100% 100%;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			background-color: #fff;
			align-items: center;
			padding: 0 43rpx;

			.icon {
				width: 518rpx;
				height: 48rpx;
				position: absolute;
				z-index: 2;
				top: -16rpx;
				left: 116rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.date {
				font-weight: 800;
				color: #333333;
				font-size: 40rpx;
			}

		}

		.pic {
			width: 240rpx;
			height: 240rpx;
			position: absolute;
			z-index: 2;
			right: 30rpx;
			bottom: 0;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.changed {
			width: 58rpx;
			height: 58rpx;
			@include main_rgba_color(theme);
			margin: auto;
		}
	}

	.bar .barbtn {
		height: 30px;
		line-height: 30px;
		font-size: 12px;
	}

	.all .week-area {
		display: flex;
		justify-content: space-between;
		box-sizing: border-box;
		width: 100%;
		background-color: #FFF;
		padding: 35rpx 0;
		color: #999;
	}

	.all .week-txt {
		text-align: center;
		width: 13vw;
	}

	.myDateTable {
		margin: 0 auto;
		width: 100%;
		background-color: #FFF;
		border-radius: 0 0 24rpx 24rpx;
		padding-bottom: 20rpx;
	}

	.myDateTable .dateCell {
		width: 13vw;
		height: 58rpx;
		line-height: 58rpx;
		display: inline-block;
		text-align: center;
		box-sizing: border-box;
		overflow: hidden;
		color: #333;
		font-weight: bold;
		font-size: 28rpx;
		margin-bottom: 32rpx;
	}

	.dateCell .cell {
		display: flex;
		border-radius: 50%;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
		flex-direction: column;
	}


	.whiteColor {}

	.greenColor {
		color: #01b90b;
		font-weight: bold;
	}

	.bgWhite {
		//background-color: #fff;
	}

	.bgGray {
		background-color: rgba(255, 255, 255, 0.42);
	}

	.todayChanged {
		width: 58rpx;
		height: 58rpx;
		@include main_bg_color(theme);
		margin: auto;
		text-align: center;
		color: #FFF;
	}

	.redColor {
		color: #ff0000;
	}

	.redDot {
		width: 3px;
		height: 3px;
		border-radius: 50%;
		background-color: #fff;
		margin-top: -12rpx;
	}
</style>
