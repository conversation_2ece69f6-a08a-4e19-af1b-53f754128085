<template>
	<view>
		<view class='payment-status'>
			<view class="payment-status-img">
				<image src="https://image.letengshengtai.com/ooseekimage/public/product/2025/08/01/c2f96b0d1003413cbd6e066b32c5d080gdfvaaeec8.png" mode="" v-if="iiii"></image>
				<image src="https://image.letengshengtai.com/ooseekimage/public/product/2025/08/01/360a7a8cd7f54b0a9af71dcf4032e767tybr3t9w34.png" mode="" v-else></image>
			</view>
			<view class="payment-status-text">
				{{statustext}}
			</view>
			
		</view>
		<view class="payment-status-butbox">
			<button formType="submit" class='returnBnt bg_color' hover-class='none' @click="goOrderDetails">查看订单</button>
			<button formType="submit" class='returnBnt cart_color' hover-class='none' @click="goIndex">继续逛逛</button>
		</view>
		<view class="payment-status-butbox" v-if="!order_pay_info.paid && order_pay_info.payType != 'offline'">
			<button formType="submit" class='returnBnt bg_color' hover-class='none' @click="goIndex">继续逛逛</button>
			<button formType="submit" class='returnBnt cart_color' hover-class='none' @click="chongxinzhifu">重新支付</button>
		</view>
	
	<view class="share-box">
		<!-- @click="goPink(order_pay_info.pinkId)" -->
<button open-type="share" class="custom-share-btn"><image src="/pages/aastatic/static/img/<EMAIL>" mode=""  ></image></button>

		
	</view>
		<!-- 推荐商品 -->
		<view class="recommend-box">
			<recommendpaystatus ></recommendpaystatus>
		</view>
	</view>
</template>

<script>
	import recommendpaystatus from "@/components/base/recommend_paystatus.vue";
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getOrderDetail,
		wechatQueryPayResult
	} from '@/api/order.js';
	import {
		openOrderSubscribe
	} from '@/utils/SubscribeMessage.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		components: {
		  
		  recommendpaystatus
		
		},
		data() {
			return {
				iiii: false,
				statustext:'支付成功',
				
				orderNo: '',
				order_pay_info: {
					paid: 0,
					_status: {}
				},
				status: 0,
				msg: '',
				errMsg: false,
				payResult: '订单查询中...',
				theme: app.globalData.theme,
			};
		},
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getOrderPayInfo();
					}
				},
				deep: true
			}
		},
		onLoad: function(options) {
			if (!options.order_id) return this.$util.Tips({
				title: '缺少参数无法查看订单支付状态'
			}, {
				tab: 3,
				url: 1
			});
			this.orderNo = options.order_id;
			this.status = options.status || 0;
			if (this.isLogin) {
				this.getOrderPayInfo();
			} else {
				toLogin();
			}
		},
		onShareAppMessage(res) {
				let sharespread = uni.getStorageSync('sharespread');
				let shareId = uni.getStorageSync('shareId');
				let shareMid = uni.getStorageSync('shareMid');
		    return {
		      title: uni.getStorageSync('shaTitle'),
		   path: `/pages/goods/goods_details/index?id=${shareId}&spread=${sharespread}&mid=${shareMid || ''}`,
		      imageUrl: uni.getStorageSync('shaIMG')
		    };
		  },
		

		methods: {
			chongxinzhifu(){
				// uni.navigateBack()
				let StoaorderNo = uni.getStorageSync('StoaorderNo');
				let StoapayPrice = uni.getStorageSync('StoapayPrice');
				uni.redirectTo({
					url: `/pages/goods/order_payment/index?orderNo=${StoaorderNo}&payPrice=${StoapayPrice}`
				})
			},
			wechatQueryPay() {
				wechatQueryPayResult(this.orderNo).then(res => {
						this.payResult = '支付成功';
						this.statustext = '支付成功';
						// uni.setNavigationBarTitle({
						// 	title: '支付成功'
						// });
						this.order_pay_info.paid = 1;
						uni.hideLoading();
					})
					.catch(err => {
						this.order_pay_info.paid = 2;
						this.errMsg = true;
						this.msg = err;
						uni.hideLoading();
						this.$util.Tips({
							title: err
						});
					});
			},
			onLoadFun: function() {
				this.getOrderPayInfo();
			},
			/**
			 * 
			 * 支付完成查询支付状态
			 * 
			 */
			getOrderPayInfo: function() {
				let that = this;
				uni.showLoading({
					title: '正在加载中'
				});
				getOrderDetail(that.orderNo).then(res => {
					that.$set(that, 'order_pay_info', res.data);
					if (res.data.payType === 'weixin' && res.data.paid === false && this.status != 2) {
						setTimeout(() => {
							that.wechatQueryPay();
						}, 2000);
					} else {
						// uni.setNavigationBarTitle({
						// 	title: res.data.paid ? '支付成功' : '未支付'
						// });
						if (res.data.paid) {
							this.payResult = '支付成功';
							this.statustext = '支付成功';
							this.order_pay_info.paid = 1;
						} else {
							this.payResult = '支付失败';
							this.statustext = '支付失败';
							this.order_pay_info.paid = 2;
						}
						uni.hideLoading();
					}

				}).catch(err => {
					uni.hideLoading();
				});
			},
			/**
			 * 去首页关闭当前所有页面
			 */
			goIndex: function(e) {
				uni.switchTab({
					url: '/pages/index/index'
				});
			},
			// 去参团页面；
			goPink: function(id) {
				uni.navigateTo({
					url: '/pages/activity/goods_combination_status/index?id=' + id
					
				});
			},
			/**
			 * 
			 * 去订单详情页面
			 */
			goOrderDetails: function(e) {
				let that = this;
				// #ifdef MP
				uni.showLoading({
					title: '正在加载',
				})
				openOrderSubscribe().then(res => {
					uni.hideLoading();
					uni.navigateTo({
						url: '/pages/goods/order_list/index'
					});
				}).catch(() => {
					uni.hideLoading();
				});
				// #endif
				// #ifndef MP
				uni.navigateTo({
					url: '/pages/goods/order_list/index'
				});
				// #endif
			}
		}
	}
</script>

<style lang="scss">
	.icon-iconfontguanbi {
		background-color: #999 !important;
		text-shadow: none !important;
	}

	.bg_color {
		@include main_bg_color(theme);
	}

	.cart_color {
		@include main_color(theme);
		@include coupons_border_color(theme);
	}

	.payment-status {
		// background-color: #fff;
		margin: 84rpx auto auto auto;
		border-radius: 10rpx;
		// padding: 1rpx 0 28rpx 0;
	}
	.payment-status-text{
		margin-top: 30rpx;
		text-align: center;
		font-size: 32rpx;
		
		color: #333333;
		
	}
	.payment-status-butbox{
		
	}
	
	

	.payment-status .icons {
		font-size: 70rpx;
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
		color: #fff;
		text-align: center;
		line-height: 140rpx;
		text-shadow: 0px 4px 0px rgba(0, 0, 0, .1);
		border: 6rpx solid #f5f5f5;
		margin: -76rpx auto 0 auto;
		background-color: #999;
	}

	.payment-status .iconfont {
		font-size: 70rpx;
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
		color: #fff;
		text-align: center;
		line-height: 140rpx;
		text-shadow: 0px 4px 0px rgba(0, 0, 0, .1);
		border: 6rpx solid #f5f5f5;
		margin: -76rpx auto 0 auto;
		background-color: #999;
	}

	.payment-status .iconfont.fail {
		text-shadow: 0px 4px 0px #7a7a7a;
	}

	.payment-status .status {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		margin: 25rpx 0 37rpx 0;
	}

	.payment-status .wrapper {
		border: 1rpx solid #eee;
		margin: 0 30rpx 47rpx 30rpx;
		padding: 35rpx 0;
		border-left: 0;
		border-right: 0;
	}

	.payment-status .wrapper .item {
		font-size: 28rpx;
		color: #282828;
	}

	.payment-status .wrapper .item~.item {
		margin-top: 20rpx;
	}

	.payment-status .wrapper .item .itemCom {
		color: #666;
	}

	.payment-status .returnBnt {
		// width: 630rpx;
		// height: 86rpx;
		// border-radius: 50rpx;
		// color: #fff;
		// font-size: 30rpx;
		// text-align: center;
		// line-height: 86rpx;
		// margin: 0 auto 20rpx auto;
		
	

	}
	.payment-status-butbox {
		margin-left: 40rpx;
		margin-right: 40rpx;
		margin-top: 40rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.returnBnt{
			width: 312rpx;
			height: 88rpx;
			border-radius: 16rpx;
			font-size: 30rpx;
			
			text-align: center;
	
			line-height: 88rpx;
		}
		.bg_color{
					border: 2rpx solid #999999;
							color: #999999;
		}
		.cart_color{
			background: #bdfd5b !important;
			color: #222222;
		}
	}

	.cart-color {
		@include main_color(theme);
		@include coupons_border_color(theme);
	}

	.payment-status-img {
		margin: 0 auto;
		margin-top: 84rpx;
		width: 300rpx;
		height: 300rpx;

		image {
			width: 300rpx;
			height: 300rpx;
		}
	}
	.share-box{
		margin-top: 18rpx;
		  padding: 0 24rpx;
		  image{
			 width: 100%;
			 height: 160rpx;
		  }
	}
	.recommend-box{
		margin-top: 18rpx;
		  padding: 0 24rpx;
	}
	.custom-share-btn{
		background-color: transparent;
	}
</style>