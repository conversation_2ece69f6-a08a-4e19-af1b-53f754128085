<template>
  <view class="shopping-cart">
    <!-- 公告模块 -->
    <view class="notice-module" v-if="noticeText">
      <view class="notice-content">
        <text class="iconfont icon-laba notice-icon"></text>
        <view class="notice-text">{{ noticeText }}</view>
      </view>
    </view>

    <!-- 优惠券模块 -->
    <view class="coupon-module" v-if="couponList.length > 0">
      <view class="coupon-content">
        <view class="coupon-left">
          <text class="coupon-label">优惠券</text>
          <view class="coupon-list">
            <view
              v-for="(coupon, index) in couponList.slice(0, 2)"
              :key="index"
              class="coupon-item"
              @click="receiveCoupon(coupon)"
            >
              {{ coupon.text }}
            </view>
          </view>
        </view>
        <view class="coupon-right" @click="goToCouponCenter">
          <text class="more-text">领券</text>
          <text class="iconfont icon-jiantou more-arrow"></text>
        </view>
      </view>
    </view>

    <!-- 购物车主体内容 -->
    <view class="cart-container">
      <!-- 正常商品列表 -->
      <view class="goods-list" v-if="cartList.valid.length > 0">
        <view 
          v-for="(store, storeIndex) in cartList.valid" 
          :key="storeIndex" 
          class="store-group"
        >
          <!-- 店铺标题 -->
          <view class="store-header">
            <view class="store-info">
              <view class="checkbox" @click="storeAllCheck(store, storeIndex)">
                <text v-if="!store.allCheck" class="iconfont icon-weixuan"></text>
                <text v-else class="iconfont icon-xuanzhong11"></text>
              </view>
              <navigator :url="'/pages/merchant/home/<USER>'+store.merId" class="store-name-wrapper">
                <text class="iconfont icon-shangjiadingdan"></text>
                <text class="store-name">{{ store.storeName }}</text>
                <view class="store-tags">
                  <view 
                    v-for="(type, typeIndex) in getStoreTypes(store.typeList)" 
                    :key="typeIndex"
                    class="store-tag"
                    :class="{
                      'tag-delivery': type === 1,
                      'tag-pickup': type === 2,
                      'tag-express': type === 3
                    }"
                  >
                    {{ getStoreTagText(type) }}
                  </view>
                </view>
              </navigator>
            </view>
          </view>

          <!-- 商品项 -->
          <view
            v-for="(item, itemIndex) in store.cartInfoList"
            :key="itemIndex"
            class="swipe-item-wrapper"
          >
            <view
              class="swipe-item"
              :style="{ transform: `translateX(${item.translateX || 0}rpx)` }"
              @touchstart="touchStart($event, storeIndex, itemIndex)"
              @touchmove="touchMove($event, storeIndex, itemIndex)"
              @touchend="touchEnd($event, storeIndex, itemIndex)"
            >
              <view class="goods-item">
                <!-- 商品主要信息区域 -->
                <view class="goods-main">
                  <!-- 选择框在左侧 -->
                  <view class="goods-checkbox">
                    <view class="checkbox" @click.stop="goodsCheck(item)">
                      <text v-if="!item.check" class="iconfont icon-weixuan"></text>
                      <text v-else class="iconfont icon-xuanzhong11"></text>
                    </view>
                  </view>

                  <!-- 商品内容区域 -->
                  <view class="goods-content-container">
                    <navigator :url='"/pages/goods/goods_details/index?id="+item.productId' hover-class='none' class="goods-content-wrapper">
                      <view class="goods-image">
                        <easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
                      </view>
                      <view class="goods-content">
                        <!-- 商品标题 -->
                        <view class="goods-title" :class="item.attrStatus?'':'reColor'">{{ item.proName }}</view>

                        <!-- 规格和价格 -->
                        <view class="goods-bottom-info">
                          <!-- 左侧：规格和价格 -->
                          <view class="goods-left-info">
                            <view class="goods-spec" v-if="item.sku">规格：{{ item.sku }}</view>
                            <view class="goods-price" v-if="item.attrStatus">
                              <text class="price-symbol">¥</text>
                              <text class="price-value" v-if="item.customData && item.customData.price > 0">
                                {{ item.vipPrice ? item.vipPrice : item.price | priceAdd(item.customData.price) }}
                              </text>
                              <text class="price-value" v-else>
                                {{ item.vipPrice ? item.vipPrice : item.price }}
                              </text>
                            </view>
                            <view class="reElection" v-else>
                              <view class="title">请重新选择商品规格</view>
                              <view class="reBnt" @click.stop="reElection(item)">重选</view>
                            </view>
                          </view>

                          <!-- 右侧：计数器 -->
                          <view class="quantity-control" v-if="item.attrStatus">
                            <view class="quantity-btn minus" :class="item.numAdd ? 'on' : ''" @click.stop='subCart(item)'>-</view>
                            <view class="quantity-num">{{ item.cartNum }}</view>
                            <view class="quantity-btn plus" :class="item.numAdd ? 'on' : ''" @click.stop='addCart(item)'>+</view>
                          </view>
                        </view>
                      </view>
                    </navigator>

                    <!-- 眼镜定制信息 -->
                    <glassesProductCustomInfo v-if="item.customData && (item.customData.lens || item.customData.optometry)" :customData="item.customData"></glassesProductCustomInfo>

                  </view>
                </view>
              </view>
            </view>

            <!-- 删除区域 -->
            <view class="swipe-delete-btn" @click="deleteItem(storeIndex, itemIndex)">
              <text class="swipe-delete-text">删除</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 失效商品 -->
      <view class="invalid-goods" v-if="cartList.invalid.length > 0">
        <view class="invalid-header">
          <text class="invalid-title">失效商品</text>
          <view class="clear-invalid" @click.stop='unsetCart'>
            <text>清空失效商品</text>
          </view>
        </view>
        <view class="invalid-list">
          <view
            v-for="(store, storeIndex) in cartList.invalid"
            :key="storeIndex"
          >
            <view
              v-for="(item, itemIndex) in store.cartInfoList"
              :key="itemIndex"
              class="invalid-item"
            >
              <view class="invalid-item-content">
                <!-- 左部分：商品图片 -->
                <view class="invalid-left">
                  <view class="invalid-goods-image">
                    <easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
                  </view>
                </view>

                <!-- 右部分：商品信息 -->
                <view class="invalid-right">
                  <!-- 上部分：商品标题 -->
                  <view class="invalid-title-section">
                    <view class="invalid-goods-title">{{ item.proName }}</view>
                  </view>

                  <!-- 下部分：状态和按钮 -->
                  <view class="invalid-bottom-section">
                    <!-- 左侧：失效状态 -->
                    <view class="invalid-status">
                      <text class="invalid-status-text">商品失效，暂不支持购买</text>
                    </view>

                    <!-- 右侧：找相似按钮 -->
                    <view class="find-similar-btn" @click.stop="findSimilar(item)">
                      <text class="find-similar-text">找相似</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 推荐商品 -->
      <recommend v-if="cartList.valid.length > 0 || cartList.invalid.length > 0"></recommend>

      <!-- 空购物车 -->
      <view v-if="cartList.valid.length === 0 && cartList.invalid.length === 0 && !loading && canShow" class="empty-cart">
        <view class="empty-image">
          <image src="/static/images/noShopper.png" mode="aspectFit"></image>
        </view>
        <text class="empty-text">暂无商品~</text>
        <recommend></recommend>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="cart-footer" v-if="cartList.valid.length > 0">
      <view class="footer-left">
        <view class="checkbox" @click.stop="checkboxAllChange">
          <text v-if="!isAllSelect" class="iconfont icon-weixuan"></text>
          <text v-else class="iconfont icon-xuanzhong11"></text>
        </view>
        <text class="select-all-text">全选 ({{ cartCount }})</text>
      </view>
      <view class="footer-right" v-if="footerswitch==true">
        <view class="total-info">
          <text class="total-text">合计：</text>
          <text class="total-price">¥{{ selectCountPrice }}</text>
        </view>
        <form @submit="subOrder" report-submit='true'>
          <button class="checkout-btn" formType="submit">立即下单</button>
        </form>
      </view>
      <view class="footer-right button-group" v-else>
        <form @submit="subCollect" report-submit='true'>
          <button class='collect-btn' formType="submit">收藏</button>
        </form>
        <form @submit="subDel" report-submit='true'>
          <button class='delete-btn' formType="submit">删除</button>
        </form>
      </view>
    </view>

    <!-- 产品属性选择弹窗 -->
    <productWindow :attr="attr" :isShow='1' :iSplus='1' :iScart='1' @myevent="onMyEvent" @ChangeAttr="ChangeAttr"
      @ChangeCartNum="ChangeCartNum" @attrVal="attrVal" @iptCartNum="iptCartNum" @goCat="reGoCat"
      id='product-window'></productWindow>
  </view>
</template>

<script>
import recommend from "@/components/base/recommend.vue";
import easyLoadimage from '@/components/base/easy-loadimage.vue';
import {
  getCartList,
  getCartCounts,
  changeCartNum,
  cartDel,
  getResetCart,
  getCartNoticeList,
  getCouponList,
  collectProduct,
  getProductExist
} from '@/api/order.js';
import {
  getProductHot,
  getProductDetail
} from '@/api/product.js';
import { mapGetters } from "vuex";
import productWindow from '@/components/productWindow';
import glassesProductCustomInfo from '@/components/glassesProductCustomInfo';
import { Debounce } from '@/utils/validate.js'
import { HTTP_REQUEST_URL } from '@/config/app';
import { toLogin } from '@/libs/login.js';

export default {
  name: 'ShoppingCart',
  components: {
    productWindow,
    glassesProductCustomInfo,
    recommend,
    easyLoadimage
  },
  data() {
    return {
      merPath: HTTP_REQUEST_URL,
      attribute: {
        cartId: 0,
        productId: 0,
        seckillId: 0,
        bargainId: 0,
        combinationId: 0,
      },
      isManage: false,
      cartCount: 0,
      footerswitch: true,
      cartList: {
        valid: [],
        invalid: []
      },
      // 公告数据
      noticeList: [],
      noticeText: '',
      noticePage: 1,
      noticeLimit: 10,
      noticeLoading: false,
      // 优惠券数据
      couponList: [],
      couponPage: 1,
      couponLimit: 10,
      couponLoading: false,
      isAllSelect: false, //全选
      selectValue: [], //选中的数据
      selectCountPrice: 0.00,
      loading: false,
      loadend: false,
      loadTitle: '加载更多',
      page: 1,
      limit: 20,
      loadingInvalid: false,
      loadendInvalid: false,
      loadTitleInvalid: '', //提示语
      limitInvalid: 20,
      attr: {
        cartAttr: false,
        productAttr: [],
        productSelect: {}
      },
      productValue: [], //系统属性
      productInfo: {},
      attrValue: '', //已选属性
      attrTxt: '请选择', //属性页面提示
      product_id: 0,
      canShow: false,
      // 滑动相关数据
      touchData: {
        startX: 0,
        startY: 0,
        currentX: 0,
        currentY: 0,
        isMoving: false,
        deleteWidth: 80 // 删除区域宽度，单位rpx
      }
    };
  },

  computed: {
    ...mapGetters(['isLogin', 'productType', 'globalData']),
    isAllSelect() {
      return this.cartList.valid.length && this.cartList.valid.every(store => 
        store.allCheck
      );
    },
    selectedCount() {
      let count = 0;
      this.cartList.valid.forEach(store => {
        if (store.valid === 1) {
          store.cartInfoList.forEach(item => {
            if (item.check) count += item.cartNum;
          });
        }
      });
      return count;
    },
    totalPrice() {
      let total = 0;
      this.cartList.valid.forEach(store => {
        if (store.valid === 1) {
          store.cartInfoList.forEach(item => {
            if (item.check) {
              total += parseFloat(item.vipPrice || item.price) * item.cartNum;
            }
          });
        }
      });
      return total.toFixed(2);
    }
  },
  
  onLoad: function() {
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '购物车'
    });
    if (!this.isLogin) {
      toLogin();
      return;
    }
    this.page = 1;
    this.cartList.valid = [];
    this.getCartList();
    this.loadendInvalid = false;
    this.cartList.invalid = [];
    this.getInvalidList();
    this.footerswitch = true;
    this.isAllSelect = false; //全选
    this.selectValue = []; //选中的数据
    this.selectCountPrice = 0.00;
    this.cartCount = 0;
    // 获取购物车公告列表
    this.getCartNoticeList();
    // 获取优惠券列表
    this.getCouponList();
    uni.showTabBar();
  },

  onShow: function() {
    this.canShow = false;
    if (this.isLogin == true) {
      this.page = 1;
      this.cartList.valid = [];
      this.getCartList();
      this.loadendInvalid = false;
      this.cartList.invalid = [];
      this.getInvalidList();
      this.footerswitch = true;
      this.isAllSelect = false; //全选
      this.selectValue = []; //选中的数据
      this.selectCountPrice = 0.00;
      this.cartCount = 0;
    }
    // 获取购物车公告列表
    this.getCartNoticeList();
    // 获取优惠券列表
    this.getCouponList();
  },
  
  // 滚动监听
  onPageScroll(e) {
    // 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
    uni.$emit('scroll');
  },
  
  methods: {
    // 获取购物车列表
    getCartList() {
      if (this.loading) return false;
      let data = {
        isValid: true
      }
      this.loading = true;
      this.loadTitle = ''
      getCartList(data).then(res => {
        let valid = res.data;
        valid.forEach((mer) => {
          mer.cartInfoList.forEach((product) => {
            let customData = product.customData;
            if (customData) {
              try {
                product.customData = JSON.parse(customData);
              } catch (e) {
                product.customData = customData;
              }
            }
          });
        });
        this.getCheckGoods(valid);
        this.$set(this.cartList, 'valid', valid);
        this.checkboxAllChange()
        this.loading = false;
        this.loadTitle = '我也是有底线的';
        if (this.cartList.valid) this.canShow = true;
        uni.hideLoading();
      });
    },
    
    // 获取公告内容
    getCartNoticeList: function() {
      let that = this;
      if (that.noticeLoading) return;

      that.noticeLoading = true;
      let data = {
        page: that.noticePage,
        limit: that.noticeLimit
      };

      getCartNoticeList(data).then(res => {
        that.noticeLoading = false;
        if (res.code === 200 && res.data && res.data.list) {
          let noticeList = res.data.list;
          if (noticeList.length > 0) {
            // 取第一条公告作为显示内容
            let firstNotice = noticeList[0];
            if (firstNotice.customData) {
              try {
                let customData = JSON.parse(firstNotice.customData);
                that.noticeText = customData.content || customData.title || '暂无公告内容';
              } catch (e) {
                that.noticeText = firstNotice.customData;
              }
            } else {
              that.noticeText = '暂无公告内容';
            }
            that.noticeList = noticeList;
          } else {
            that.noticeText = '';
            that.noticeList = [];
          }
        } else {
          that.noticeText = '';
          that.noticeList = [];
        }
      }).catch(err => {
        that.noticeLoading = false;
        console.error('获取公告列表失败:', err);
        that.noticeText = '';
        that.noticeList = [];
      });
    },
    
    // 获取优惠券列表
    getCouponList: function() {
      let that = this;
      if (that.couponLoading) return;

      that.couponLoading = true;
      let data = {
        category: 0, // 平台券
        publisher:1,
        merId: 0,
        page: that.couponPage,
        limit: that.couponLimit
      };

      getCouponList(data).then(res => {
        that.couponLoading = false;
        if (res.code === 200 && res.data && res.data.list) {
          let couponList = res.data.list;
          // 处理优惠券数据，格式化显示文本
          that.couponList = couponList.map(coupon => {
            let text = '';
            if (coupon.couponType === 1) {
              // 满减券
              if (coupon.minPrice > 0) {
                text = `满${coupon.minPrice}减${coupon.money}`;
              } else {
                text = `立减${coupon.money}元`;
              }
            } else if (coupon.couponType === 2) {
              // 折扣券
              if (coupon.minPrice > 0) {
                text = `满${coupon.minPrice}享${coupon.discount}折`;
              } else {
                text = `${coupon.discount}折券`;
              }
            } else {
              text = coupon.name || '优惠券';
            }

            return {
              ...coupon,
              text: text,
              type: `type${coupon.couponType}`,
              isReceived: coupon.isUse || false
            };
          });
        } else {
          that.couponList = [];
        }
      }).catch(err => {
        that.couponLoading = false;
        console.error('获取优惠券列表失败:', err);
        that.couponList = [];
      });
    },
    
    // 领取优惠券
    receiveCoupon: function(coupon) {
      let that = this;
      // 检查是否已领取
      if (coupon.isReceived) {
        that.$util.Tips({
          title: '已领取过该优惠券',
          icon: 'none'
        });
        return;
      }
      
      console.log('领取优惠券:', coupon);
      // 临时模拟领取成功
      that.$util.Tips({
        title: '领取成功',
        icon: 'success'
      });
      coupon.isReceived = true;
    },
    
    // 根据typeList获取配送类型数组
    getStoreTypes: function(typeList) {
      if (!typeList || !Array.isArray(typeList)) {
        return [];
      }
      return typeList;
    },
    
    // 根据配送类型获取标签文本
    getStoreTagText: function(type) {
      const tagMap = {
        1: '快递',
        2: '自提', 
        3: '配送'
      };
      return tagMap[type] || '';
    },
    
    // 跳转到优惠券中心
    goToCouponCenter: function() {
      console.log('跳转到优惠券中心');
      uni.navigateTo({
        url: '/pages/coupon/coupon_center/index?from=order'
      });
    },
    
    // 查看验光单
    viewOptometry: function(item) {
      console.log('查看验光单:', item);
      uni.navigateTo({
        url: '/pages/optometry/optometry_detail/index?id=' + (item.customData.optometry.id || item.id)
      });
    },
    
    // 触摸开始
    touchStart: function(e, storeIndex, itemIndex) {
      let touch = e.touches[0];
      this.touchData.startX = touch.clientX;
      this.touchData.startY = touch.clientY;
      this.touchData.isMoving = false;

      // 重置其他商品的滑动状态
      this.resetAllSwipeState();
    },

    // 触摸移动
    touchMove: function(e, storeIndex, itemIndex) {
      let touch = e.touches[0];
      let deltaX = this.touchData.startX - touch.clientX;
      let deltaY = Math.abs(this.touchData.startY - touch.clientY);

      // 如果垂直滑动距离大于水平滑动距离，则不处理
      if (deltaY > Math.abs(deltaX)) {
        return;
      }

      // 阻止页面滚动
      e.preventDefault();

      this.touchData.isMoving = true;

      // 只允许向左滑动，将像素转换为rpx（假设屏幕宽度为375px对应750rpx）
      if (deltaX > 0) {
        let translateX = -Math.min(deltaX * 2, this.touchData.deleteWidth);
        this.$set(this.cartList.valid[storeIndex].cartInfoList[itemIndex], 'translateX', translateX);
      } else {
        this.$set(this.cartList.valid[storeIndex].cartInfoList[itemIndex], 'translateX', 0);
      }
    },

    // 触摸结束
    touchEnd: function(e, storeIndex, itemIndex) {
      if (!this.touchData.isMoving) {
        return;
      }

      let item = this.cartList.valid[storeIndex].cartInfoList[itemIndex];
      let translateX = item.translateX || 0;

      // 如果滑动距离超过删除按钮宽度的一半，则显示删除按钮
      if (Math.abs(translateX) > this.touchData.deleteWidth / 2) {
        this.$set(item, 'translateX', -this.touchData.deleteWidth);
      } else {
        this.$set(item, 'translateX', 0);
      }

      this.touchData.isMoving = false;
    },

    // 重置所有滑动状态
    resetAllSwipeState: function() {
      this.cartList.valid.forEach(store => {
        store.cartInfoList.forEach(item => {
          this.$set(item, 'translateX', 0);
        });
      });
    },

    // 删除商品
    deleteItem: function(storeIndex, itemIndex) {
      let that = this;
      let item = this.cartList.valid[storeIndex].cartInfoList[itemIndex];

      uni.showModal({
        title: '提示',
        content: '确认删除该商品？',
        success: function(res) {
          if (res.confirm) {
            cartDel([item.id]).then(res => {
              that.$util.Tips({
                title: '删除成功'
              });
              that.loadend = false;
              that.cartList.valid = [];
              that.getCartList();
              that.getCartNum();
            }).catch(err => {
              that.$util.Tips({
                title: err || '删除失败'
              });
            });
          }
        }
      });
    },

    // 删除单个失效商品
    deleteInvalidItem: function(storeIndex, itemIndex) {
      let that = this;
      let item = this.cartList.invalid[storeIndex].cartInfoList[itemIndex];

      uni.showModal({
        title: '提示',
        content: '确认删除该失效商品？',
        success: function(res) {
          if (res.confirm) {
            cartDel([item.id]).then(res => {
              that.$util.Tips({
                title: '删除成功'
              });
              // 重新获取失效商品列表
              that.loadendInvalid = false;
              that.cartList.invalid = [];
              that.getInvalidList();
            }).catch(err => {
              that.$util.Tips({
                title: err || '删除失败'
              });
            });
          }
        }
      });
    },

    // 找相似商品
    findSimilar: function(item) {
      console.log('查找相似商品:', item);
      // 这里可以跳转到商品搜索页面或相似商品推荐页面
      uni.navigateTo({
        url: '/pages/goods/goods_search/index?keyword=' + encodeURIComponent(item.proName)
      });
    },

    // 修改购物车
    reGoCat: function() {
      let that = this,
        productSelect = that.productValue[this.attrValue];
      //如果有属性,没有选择,提示用户选择
      if (
        that.attr.productAttr.length &&
        productSelect === undefined
      )
        return that.$util.Tips({
          title: "产品库存不足，请选择其它"
        });

      let q = {
        id: that.attribute.cartId,
        productId: that.attribute.productId,
        num: that.attr.productSelect.cart_num,
        unique: that.attr.productSelect !== undefined ?
          that.attr.productSelect.unique : that.productInfo.id
      };
      getResetCart(q)
        .then(function(res) {
          that.attr.cartAttr = false;
          that.$util.Tips({
            title: "添加购物车成功",
            success: () => {
              that.loadend = false;
              that.page = 1;
              that.cartList.valid = [];
              that.getCartList();
              that.getCartNum();
            }
          });
        })
        .catch(res => {
          return that.$util.Tips({
            title: res
          });
        });
    },
    
    onMyEvent: function() {
      this.$set(this.attr, 'cartAttr', false);
    },
    
    reElection: function(item) {
      this.getGoodsDetails(item)
    },
    
    /**
     * 获取产品详情
     * 
     */
    getGoodsDetails: function(item) {
      uni.showLoading({
        title: '加载中',
        mask: true
      });
      let that = this;
      that.attribute.cartId = item.id;
      that.attribute.productId = item.productId;
      getProductDetail(item.productId, this.productType).then(res => {
        uni.hideLoading();
        that.attr.cartAttr = true;
        let productInfo = res.data.productInfo;
        that.$set(that, 'productInfo', productInfo);
        that.$set(that, 'productValue', res.data.productValue);
        let productAttr = res.data.productAttr.map(item => {
          return {
            attrName: item.attrName,
            attrValues: item.attrValues.split(','),
            id: item.id,
            isDel: item.isDel,
            productId: item.productId,
            type: item.type
          }
        });
        this.$set(that.attr, 'productAttr', productAttr);
        that.DefaultSelect();
      }).catch(err => {
        uni.hideLoading();
      })
    },
    
    /**
     * 属性变动赋值
     * 
     */
    ChangeAttr: function(res) {
      let productSelect = this.productValue[res];
      if (productSelect && productSelect.stock > 0) {
        this.$set(this.attr.productSelect, "image", productSelect.image);
        this.$set(this.attr.productSelect, "price", productSelect.price);
        this.$set(this.attr.productSelect, "stock", productSelect.stock);
        this.$set(this.attr.productSelect, "unique", productSelect.id);
        this.$set(this.attr.productSelect, "cart_num", 1);
        this.$set(this, "attrValue", res);
        this.$set(this, "attrTxt", "已选择");
      } else {
        this.$set(this.attr.productSelect, "image", this.productInfo.image);
        this.$set(this.attr.productSelect, "price", this.productInfo.price);
        this.$set(this.attr.productSelect, "stock", 0);
        this.$set(this.attr.productSelect, "unique", this.productInfo.id);
        this.$set(this.attr.productSelect, "cart_num", 0);
        this.$set(this, "attrValue", "");
        this.$set(this, "attrTxt", "请选择");
      }
    },
    
    /**
     * 默认选中属性
     * 
     */
    DefaultSelect: function() {
      let productAttr = this.attr.productAttr;
      let value = [];
      for (let key in this.productValue) {
        if (this.productValue[key].stock > 0) {
          value = this.attr.productAttr.length ? key.split(",") : [];
          break;
        }
      }
      for (let i = 0; i < productAttr.length; i++) {
        this.$set(productAttr[i], "index", value[i]);
      }
      //sort();排序函数:数字-英文-汉字；
      let productSelect = this.productValue[value.sort().join(",")];
      if (productSelect && productAttr.length) {
        this.$set(
          this.attr.productSelect,
          "storeName",
          this.productInfo.storeName
        );
        this.$set(this.attr.productSelect, "image", productSelect.image);
        this.$set(this.attr.productSelect, "price", productSelect.price);
        this.$set(this.attr.productSelect, "stock", productSelect.stock);
        this.$set(this.attr.productSelect, "unique", productSelect.id);
        this.$set(this.attr.productSelect, "cart_num", 1);
        this.$set(this, "attrValue", value.sort().join(","));
        this.$set(this, "attrTxt", "已选择");
      } else if (!productSelect && productAttr.length) {
        this.$set(
          this.attr.productSelect,
          "storeName",
          this.productInfo.storeName
        );
        this.$set(this.attr.productSelect, "image", this.productInfo.image);
        this.$set(this.attr.productSelect, "price", this.productInfo.price);
        this.$set(this.attr.productSelect, "stock", 0);
        this.$set(this.attr.productSelect, "unique", this.productInfo.id);
        this.$set(this.attr.productSelect, "cart_num", 0);
        this.$set(this, "attrValue", "");
        this.$set(this, "attrTxt", "请选择");
      } else if (!productSelect && !productAttr.length) {
        this.$set(
          this.attr.productSelect,
          "storeName",
          this.productInfo.storeName
        );
        this.$set(this.attr.productSelect, "image", this.productInfo.image);
        this.$set(this.attr.productSelect, "price", this.productInfo.price);
        this.$set(this.attr.productSelect, "stock", this.productInfo.stock);
        this.$set(
          this.attr.productSelect,
          "unique",
          this.productInfo.id || ""
        );
        this.$set(this.attr.productSelect, "cart_num", 1);
        this.$set(this, "attrValue", "");
        this.$set(this, "attrTxt", "请选择");
      }
    },
    
    attrVal(val) {
      this.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val
        .indexn]);
    },
    
    /**
     * 购物车数量加和数量减
     * 
     */
    ChangeCartNum: function(changeValue) {
      //changeValue:是否 加|减
      //获取当前变动属性
      let productSelect = this.productValue[this.attrValue];
      //如果没有属性,赋值给商品默认库存
      if (productSelect === undefined && !this.attr.productAttr.length)
        productSelect = this.attr.productSelect;
      //无属性值即库存为0；不存在加减；
      if (productSelect === undefined) return;
      let stock = productSelect.stock || 0;
      let num = this.attr.productSelect;
      if (changeValue) {
        num.cart_num++;
        if (num.cart_num > stock) {
          this.$set(this.attr.productSelect, "cart_num", stock ? stock : 1);
          this.$set(this, "cart_num", stock ? stock : 1);
        }
      } else {
        num.cart_num--;
        if (num.cart_num < 1) {
          this.$set(this.attr.productSelect, "cart_num", 1);
          this.$set(this, "cart_num", 1);
        }
      }
    },
    
    /**
     * 购物车手动填写
     * 
     */
    iptCartNum: function(e) {
      this.$set(this.attr.productSelect, 'cart_num', e);
    },
    
    subDel: Debounce(function(event) {
      let selectValue = []
      this.cartList.valid.forEach(el => {
        el.cartInfoList.forEach(goods => {
          if (goods.check) {
            selectValue.push(goods.id)
          }
        })
      })
      if (selectValue.length > 0)
        cartDel(selectValue).then(res => {
          this.loadend = false;
          this.cartList.valid = [];
          this.getCartList();
          this.getCartNum();
        });
      else
        return this.$util.Tips({
          title: '请选择产品'
        });
    }),
    
    subCollect: function(event) {
      let that = this;
      let type_id = []
      this.cartList.valid.forEach(el => {
        el.cartInfoList.forEach(goods => {
          if (goods.check) {
            type_id.push(goods.id)
          }
        })
      })
      if (type_id.length > 0) {
        collectProduct(type_id[0], 0).then(res => {
          that.$util.Tips({
            title: '收藏成功',
            icon: 'success'
          });
          this.cartList.valid = [];
          this.getCartList();
        }).catch(err => {
          return that.$util.Tips({
            title: err
          });
        });
      } else {
        return that.$util.Tips({
          title: '请选择产品'
        });
      }
    },
    
    // 立即下单
    subOrder: Debounce(function(event) {
      uni.showLoading({
        title: '加载中...'
      });
      this.selectValue = [];
      this.cartList.valid.forEach(el => {
        el.cartInfoList.forEach(goods => {
          if (goods.check) {
            this.selectValue.push(goods.id)
          }
        })
      })
      if (this.selectValue.length > 0) {
        this.getPreOrder();
      } else {
        uni.hideLoading();
        return this.$util.Tips({
          title: '请选择产品'
        });
      }
    }),
    
    /**
     * 预下单
     */
    getPreOrder: function() {
      let shoppingCartId = this.selectValue.map(item => {
        return {
          "shoppingCartId": Number(item)
        }
      })
      uni.hideLoading();
      this.$Order.getPreOrder("shoppingCart", shoppingCartId);
    },
    
    inArray: function(search, array) {
      for (let i in array) {
        if (array[i] == search) {
          return true;
        }
      }
      return false;
    },
    
    checkboxAllChange() {
      this.isAllSelect = !this.isAllSelect
      this.cartAllCheck('cartCheck')
    },
    
    getCartNum: function() {
      let that = this;
      getCartCounts(true, 'sum').then(res => {
        that.cartCount = res.data.count;
      });
    },
    
    // 商铺全选
    storeAllCheck(item, index) {
      // 店铺取消
      if (item.allCheck) {
        item.allCheck = false
        item.cartInfoList.forEach((el, index) => {
          el.check = false
        })
      } else {
        item.allCheck = true
        item.cartInfoList.forEach((el, index) => {
          if (!this.footerswitch) {
            el.check = true
          } else {
            if (parseFloat(el.stock) > 0) el.check = true
          }
        })
      }
      this.cartAllCheck('goodsCheck');
    },
    
    // 商品选中
    goodsCheck(goods) {
      if (!this.footerswitch) {
        goods.check = !goods.check
        this.cartAllCheck('goodsCheck')
      } else {
        if (parseFloat(goods.stock) > 0) {
          goods.check = !goods.check
          this.cartAllCheck('goodsCheck')
        }
      }
    },
    
    // 全选判断
    cartAllCheck(type) {
      let allArr = [];
      let totalMoney = 0
      let totalNum = 0
      this.cartList.valid.forEach((el, index) => {
        if (type == 'goodsCheck') {
          if (this.footerswitch) {
            let tempStock = el.cartInfoList.filter(goods => {
              return goods.stock > 0
            })
            let tempArr = el.cartInfoList.filter(goods => {
              return goods.check == true
            })
            if (tempStock.length == tempArr.length) {
              el.allCheck = true
              allArr.push(el)
            } else {
              el.allCheck = false
            }
          } else {
            let tempArr = el.cartInfoList.filter(goods => {
              return goods.check == true
            })
            if (el.cartInfoList.length == tempArr.length) {
              el.allCheck = true
              allArr.push(el)
            } else {
              el.allCheck = false
            }
          }

        } else {
          el.cartInfoList.forEach((goods) => {
            if (this.footerswitch) {
              goods.check = this.isAllSelect && parseFloat(goods.stock) > 0
            } else {
              goods.check = this.isAllSelect
            }
          })
          el.allCheck = this.isAllSelect
          if (el.allCheck) allArr.push(el)
        }
        // 总金额 //总数
        el.cartInfoList.forEach(e => {
          if (e.check && e.stock > 0 && this.footerswitch) {
            if (e.customData && e.customData.price > 0) {
              totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(this.$util.$h.Add(e.price, e.customData.price), e
                .cartNum))
            } else {
              totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(e.price, e
                .cartNum))
            }
            totalNum += e.cartNum

          } else if (e.check && !this.footerswitch) {
            if (e.customData && e.customData.price > 0) {
              totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(this.$util.$h.Add(e.price, e.customData.price), e
                .cartNum))
            } else {
              totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(e.price, e
                .cartNum))
            }
            totalNum += e.cartNum
          }
        })
      })
      this.cartCount = totalNum
      this.selectCountPrice = totalMoney
      // 全选
      this.isAllSelect = allArr.length == this.cartList.valid.length ? true : false
    },
    
    // 判断商品的初始状态是全部选中的
    getCheckGoods(valid) {
      let totalNum = 0;
      valid.forEach((item, index) => {
        item.allCheck = true
        item.cartInfoList.forEach((goods, j) => {
          if (this.footerswitch && this.isAllSelect) {
            goods.check = true
            totalNum += goods.cartNum
          } else {
            if (parseFloat(goods.stock) === 0) {
              goods.check = false
            } else {
              goods.check = true
              totalNum += goods.cartNum
            }
          }

          // 设置加号状态（当数量等于库存时禁用）
          if (goods.cartNum >= goods.stock) {
            goods.numAdd = true;
          } else {
            goods.numAdd = false;
          }
          // 减号始终可用，逻辑在subCart中处理
        })
      })
      this.cartCount = totalNum;
    },
    
    getInvalidList: function() {
      let that = this;
      if (this.loadingInvalid) return false;
      let data = {
        isValid: false
      }
      this.loadingInvalid = true;
      getCartList(data).then(res => {
        let invalidList = res.data;
        that.$set(that.cartList, 'invalid', invalidList);
        that.loadingInvalid = false;
      }).catch(res => {
        that.loadingInvalid = false;
      })
    },
    

    
    unsetCart: function() {
      let that = this,
        ids = [];
      this.cartList.invalid.forEach((el, index) => {
        el.cartInfoList.forEach(e => {
          ids.push(e.id);
        })
      })
      cartDel(ids).then(res => {
        that.$util.Tips({
          title: '清除成功'
        });
        that.$set(that.cartList, 'invalid', []);
      }).catch(res => {

      });
    },
    
    subCart: Debounce(function(item) {
      let that = this;
      // 确保数量不会小于1
      if (item.cartNum <= 1) {
        return; // 数量为1时不再减少
      }

      item.cartNum = Number(item.cartNum) - 1;
      that.setCartNum(item.id, item.cartNum, function(data) {
        item.numSub = false;
        item.numAdd = false;
        if (item.cartNum >= item.stock) {
          item.numAdd = true;
        }
        that.cartAllCheck('goodsCheck')
      });
    }),
    
    addCart: Debounce(function(item) {
      let that = this;
      item.cartNum = Number(item.cartNum) + 1;
      if (item.cartNum < item.stock) {
        item.numAdd = false;
        item.numSub = false;
        that.setCartNum(item.id, item.cartNum, function(data) {
          that.cartAllCheck('goodsCheck')
        })
      } else if (item.cartNum === item.stock) {
        item.numAdd = true;
        item.numSub = false;
        that.setCartNum(item.id, item.cartNum, function(data) {
          that.cartAllCheck('goodsCheck')
        })
      } else {
        item.cartNum = item.stock;
        item.numAdd = true;
        item.numSub = false;
      }
    }),
    
    setCartNum(cartId, cartNum, successCallback) {
      let that = this;
      changeCartNum(cartId, cartNum).then(res => {
        successCallback && successCallback(res.data);
      }).catch(err => {
        return that.$util.Tips({
          title: err
        });
      });
    }
  },
  
  onReachBottom() {
    let that = this;
    // 如果有效商品还有更多数据，优先加载有效商品
    if (!that.loadend) {
      that.getCartList();
    }
    // 如果失效商品还有更多数据，加载失效商品
    else if (!that.loadendInvalid) {
      that.getInvalidList();
    }
  }
}
</script>

<style lang="scss" scoped>
.shopping-cart {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 140rpx;
}

/* 公告模块样式 */
.notice-module {
  margin: 20rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.notice-content {
  padding: 10rpx 12rpx;
  border: 2rpx solid #FF2222;
  border-radius: 12rpx;
  background: #fff;
  display: flex;
  align-items: center;
}

.notice-icon {
  font-size: 28rpx;
  color: #FF2222;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.notice-text {
  font-size: 22rpx;
  color: #FF2222;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 优惠券模块样式 */
.coupon-module {
  margin: 0 24rpx 20rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.coupon-content {
  padding: 16rpx;
  background: #fff;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.coupon-label {
  font-size: 26rpx;
  color: #666666;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.coupon-list {
  flex: 1;
  display: flex;
  align-items: center;
  margin: 0;
}

.coupon-item {
  padding: 0rpx 16rpx;
  margin-right: 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #ffffff;
  background-image: url('/static/images/coupon_car.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  white-space: nowrap;
  min-height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coupon-item:last-child {
  margin-right: 0;
}

.coupon-item.received {
  background-image: url('/static/images/coupon_car.png');
  color: #cccccc;
  opacity: 0.6;
}

.coupon-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 25rpx;
  color: #FF5400;
  margin-right: 8rpx;
  line-height: 1;
}

.more-arrow {
  font-size: 20rpx;
  color: #FF5400;
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 购物车容器 */
.cart-container {
  padding: 0 24rpx;
}

/* 店铺组 */
.store-group {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  overflow: hidden;
}

.store-header {
  padding: 0 30rpx;
  height: 85rpx;
  border-bottom: 1px solid #f0f0f0;
}

.store-info {
  display: flex;
  align-items: center;
  height: 100%;
}

.store-name-wrapper {
  display: flex;
  align-items: center;
  margin-left: 28rpx;
  text-decoration: none;
}

.store-name {
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.store-tags {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}

.store-tag {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  margin-left: 8rpx;
}

/* 配送标签样式 */
.store-tag.tag-delivery {
  background-color: #FF5400;
  color: #ffffff;
}

/* 自提标签样式 */
.store-tag.tag-pickup {
  background-color: #BDFD5B;
  color: #222222;
}

/* 快递标签样式 */
.store-tag.tag-express {
  background-color: #4DA6FA;
  color: #ffffff;
}

/* 滑动容器 */
.swipe-item-wrapper {
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.swipe-item-wrapper:last-child {
  border-bottom: none;
}

.swipe-item {
  position: relative;
  z-index: 2;
  background-color: #fff;
  transition: transform 0.3s ease;
}

/* 删除区域 */
.swipe-delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  height: 100%;
  background-color: #FF2222;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.swipe-delete-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}

/* 商品项 */
.goods-item {
  display: flex;
  flex-direction: column;
  padding: 24rpx 30rpx;
  background-color: #fff;
}

/* 商品主要信息区域 */
.goods-main {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
}

.goods-checkbox {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.goods-content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.goods-content-wrapper {
  display: flex;
  flex: 1;
  text-decoration: none;
  margin-bottom: 16rpx;
}

.goods-image {
  width: 160rpx;
  height: 160rpx;
  flex-shrink: 0;
}

.goods-image image {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}

.goods-content {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-title {
  font-size: 28rpx;
  color: #282828;
  line-height: 1.4;
  margin-bottom: 10rpx;
}

.goods-title.reColor {
  color: #999;
}

.goods-bottom-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: auto;
}

.goods-left-info {
  flex: 1;
}

.goods-spec {
  font-size: 24rpx;
  color: #666666;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-bottom: 8rpx;
  display: inline-block;
}

.goods-price {
  display: flex;
  align-items: baseline;
  font-size: 32rpx;
  font-weight: 600;
  color: #FF2222;
}

.price-symbol {
  font-size: 24rpx;
  margin-right: 4rpx;
}

.price-value {
  font-size: 32rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

/* 重选按钮 */
.reElection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.reElection .title {
  font-size: 24rpx;
  color: #666;
}

.reElection .reBnt {
  width: 120rpx;
  height: 46rpx;
  border-radius: 23rpx;
  font-size: 26rpx;
  border: 1px solid #BDFD5B;
  color: #BDFD5B;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  height: 47rpx;
  flex-shrink: 0;
}

.quantity-btn {
  border: 1rpx solid #a4a4a4;
  width: 66rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #a4a4a4;
}

.quantity-btn.minus {
  border-right: 0;
  border-radius: 22rpx 0rpx 0rpx 22rpx;
  font-size: 34rpx;
}

.quantity-btn.plus {
  border-left: 0;
  border-radius: 0rpx 22rpx 22rpx 0rpx;
  font-size: 34rpx;
}

.quantity-btn.on {
  border-color: #e3e3e3;
  color: #dedede;
}

.quantity-num {
  border: 1rpx solid #a4a4a4;
  border-left: 0;
  border-right: 0;
  width: 66rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #282828;
}

/* 商品描述 */
.goods-desc-wrapper {
  margin-top: 16rpx;
  width: 100%;
}

.goods-desc {
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 验光单信息 */
.optometry-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
  padding: 12rpx 16rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.optometry-text {
  font-size: 26rpx;
  color: #333;
}

.optometry-arrow {
  font-size: 20rpx;
  color: #666;
}

/* 失效商品 */
.invalid-goods {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.invalid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
}

.invalid-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.clear-invalid {
  font-size: 26rpx;
  color: #999;
  padding: 8rpx 16rpx;
}

.invalid-list {
  padding: 0;
}

.invalid-item {
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
}

.invalid-item:last-child {
  border-bottom: none;
  margin-bottom: 20rpx;
}

.invalid-item-content {
  padding: 24rpx 30rpx;
  display: flex;
  align-items: flex-start;
  background: #fff;
}

/* 左部分：商品图片 */
.invalid-left {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.invalid-goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 6rpx;
  overflow: hidden;
}

.invalid-goods-image image {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}

/* 右部分：商品信息 */
.invalid-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 120rpx;
}

/* 上部分：商品标题 */
.invalid-title-section {
  margin-bottom: 16rpx;
}

.invalid-goods-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 下部分：状态和按钮 */
.invalid-bottom-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

/* 左侧：失效状态 */
.invalid-status {
  flex: 1;
  margin-right: 16rpx;
}

.invalid-status-text {
  font-size: 24rpx;
  color: #666666;
  background-color: #F5F5F5;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  display: inline-block;
}

/* 右侧：找相似按钮 */
.find-similar-btn {
  background-color: #BDFD5B;
  color: #222222;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  flex-shrink: 0;
}

.find-similar-text {
  color: #222222;
  font-size: 24rpx;
}

/* 空购物车 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  background: #fff;
  border-radius: 16rpx;
}

.empty-image {
  width: 414rpx;
  height: 305rpx;
  margin-bottom: 30rpx;
}

.empty-image image {
  width: 100%;
  height: 100%;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.go-shopping {
  padding: 15rpx 40rpx;
  background: #BDFD5B;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #333;
}

/* 底部操作栏 */
.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1rpx solid #eee;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  z-index: 999999;
  // padding-bottom: env(safe-area-inset-bottom);
}

.footer-left {
  display: flex;
  align-items: center;
}

.select-all-text {
  font-size: 28rpx;
  color: #282828;
  margin-left: 14rpx;
}

.footer-right {
  display: flex;
  align-items: center;
}

.footer-right.button-group {
  gap: 17rpx;
}

.total-info {
  margin-right: 22rpx;
}

.total-text {
  font-size: 28rpx;
  color: #333;
}

.total-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF2222;
}

.checkout-btn {
  background: #BDFD5B;
  color: #333;
  font-size: 30rpx;
  width: 226rpx;
  height: 70rpx;
  border-radius: 50rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collect-btn {
  font-size: 28rpx;
  border-radius: 50rpx;
  width: 160rpx;
  height: 60rpx;
  border: 1px solid #BDFD5B;
  color: #BDFD5B;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  font-size: 28rpx;
  color: #999;
  border-radius: 50rpx;
  border: 1px solid #999;
  width: 160rpx;
  height: 60rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 选择框样式 */
.checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
}

.checkbox .iconfont {
  font-size: 36rpx;
}

.checkbox .icon-weixuan {
  color: #ccc;
  background: transparent;
}

.checkbox .icon-xuanzhong11 {
  color: #000000;
  background: #BDFD5B;
  border-radius: 50%;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
}

.checkbox .icon-xuanzhong11:before {
  content: "\e64e" !important;
}

/* 购物车页面中 glassesProductCustomInfo 组件的 lens 样式定制 */
/deep/ .glasses  {
  width: 100%!important;
}
/deep/ .glasses .lens {
  background-color: #f5f5f5;
  color: #999999!important;
  width: 100%;
  border-radius: 12rpx!important;
  padding: 8rpx 12rpx!important;
  text-align: start;
  font-size: 24rpx!important;
  margin-bottom: 20rpx!important;
}

/* 覆盖默认样式 */
button::after {
  border: none;
}

form {
  margin: 0;
  padding: 0;
}
</style>
