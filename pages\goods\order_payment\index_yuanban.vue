<template>
	<view :data-theme="theme">
		<view class='wrapper'>
			<view class='item borRadius14'>
				<view class="title">￥<text>{{payPrice}}</text></view>
				<view class='list'>
					<block v-for="(item,index) in cartArr" :key='index'>
						<view v-if="item.payStatus === 1" class='payItem acea-row row-middle'
							:class='active==index ?"on":""' @tap='payItem(index)'>
							<view class='name acea-row row-center-wrapper'>
								<view class='iconfont animated'
									:class='(item.icon) + " " + (animated==true&&active==index ?"bounceIn":"")'>
								</view>
								{{item.name}}
							</view>
							<view class='tip'>
								{{item.title}}
								<block v-if="item.value === 'yue'">
									{{item.userBalance}}
								</block>
							</view>
							<view class="radio">
								<block v-if="active==index">
									<view class="iconfont icon-xuanzhong1 font-color"></view>
								</block>
								<block v-else>
									<view class="iconfont icon-weixuanzhong"></view>
								</block>
							</view>
						</view>
					</block>

				</view>
			</view>
		</view>
		<view class="btn-box">
			<view class='Bnt bg-color' @click='subscribeMessage'>立即支付</view>
		</view>

	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		orderPay,
		wechatQueryPayResult,
		getPayConfig
	} from '@/api/order.js';
	import {
		openOrderSubscribe
	} from '@/utils/SubscribeMessage.js';
	import {
		Debounce
	} from '@/utils/validate.js'
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	let cartArr = [{
			name: "微信支付",
			icon: "icon-weixinzhifu1",
			value: 'weixin',
			title: '微信快捷支付',
			payStatus: 1,
		},
		{
			name: "余额支付",
			icon: "icon-yuezhifu",
			value: 'yue',
			title: '可用余额:',
			payStatus: 1,
			userBalance: ''
		},
		// #ifndef MP
		{
			name: "支付宝支付",
			icon: "icon-zhifubao",
			value: 'alipay',
			title: '支付宝快捷支付',
			payStatus: 1,
		}
		// #endif
	];
	export default {
		data() {
			return {
				active: 0, //支付方式切换
				theme: app.globalData.theme,
				//支付方式
				cartArr: [],
				payPrice: '',
				orderNo: '',
				animated: false,
				payType: 'weixin', //支付方式
				payChannel: '',
				subscribing: false
			}
		},
		computed: {
			...mapGetters(['productType', 'systemPlatform'])
		},
		onLoad(options) {
			this.payPrice = options.payPrice;
			this.orderNo = options.orderNo;
			this.payConfig();
		},
		methods: {
			// 支付配置
			payConfig() {
				uni.showLoading({
					title: this.$t(`message.tips.loding`)
				});
				getPayConfig().then(res => {
					this.cartArr = cartArr;
					this.cartArr[0].payStatus = res.data.payWechatOpen ? 1 : 0;
					this.cartArr[1].payStatus = res.data.yuePayStatus ? 1 : 0;
					this.cartArr[1].userBalance = res.data.userBalance ? res.data.userBalance : 0;
					// #ifndef MP
					if (this.$wechat.isWeixin()) {
						this.cartArr[2].payStatus = 0;
					} else {
						this.cartArr[2].payStatus = res.data.aliPayStatus ? 1 : 0;
					}
					// #endif
					uni.hideLoading();
				})
			},
			payItem: Debounce(function(e) {
				let that = this;
				let active = e;
				that.active = active;
				that.animated = true;
				that.payType = that.cartArr[active].value;
				//that.computedPrice();
				setTimeout(function() {
					that.car();
				}, 500);
			}),
			car: function() {
				let that = this;
				that.animated = false;
			},

			subscribeMessage() {
				if (this.subscribing) return;
				this.subscribing = true;
				wx.getSetting({
					withSubscriptions:true,
					success: (res)=> {
						let allTemplIds = uni.getStorageSync('wechatSubscribeTempID');
						if (allTemplIds && res.subscriptionsSetting["mainSwitch"]) {
							let tmplIds = []
							if (!res.subscriptionsSetting.itemSettings) {
								tmplIds = allTemplIds
							} else {
								allTemplIds.forEach((tmplId) => {
									if (res.subscriptionsSetting.itemSettings[tmplId] != "reject") {
										tmplIds.push(tmplId)
									}
								});
							}
							if (tmplIds.length > 0) {
								wx.requestSubscribeMessage({
									tmplIds: tmplIds,
									success: (res) => {
										this.getOrderPay();
									},
									fail: (res) => {
										this.getOrderPay();
									}
								});
							} else {
								this.getOrderPay();
							}
						} else {
							this.getOrderPay();
						}
					},
					fail: (res) => {
						this.getOrderPay();
					}
				});
			},

			getOrderPay: Debounce(function() {
				this.subscribing = false;
				if (this.payType === 'yue') {
					this.payChannel = 'yue'
				} else if (this.payType == 'alipay') {
					// #ifdef H5
					this.payChannel = 'alipay';
					// #endif
					// #ifdef APP-PLUS
					this.payChannel = 'alipayApp';
					// #endif
				} else {
					// #ifdef H5
					if (this.$wechat.isWeixin) this.payChannel = this.$wechat.isWeixin ? 'public' : 'h5';
					// #endif
					// #ifdef APP-PLUS
					this.payChannel = this.systemPlatform === 'ios' ? 'wechatIos' : 'wechatAndroid';
					// #endif
					// #ifdef MP
					this.payChannel = "mini";
					if (this.productType == 'video') {
						this.payChannel = "video";
					} else {
						this.payChannel = "mini";
					}
					// #endif
				}
				uni.showLoading({
					title: this.$t(`message.tips.loding`)
				});
				let that = this;
				let goPages = '/pages/goods/order_pay_status/index?order_id=' + this.orderNo;

				orderPay({
					orderNo: this.orderNo,
					payChannel: that.payChannel,
					payType: that.payType,
					scene: that.productType === 'normal' ? 0 : 1177

				}).then(res => {
					let jsConfig = res.data.jsConfig;
					switch (res.data.payType) {
						case 'weixin':
							that.weixinPay(jsConfig, this.orderNo, goPages);
							break;
						case 'yue':
							setTimeout(() => {
								uni.hideLoading();
								uni.reLaunch({
									url: goPages + '&status=1'
								});
							}, 2000)
							break;
						case 'h5':
							uni.hideLoading();
							setTimeout(() => {
								location.href = jsConfig.mwebUrl + '&redirect_url=' + window
									.location
									.protocol + '//' + window.location.host + goPages +
									'&status=1';
							}, 2000)
							break;
						case 'alipay':
							//#ifdef H5
							if (this.$wechat.isWeixin()) {
								uni.redirectTo({
									url: `/pages/users/alipay_invoke/index?id=${this.orderNo}&type=order`
								});
							} else {
								//h5支付
								uni.hideLoading();
								that.formContent = res.data.alipayRequest;
								uni.setStorage({
									key: 'orderNo',
									data: this.orderNo
								});
								that.$nextTick(() => {
									document.forms['punchout_form'].submit();
								})
							}
							//#endif
							// #ifdef APP-PLUS
							let alipayRequest = res.data.alipayRequest;
							uni.requestPayment({
								provider: 'alipay',
								orderInfo: alipayRequest,
								success: (e) => {
									setTimeout(res => {
										uni.hideLoading();
										uni.navigateTo({
											url: '/pages/users/alipay_return/alipay_return?out_trade_no=' +
												this.orderNo + '&payChannel=' +
												'appAlipay'
										})
									}, 1000)
								},
								fail: (e) => {
									uni.showModal({
										content: "支付失败",
										showCancel: false,
										success: function(res) {
											if (res.confirm) {
												//点击确认的操作
												uni.navigateTo({
													url: '/pages/users/alipay_return/alipay_return?out_trade_no=' +
														this.orderNo +
														'&payChannel=' +
														'appAlipay'
												})
											}
										}
									})
								},
								complete: () => {
									uni.hideLoading();
								},
							});
							// #endif
							break;
					}
				}).catch(err => {
					console.log(err)
					uni.hideLoading();
					return that.$util.Tips({
						title: err
					});
				});
			}),
			weixinPay(jsConfig, orderNo, goPages) {
				let that = this;
				// #ifdef MP
				if (that.productType === 'video') {
					uni.requestOrderPayment({
						timeStamp: jsConfig.timeStamp,
						nonceStr: jsConfig.nonceStr,
						package: jsConfig.packages,
						signType: jsConfig.signType,
						paySign: jsConfig.paySign,
						ticket: that.productType === 'normal' ? null : jsConfig.ticket,
						success: function(ress) {
							console.log(ress);
							uni.hideLoading();
							uni.redirectTo({
								url: goPages
							})
							// openOrderSubscribe().then(() => {
							// 	if (that.orderInfoVo.bargainId || that.orderInfoVo.combinationId ||
							// 		that
							// 		.pinkId || that.orderInfoVo.seckillId) {
							// 		setTimeout(res => {
							// 			uni.hideLoading();
							// 			uni.reLaunch({
							// 				url: goPages
							// 			})
							// 		}, 1000);
							// 	}
							// 	setTimeout(res => {
							// 		uni.hideLoading();
							// 		uni.redirectTo({
							// 			url: goPages
							// 		})
							// 	}, 1000);
							// })
						},
						fail: function(e) {
							uni.hideLoading();
							return that.$util.Tips({
								title: '取消支付'
							}, {
								tab: 5,
								url: goPages + '&status=2'
							});
						},
						complete: function(e) {
							uni.hideLoading();
							// // requestPayment:fail cancel
							// if (e.errMsg == 'requestPayment:cancel') {
							// 	return that.$util.Tips({
							// 		title: '取消支付'
							// 	}, {
							// 		tab: 5,
							// 		url: goPages + '&status=2'
							// 	});
							// }
						},
					})
				} else {
					uni.requestPayment({
						timeStamp: jsConfig.timeStamp,
						nonceStr: jsConfig.nonceStr,
						package: jsConfig.packages,
						signType: jsConfig.signType,
						paySign: jsConfig.paySign,
						ticket: that.productType === 'normal' ? null : jsConfig.ticket,
						success: function(ress) {
							console.log(ress);
							uni.hideLoading();
							uni.redirectTo({
								url: goPages
							})
							// openOrderSubscribe().then((subscribeRes) => {
							// 	console.log(subscribeRes)
							// 	if (that.orderInfoVo.bargainId || that.orderInfoVo.combinationId || that.pinkId || that.orderInfoVo.seckillId) {
							// 		setTimeout(res => {
							// 			uni.hideLoading();
							// 			uni.reLaunch({
							// 				url: goPages
							// 			})
							// 		}, 1000);
							// 	}
							// 	setTimeout(res => {
							// 		uni.hideLoading();
							// 		uni.redirectTo({
							// 			url: goPages
							// 		})
							// 	}, 1000);
							// })
						},
						fail: function(e) {
							uni.hideLoading();
							return that.$util.Tips({
								title: '取消支付'
							}, {
								tab: 5,
								url: goPages + '&status=2'
							});
						},
						complete: function(e) {
							uni.hideLoading();
							// // requestPayment:fail cancel
							// if (e.errMsg == 'requestPayment:cancel') {
							// 	return that.$util.Tips({
							// 		title: '取消支付'
							// 	}, {
							// 		tab: 5,
							// 		url: goPages + '&status=2'
							// 	});
							// }
						},
					})
				}

				// #endif
				// #ifdef H5
				let data = {
					timestamp: jsConfig.timeStamp,
					nonceStr: jsConfig.nonceStr,
					package: jsConfig.packages,
					signType: jsConfig.signType,
					paySign: jsConfig.paySign
				};
				that.$wechat.pay(data).then(res => {
					if (res.errMsg == 'chooseWXPay:cancel') {
						uni.hideLoading();
						return that.$util.Tips({
							title: '取消支付'
						}, {
							tab: 5,
							url: goPages + '&status=2'
						});
					} else {
						wechatQueryPayResult(orderNo).then(res => {
							setTimeout(res => {
								uni.hideLoading();
								uni.redirectTo({
									url: goPages
								})
							}, 1000);
						}).cache(err => {
							uni.hideLoading();
							return that.$util.Tips({
								title: err
							});
						})
					}
				}).cache(res => {
					uni.hideLoading();
					return that.$util.Tips({
						title: '取消支付'
					}, {
						tab: 5,
						url: goPages + '&status=2'
					});
				});
				// #endif
				// #ifdef APP-PLUS
				let mp_pay_name = ''
				if (uni.requestOrderPayment) {
					mp_pay_name = 'requestOrderPayment'
				} else {
					mp_pay_name = 'requestPayment'
				}
				uni[mp_pay_name]({
					provider: 'wxpay',
					orderInfo: {
						"appid": jsConfig.appId, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
						"noncestr": jsConfig.nonceStr, // 随机字符串
						"package": "Sign=WXPay", // 固定值
						"partnerid": jsConfig.partnerid, // 微信支付商户号
						"prepayid": jsConfig.packages, // 统一下单订单号 
						"timestamp": Number(jsConfig.timeStamp), // 时间戳（单位：秒）
						"sign": this.systemPlatform === 'ios' ? 'MD5' : jsConfig.paySign // 签名，这里用的 MD5 签名
					}, //微信、支付宝订单数据 【注意微信的订单信息，键值应该全部是小写，不能采用驼峰命名】
					success: function(res) {
						wechatQueryPayResult(orderNo).then(res => {
							uni.hideLoading();
							let url = '/pages/goods/order_pay_status/index?order_id=' + orderNo +
								'&msg=支付成功';
							uni.showToast({
								title: "支付成功"
							})
							setTimeout(res => {
								uni.redirectTo({
									url: url
								})
							}, 2000)
						}).cache(err => {
							uni.hideLoading();
							return that.$util.Tips({
								title: err
							});
						})
					},
					fail: function(err) {
						uni.hideLoading();
						let url = '/pages/goods/order_pay_status/index?order_id=' + orderNo + '&msg=支付失败';
						uni.showModal({
							content: "支付失败",
							showCancel: false,
							success: function(res) {
								if (res.confirm) {
									uni.redirectTo({
										url: url
									})
								}
							}
						})
					},
					complete: (err) => {
						uni.hideLoading();
					}
				});
				// #endif
			},
		},
	}
</script>

<style lang="scss" scoped>
	.btn-box {
		padding: 0 30rpx;
		position: fixed;
		bottom: 15rpx;
		bottom: calc(15rpx + constant(safe-area-inset-bottom) / 2);
		bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);
	}

	.Bnt {
		font-size: 30rpx;
		font-weight: bold;
		color: #fff;
		width: 690rpx;
		height: 86rpx;
		border-radius: 43rpx;
		text-align: center;
		line-height: 86rpx;
	}

	.wrapper {

		padding: 30rpx;

		.list {
			margin-top: 50rpx;
		}

		.item {
			padding: 50rpx 30rpx;
			font-size: 30rpx;
			color: #333333;
			background-color: #fff;

			.title {
				text-align: center;
				@include main_color(theme);
				font-size: 34rpx;

				text {
					font-weight: 800;
					font-size: 50rpx;
				}
			}
		}

		.payItem {
			border-bottom: 1px solid #eee;
			justify-content: space-between;
			height: 138rpx;
			line-height: 138rpx;
			width: 100%;
			box-sizing: border-box;
			font-size: 32pxrpx;
			color: #333333;

			.on {
				// border-color: #fc5445;
				@include coupons_border_color(theme);
				color: $theme-color;
			}

			.name {

				.iconfont {
					width: 48rpx;
					height: 48rpx;
					border-radius: 50%;
					text-align: center;
					line-height: 48rpx;
					background-color: #fe960f;
					color: #fff;
					font-size: 30rpx;
					margin-right: 28rpx;
				}
			}

			.iconfont.icon-weixinzhifu1 {
				background-color: #41b035;
			}

			.iconfont.icon-zhifubao {
				background-color: #00AAEA;
			}

			.tip {
				width: 49%;
				text-align: center;
				font-size: 26rpx;
				color: #aaa;
			}

			.radio {
				.iconfont {
					font-size: 46rpx;
				}
			}
		}
	}
</style>
