{
    "name" : "OOSEEK",
    "appid" : "__UNI__BBCF574",
    "description" : "OOSEEK眼镜商城",
    "sassImplementationName" : "node-sass",
    "versionName" : "2.1",
    "versionCode" : 2,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "privacy" : {
            "prompt" : "template",
            "template" : {
                "title" : "服务协议和隐私政策",
                "message" : "　　请你务必审慎阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的设备标识、操作日志等信息用于分析、优化应用性能。<br/>　　你可阅读<a href='https://h5.merchant.java.crmeb.net/pages/users/agreement_info/index?from=userinfo'>《服务协议和隐私政策》</a>了解详细信息。如果你同意，请点击下面按钮开始接受我们的服务。",
                "buttonAccept" : "同意",
                "buttonRefuse" : "暂不同意",
                "second" : {
                    "title" : "温馨提示",
                    "message" : "　　进入应用前，你需先同意<a href='https://你的H5域名/pages/users/agreement_info/index?from=userinfo'>《服务协议和隐私政策》</a>，否则将退出应用。",
                    "buttonAccept" : "同意并继续",
                    "buttonRefuse" : "退出应用"
                }
            }
        },
        /* 模块配置 */
        "modules" : {
            "Share" : {},
            "Payment" : {},
            "OAuth" : {},
            "LivePusher" : {},
            "VideoPlayer" : {},
            "Maps" : {},
            "Webview-x5" : {},
            "Geolocation" : {},
            "iBeacon" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.BATTERY_STATS\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_CONFIGURATION\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "permissionPhoneState" : {
                    "request" : "none" //拨打电话权限关闭
                }
            },
            /* ios打包配置 */
            "ios" : {
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:app.beta.java.crmeb.net" ]
                    }
                },
                "privacyDescription" : {
                    "NSLocationWhenInUseUsageDescription" : "根据客户地理位置推荐最近门店",
                    "NSPhotoLibraryUsageDescription" : "上传用户头像保存分享海报",
                    "NSPhotoLibraryAddUsageDescription" : "上传用户头像保存分享海报",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "根据客户地理位置推荐最近门店",
                    "NSLocationAlwaysUsageDescription" : "根据客户地理位置推荐最近门店",
                    "NSCameraUsageDescription" : "上传用户头像保存分享海报"
                },
                "idfa" : false,
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "4e5cb4e85bef477cf4c2151c6f158bcf",
                        "appkey_android" : "ef8c6e93484e27d992461e24fd9ae9e3"
                    },
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "oauth" : {
                    "apple" : {},
                    "weixin" : {
                        "appid" : "wxa83d6fa13f",
                        "appsecret" : "e1d254e4cf0ffba",
                        "UniversalLinks" : "https://h5.merchant.java.crmeb.net/"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "4e5cb4e85be58bcf",
                        "appkey_android" : "ef8c6e93484e27d992461e24fd9ae9e3"
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxa83d6f13f",
                        "UniversalLinks" : "https://h5.merchant.java.crmeb.net/"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxa83dab13f",
                        "UniversalLinks" : "https://h5.merchant.java.crmeb.net/"
                    },
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxf539fb56823b3954",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : true,
            "es6" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于和门店的距离长度"
            }
        },
        "optimization" : {
            "subpackages" : true
        },
        // "plugins" : {
        //     "chat" : {
        //         "version" : "1.0.8",
        //         "provider" : "wx738958e0f4c894f9"
        //     }
        // },
        // "sitemapLocation" : "sitemap.json",
        "usingComponents" : true,
        "lazyCodeLoading" : "requiredComponents",
        "requiredPrivateInfos" : [ "getLocation", "chooseAddress", "chooseLocation" ]
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "appid" : "tt168860351e71d01",
        "setting" : {
            "es6" : true,
            "postcss" : true,
            "minified" : true
        }
    },
    "h5" : {
        "devServer" : {
            "https" : false
        },
        "router" : {
            "mode" : "history",
            "base" : ""
        },
        "domain" : "",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "RFZBZ-EF2AB-YVPUO-NW6H3-6L4D6-5DBHU"
                }
            }
        },
        // "optimization" : {
        //     "treeShaking" : {
        //         "enable" : true
        //     }
        // },
        "async" : {
            "timeout" : 200000
        },
        "title" : "OOSEEK",
        "template" : "template.h5.html"
    },
    "plus" : {
        "statusbar" : {
            "immersed" : true
        },
        "distribute" : {
            "google" : {
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                }
            }
        }
    },
    "fallbackLocale" : "zh-Hans",
    "locale" : "zh-Hans"
}
