// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

export default {
	token: state => state.app.token,
	isLogin: state => !!state.app.token,
	backgroundColor: state => state.app.backgroundColor,
	userInfo: state => state.app.userInfo || {},
	userAddress: state => state.app.userAddress || null,
	uid: state => state.app.uid,
	homeActive: state => state.app.homeActive,
	home: state => state.app.home,
	chatUrl: state => state.app.chatUrl,
	systemPlatform: state => state.app.systemPlatform,
	productType: state => state.app.productType,
	globalData: state => state.app.globalData,
	merchantClassify: state => state.app.merchantClassify,
	merchantType: state => state.app.merchantType,
	merchantAPPInfo: state => state.app.merchantAPPInfo,
	merSttledData: state => state.app.merSttledData,
	productCategoryTree: state => state.app.productCategoryTree,
	currentMerId: state => state.app.currentMerId
};
