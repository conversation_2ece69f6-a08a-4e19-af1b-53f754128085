<template>
	<view :data-theme="theme" class="optometry-page">
		<form @submit="formSubmit" report-submit='true'>
			<!-- 自定义导航栏 -->
			<view class="custom-navbar" :style="navbarStyle">
				<view class="navbar-content" :style="navbarContentStyle">
					<view class="navbar-left" @click="goBack">
						<text class="iconfont icon-fanhui"></text>
					</view>
					<view class="navbar-title">验光单</view>
				</view>
			</view>

			<!-- 渐变背景区域 -->
			<view class="gradient-section" :style="gradientSectionStyle">
				<!-- 用户头像 -->
				<view class="user-avatar">
					<image src="/static/images/default-avatar.png" class="avatar-img"></image>
				</view>

				<!-- 基本信息区域 -->
				<view class="basic-info">
					<view class="info-title">基本信息</view>
					<view class="name-input-row">
						<text class="label">姓名：</text>
						<input
							type="text"
							placeholder="请输入姓名"
							name="name"
							:value="optometry.name"
							class="name-input"
							maxlength="20"
						/>
					</view>
				</view>
			</view>

		<!-- 屈光度区域 -->
		<view class="refraction-section">
			<view class="refraction-title">屈光度</view>

			<!-- 左右眼标题 -->
			<view class="eye-headers">
				<view class="eye-header">OD(右眼)</view>
				<view class="eye-header">OD(左眼)</view>
			</view>

			<!-- 度数行 -->
			<view class="param-row">
				<view class="param-label">度数</view>
				<view class="param-inputs">
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseRS">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getRSDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseRS">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseLS">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getLSDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseLS">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- 散光行 -->
			<view class="param-row">
				<view class="param-label">散光</view>
				<view class="param-inputs">
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseRC">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getRCDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseRC">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseLC">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getLCDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseLC">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- 轴位行 -->
			<view class="param-row">
				<view class="param-label">轴位</view>
				<view class="param-inputs">
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseRA">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getRADisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseRA">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseLA">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getLADisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseLA">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- 瞳距行 -->
			<view class="param-row">
				<view class="param-label">瞳距</view>
				<view class="param-inputs">
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseRPD">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getRPDDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseRPD">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseLPD">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getLPDDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseLPD">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- 瞳高行 -->
			<view class="param-row">
				<view class="param-label">瞳高</view>
				<view class="param-inputs">
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseRPH">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getRPHDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseRPH">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
					<view class="param-input-stepper">
						<view class="stepper-btn" @click="decreaseLPH">
							<text class="iconfont icon-jianhao"></text>
						</view>
						<view class="stepper-value">{{getLPHDisplayValue()}}</view>
						<view class="stepper-btn" @click="increaseLPH">
							<text class="iconfont icon-jiahao"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- 下加光行 -->
			<view class="param-row single-param">
				<view class="param-label">下加光(mm)</view>
				<view class="single-input-stepper">
					<view class="stepper-btn" @click="decreaseADD">
						<text class="iconfont icon-jianhao"></text>
					</view>
					<view class="stepper-value">{{getADDDisplayValue()}}</view>
					<view class="stepper-btn" @click="increaseADD">
						<text class="iconfont icon-jiahao"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- 上传验光单照片 -->
		<view class="upload-section">
			<view class="upload-title">上传验光单照片(非必填)</view>
			<view class="upload-area" @click="uploadpic">
				<view v-if="!optometry.url" class="upload-placeholder">
					<text class="iconfont icon-jiahao upload-icon"></text>
				</view>
				<image v-else :src="optometry.url" class="uploaded-image" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 设置默认验光单 -->
		<view class="default-section" @click="ChangeIsDefault">
			<checkbox-group>
				<checkbox :checked="optometry.isDefault" class="default-checkbox" />
				<text class="default-text">设置为默认验光单</text>
			</checkbox-group>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" form-type="submit">保存</button>
		</view>
		</form>
	</view>
</template>

<script>
	import {
		editOptometry,
		getOptometryDetail
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		data() {
			return {
				id: 0,
				optometry: {
					data: {
						RS: 0,
						RC: 0,
						RA: 0,
						LS: 0,
						LC: 0,
						LA: 0,
						RPD: 0,
						LPD: 0,
						RPH: 0,
						LPH: 0,
						ADD: 0
					},
					url: '',
					isDefault: false
				},
				LS_list: ["左眼度数"],
				RS_list: ["右眼度数"],
				LC_list: ["左眼散光"],
				RC_list: ["右眼散光"],
				LA_list: ["左眼轴位"],
				RA_list: ["右眼轴位"],
				LPD_list: ["左眼瞳距"],
				RPD_list: ["右眼瞳距"],
				LPH_list: ["左眼瞳高"],
				RPH_list: ["右眼瞳高"],
				ADD_list: [],
				S_list: [0],
				C_list: [0],
				A_list: [0],
				PD_list: [0],
				PH_list: [0],
				LS_index: 0,
				RS_index: 0,
				LC_index: 0,
				RC_index: 0,
				LA_index: 0,
				RA_index: 0,
				LPD_index: 0,
				RPD_index: 0,
				LPH_index: 0,
				RPH_index: 0,
				ADD_index: 0,
				theme: app.globalData.theme,
				showLoading: false,
				nearShow: false,
				statusBarHeight: 44,
				navbarStyle: '',
				navbarContentStyle: '',
				gradientSectionStyle: '',
				capsuleInfo: {}
			};
		},
		computed: mapGetters(['isLogin']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getOptometryDetail();
					}
				},
				deep: true
			}
		},
		onLoad(options) {
			// 设置状态栏高度和胶囊按钮信息
			const systemInfo = uni.getSystemInfoSync();
			const statusBarHeight = systemInfo.statusBarHeight || 44;
			this.statusBarHeight = statusBarHeight;

			// 获取胶囊按钮信息（仅在微信小程序中有效）
			// #ifdef MP-WEIXIN
			const capsuleInfo = uni.getMenuButtonBoundingClientRect();
			this.capsuleInfo = capsuleInfo;

			// 计算导航栏样式
			const navbarHeight = (capsuleInfo.top - statusBarHeight) * 2 + capsuleInfo.height;
			const navbarPaddingTop = statusBarHeight;
			const navbarContentHeight = capsuleInfo.height;
			const navbarContentTop = capsuleInfo.top - statusBarHeight;

			this.navbarStyle = `
				padding-top: ${navbarPaddingTop}px;
				height: ${navbarHeight + navbarPaddingTop}px;
			`;

			this.navbarContentStyle = `
				height: ${navbarContentHeight}px;
				margin-top: ${navbarContentTop}px;
				padding-right: ${systemInfo.windowWidth - capsuleInfo.left}px;
			`;

			// 计算渐变区域的padding-top
			this.gradientSectionStyle = `
				padding-top: ${navbarHeight + navbarPaddingTop + 40}px;
			`;
			// #endif

			// #ifndef MP-WEIXIN
			// 非微信小程序环境的默认设置
			this.navbarStyle = `
				padding-top: ${statusBarHeight}px;
				height: ${88 + statusBarHeight}px;
			`;
			this.navbarContentStyle = `
				height: 88px;
				margin-top: 0px;
				padding-right: 32px;
			`;
			this.gradientSectionStyle = `
				padding-top: ${88 + statusBarHeight + 40}px;
			`;
			// #endif

			// 度数
			let S_value_list = [],
				S_text_list = [],
				cur_S = -25.00;
			while (cur_S <= -18) {
				S_value_list.push(cur_S)
				S_text_list.push("近视" + parseInt(-cur_S * 100) + "度(" + cur_S + ")")
				cur_S += 1
			}
			cur_S = -17.5
			while (cur_S <= -15) {
				S_value_list.push(cur_S)
				S_text_list.push("近视" + parseInt(-cur_S * 100) + "度(" + cur_S + ")")
				cur_S += 0.5
			}
			cur_S = -14.75
			while (cur_S <= 12) {
				let text = "平光(";
				if (cur_S < 0) {
					text = "近视" + parseInt(-cur_S * 100) + "度("
				}
				if (cur_S > 0) {
					text = "远视" + parseInt(cur_S * 100) + "度(+"
				}
				S_value_list.push(cur_S)
				S_text_list.push(text + cur_S + ")")
				cur_S += 0.25
			}
			this.S_list = S_value_list;
			this.LS_list = [...S_text_list];
			this.RS_list = [...S_text_list];
			for (let index in S_value_list) {
				if (S_value_list[index] == 0) {
					this.LS_list[index] = "左眼度数";
					this.RS_list[index] = "右眼度数";
					this.LS_index = index;
					this.RS_index = index;
				}
			}
			
			// 散光
			let C_value_list = [],
				C_text_list = [],
				cur_C = -0.25;
			while (cur_C >= -7) {
				let text = "散光" + parseInt(-cur_C * 100) + "度";
				C_value_list.push(cur_C)
				C_text_list.push(text + "(" + cur_C + ")")
				cur_C -= 0.25
			}
			this.C_list = this.C_list.concat(C_value_list);
			this.LC_list = this.LC_list.concat(C_text_list);
			this.RC_list = this.RC_list.concat(C_text_list);
			
			// 轴位
			let A_value_list = [],
				A_text_list = [],
				cur_A = 1;
			while (cur_A <= 180) {
				A_value_list.push(cur_A)
				A_text_list.push(cur_A + '')
				cur_A += 1
			}
			this.A_list = this.A_list.concat(A_value_list);
			this.LA_list = this.LA_list.concat(A_text_list);
			this.RA_list = this.RA_list.concat(A_text_list);
			
			// 瞳距
			let PD_value_list = [],
				PD_text_list = [],
				cur_PD = 25;
			while (cur_PD <= 40) {
				PD_value_list.push(cur_PD)
				PD_text_list.push(cur_PD + '')
				cur_PD += 0.5
			}
			this.PD_list = this.PD_list.concat(PD_value_list);
			this.LPD_list = this.LPD_list.concat(PD_text_list);
			this.RPD_list = this.RPD_list.concat(PD_text_list);
			
			// 瞳高
			let PH_value_list = [],
				PH_text_list = [],
				cur_PH = 25;
			while (cur_PH <= 40) {
				PH_value_list.push(cur_PH)
				PH_text_list.push(cur_PH + '')
				cur_PH += 0.5
			}
			this.PH_list = this.PH_list.concat(PH_value_list);
			this.LPH_list = this.LPH_list.concat(PH_text_list);
			this.RPH_list = this.RPH_list.concat(PH_text_list);
			
			// 下加光
			let ADD_value_list = [],
				cur_ADD = 0;
			while (cur_ADD <= 4) {
				ADD_value_list.push(cur_ADD)
				cur_ADD += 0.25
			}
			this.ADD_list = this.ADD_list.concat(ADD_value_list);

			if (this.isLogin) {
				this.id = options.id || 0;
				uni.setNavigationBarTitle({
					title: options.id ? '修改验光单' : '添加验光单'
				})
				this.getOptometryDetail();
			} else {
				toLogin();
			}
		},
		methods: {
			getOptometryDetail: function() {
				if (!this.id) return false;
				let that = this;
				getOptometryDetail(this.id).then(res => {
					if(res.data){
						res.data.data = JSON.parse(res.data.data);
						that.$set(that, 'optometry', res.data);
						that.initialize();
					}
				});
			},
			initialize: function() {
				let that = this;
				this.S_list.forEach((item, index) => {
					if (that.optometry.data.LS != 0 && that.optometry.data.LS == item) {
						that.LS_index = index;
					}
					if (that.optometry.data.RS != 0 && that.optometry.data.RS == item) {
						that.RS_index = index;
					}
				});
				this.C_list.forEach((item, index) => {
					if (that.optometry.data.LC < 0 && that.optometry.data.LC == item) {
						that.LC_index = index;
					}
					if (that.optometry.data.RC < 0 && that.optometry.data.RC == item) {
						that.RC_index = index;
					}
				});
				this.A_list.forEach((item, index) => {
					if (that.optometry.data.LA > 0 && that.optometry.data.LA == item) {
						that.LA_index = index;
					}
					if (that.optometry.data.RA > 0 && that.optometry.data.RA == item) {
						that.RA_index = index;
					}
				});
				this.PD_list.forEach((item, index) => {
					if (that.optometry.data.LPD > 0 && that.optometry.data.LPD == item) {
						that.LPD_index = index;
					}
					if (that.optometry.data.RPD > 0 && that.optometry.data.RPD == item) {
						that.RPD_index = index;
					}
				});
				this.PH_list.forEach((item, index) => {
					if (that.optometry.data.LPH > 0 && that.optometry.data.LPH == item) {
						that.LPH_index = index;
					}
					if (that.optometry.data.RPH > 0 && that.optometry.data.RPH == item) {
						that.RPH_index = index;
					}
				});
				this.ADD_list.forEach((item, index) => {
					if (that.optometry.data.ADD > 0 && that.optometry.data.ADD == item) {
						that.ADD_index = index;
					}
				});
			},
			uploadpic: function() {
				let that = this;
				that.$util.uploadImageOne({
					url: 'upload/image',
					name: 'multipart',
					model: "maintain",
					pid: 0
				}, function(res) {
					that.optometry.url = res.data.url;
				});
			},
			/**
			 * 提交
			 */
			formSubmit: Debounce(function(e) {
				let that = this,
					value = e.detail.value;
				if (!value.name) return that.$util.Tips({
					title: '请填写验光人姓名'
				});
				let add = parseInt(that.optometry.data.ADD);
				if (isNaN(add) || add <= 0) {
					that.optometry.data.ADD = 0;
				}

				value.id = that.id;
				value.data = JSON.stringify(that.optometry.data);
				value.url = that.optometry.url;
				value.isDefault = that.optometry.isDefault;

				uni.showLoading({
					title: '保存中',
					mask: true
				})
				editOptometry(value).then(res => {
					if (that.id)
						that.$util.Tips({
							title: '修改成功',
							icon: 'success'
						});
					else
						that.$util.Tips({
							title: '添加成功',
							icon: 'success'
						});
					setTimeout(function() {
						return uni.navigateBack({
							delta: 1,
						})
					}, 1000);
				}).catch(err => {
					return that.$util.Tips({
						title: err
					});
				})
			}),
			bindLSChange: function(e) {
				this.LS_index = e.detail.value;
				this.optometry.data.LS = this.S_list[this.LS_index]
			},
			bindRSChange: function(e) {
				this.RS_index = e.detail.value;
				this.optometry.data.RS = this.S_list[this.RS_index]
			},
			bindLCChange: function(e) {
				this.LC_index = e.detail.value;
				this.optometry.data.LC = this.C_list[this.LC_index]
			},
			bindRCChange: function(e) {
				this.RC_index = e.detail.value;
				this.optometry.data.RC = this.C_list[this.RC_index]
			},
			bindLAChange: function(e) {
				this.LA_index = e.detail.value;
				this.optometry.data.LA = this.A_list[this.LA_index]
			},
			bindRAChange: function(e) {
				this.RA_index = e.detail.value;
				this.optometry.data.RA = this.A_list[this.RA_index]
			},
			bindLPDChange: function(e) {
				this.LPD_index = e.detail.value;
				this.optometry.data.LPD = this.PD_list[this.LPD_index]
			},
			bindRPDChange: function(e) {
				this.RPD_index = e.detail.value;
				this.optometry.data.RPD = this.PD_list[this.RPD_index]
			},
			bindLPHChange: function(e) {
				this.LPH_index = e.detail.value;
				this.optometry.data.LPH = this.PH_list[this.LPH_index]
			},
			bindRPHChange: function(e) {
				this.RPH_index = e.detail.value;
				this.optometry.data.RPH = this.PH_list[this.RPH_index]
			},
			bindADDChange: function(e) {
				this.ADD_index = e.detail.value;
				this.optometry.data.ADD = this.ADD_list[this.ADD_index]
			},
			ChangeIsDefault: function(e) {
				this.$set(this.optometry, 'isDefault', !this.optometry.isDefault);
			},

			// 返回上一页
			goBack: function() {
				uni.navigateBack({
					delta: 1
				});
			},

			// 获取显示值方法
			getRSDisplayValue: function() {
				return this.S_list[this.RS_index];
			},
			getLSDisplayValue: function() {
				return this.S_list[this.LS_index];
			},
			getRCDisplayValue: function() {
				return this.C_list[this.RC_index];
			},
			getLCDisplayValue: function() {
				return this.C_list[this.LC_index];
			},
			getRADisplayValue: function() {
				return this.A_list[this.RA_index];
			},
			getLADisplayValue: function() {
				return this.A_list[this.LA_index];
			},
			getRPDDisplayValue: function() {
				return this.PD_list[this.RPD_index];
			},
			getLPDDisplayValue: function() {
				return this.PD_list[this.LPD_index];
			},
			getRPHDisplayValue: function() {
				return this.PH_list[this.RPH_index];
			},
			getLPHDisplayValue: function() {
				return this.PH_list[this.LPH_index];
			},
			getADDDisplayValue: function() {
				return this.ADD_list[this.ADD_index];
			},

			// 度数加减方法
			increaseRS: function() {
				if (this.RS_index < this.S_list.length - 1) {
					this.RS_index++;
					this.optometry.data.RS = this.S_list[this.RS_index];
				}
			},
			decreaseRS: function() {
				if (this.RS_index > 0) {
					this.RS_index--;
					this.optometry.data.RS = this.S_list[this.RS_index];
				}
			},
			increaseLS: function() {
				if (this.LS_index < this.S_list.length - 1) {
					this.LS_index++;
					this.optometry.data.LS = this.S_list[this.LS_index];
				}
			},
			decreaseLS: function() {
				if (this.LS_index > 0) {
					this.LS_index--;
					this.optometry.data.LS = this.S_list[this.LS_index];
				}
			},

			// 散光加减方法
			increaseRC: function() {
				if (this.RC_index < this.C_list.length - 1) {
					this.RC_index++;
					this.optometry.data.RC = this.C_list[this.RC_index];
				}
			},
			decreaseRC: function() {
				if (this.RC_index > 0) {
					this.RC_index--;
					this.optometry.data.RC = this.C_list[this.RC_index];
				}
			},
			increaseLC: function() {
				if (this.LC_index < this.C_list.length - 1) {
					this.LC_index++;
					this.optometry.data.LC = this.C_list[this.LC_index];
				}
			},
			decreaseLC: function() {
				if (this.LC_index > 0) {
					this.LC_index--;
					this.optometry.data.LC = this.C_list[this.LC_index];
				}
			},

			// 轴位加减方法
			increaseRA: function() {
				if (this.RA_index < this.A_list.length - 1) {
					this.RA_index++;
					this.optometry.data.RA = this.A_list[this.RA_index];
				}
			},
			decreaseRA: function() {
				if (this.RA_index > 0) {
					this.RA_index--;
					this.optometry.data.RA = this.A_list[this.RA_index];
				}
			},
			increaseLA: function() {
				if (this.LA_index < this.A_list.length - 1) {
					this.LA_index++;
					this.optometry.data.LA = this.A_list[this.LA_index];
				}
			},
			decreaseLA: function() {
				if (this.LA_index > 0) {
					this.LA_index--;
					this.optometry.data.LA = this.A_list[this.LA_index];
				}
			},

			// 瞳距加减方法
			increaseRPD: function() {
				if (this.RPD_index < this.PD_list.length - 1) {
					this.RPD_index++;
					this.optometry.data.RPD = this.PD_list[this.RPD_index];
				}
			},
			decreaseRPD: function() {
				if (this.RPD_index > 0) {
					this.RPD_index--;
					this.optometry.data.RPD = this.PD_list[this.RPD_index];
				}
			},
			increaseLPD: function() {
				if (this.LPD_index < this.PD_list.length - 1) {
					this.LPD_index++;
					this.optometry.data.LPD = this.PD_list[this.LPD_index];
				}
			},
			decreaseLPD: function() {
				if (this.LPD_index > 0) {
					this.LPD_index--;
					this.optometry.data.LPD = this.PD_list[this.LPD_index];
				}
			},

			// 瞳高加减方法
			increaseRPH: function() {
				if (this.RPH_index < this.PH_list.length - 1) {
					this.RPH_index++;
					this.optometry.data.RPH = this.PH_list[this.RPH_index];
				}
			},
			decreaseRPH: function() {
				if (this.RPH_index > 0) {
					this.RPH_index--;
					this.optometry.data.RPH = this.PH_list[this.RPH_index];
				}
			},
			increaseLPH: function() {
				if (this.LPH_index < this.PH_list.length - 1) {
					this.LPH_index++;
					this.optometry.data.LPH = this.PH_list[this.LPH_index];
				}
			},
			decreaseLPH: function() {
				if (this.LPH_index > 0) {
					this.LPH_index--;
					this.optometry.data.LPH = this.PH_list[this.LPH_index];
				}
			},

			// 下加光加减方法
			increaseADD: function() {
				if (this.ADD_index < this.ADD_list.length - 1) {
					this.ADD_index++;
					this.optometry.data.ADD = this.ADD_list[this.ADD_index];
				}
			},
			decreaseADD: function() {
				if (this.ADD_index > 0) {
					this.ADD_index--;
					this.optometry.data.ADD = this.ADD_list[this.ADD_index];
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	page {
		background: linear-gradient(180deg, #BDFD5B 0%, #BDFD5B 400rpx, #f5f5f5 400rpx, #f5f5f5 100%);
		--status-bar-height: 44rpx; /* 默认状态栏高度 */
	}

	.optometry-page {
		min-height: 100vh;
		background: transparent;
	}

	// 自定义导航栏
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: transparent;

		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-left: 32rpx;

			.navbar-left {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.iconfont {
					font-size: 36rpx;
					color: #333333;
				}
			}

			.navbar-title {
				flex: 1;
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
				color: #333333;
				margin-left: 50rpx; /* 平衡左侧返回按钮的宽度 */
			}
		}
	}

	// 渐变背景区域
	.gradient-section {
		position: relative;
		padding-left: 32rpx;
		padding-right: 32rpx;

		// 渐变背景伪元素
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 104rpx; // 在基本信息标题下方结束（40rpx底部间距 + 32rpx卡片底部间距 + 32rpx标题下边距）
			background: linear-gradient(180deg, #b8fe4e, #f2f8e0);
			z-index: 0;
		}

		// 用户头像
		.user-avatar {
			position: relative;
			z-index: 1;
			display: flex;
			justify-content: center;
			margin-bottom: 40rpx;

			.avatar-img {
				width: 120rpx;
				height: 120rpx;
				border-radius: 60rpx;
				background-color: #ffffff;
			}
		}

		// 基本信息区域
		.basic-info {
			position: relative;
			z-index: 1;
			background-color: #ffffff;
			border-radius: 16rpx;
			padding: 32rpx;
			margin-bottom: 24rpx;

			.info-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333333;
				margin-bottom: 32rpx;
			}

			.name-input-row {
				display: flex;
				align-items: center;

				.label {
					font-size: 28rpx;
					color: #333333;
					margin-right: 24rpx;
				}

				.name-input {
					flex: 1;
					font-size: 28rpx;
					color: #333333;
					text-align: right;
					border: none;
					background: transparent;
					outline: none;
				}
			}
		}
	}

	// 屈光度区域
	.refraction-section {
		background-color: #ffffff;
		margin: 24rpx 32rpx;
		border-radius: 16rpx;
		padding: 32rpx;

		.refraction-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
			margin-bottom: 32rpx;
		}

		// 左右眼标题
		.eye-headers {
			display: flex;
			margin-bottom: 24rpx;
			padding-left: 120rpx;

			.eye-header {
				flex: 1;
				text-align: center;
				font-size: 28rpx;
				color: #666666;
				font-weight: 500;
			}
		}

		// 参数行
		.param-row {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;

			&.single-param {
				.param-label {
					width: 200rpx;
				}
			}

			.param-label {
				width: 120rpx;
				font-size: 28rpx;
				color: #333333;
			}

			.param-inputs {
				flex: 1;
				display: flex;
				gap: 24rpx;

				.param-input-stepper {
					flex: 1;
					height: 80rpx;
					background-color: #f8f8f8;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 0 16rpx;

					.stepper-btn {
						width: 48rpx;
						height: 48rpx;
						background-color: #ffffff;
						border-radius: 6rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border: 1rpx solid #e0e0e0;

						.iconfont {
							font-size: 24rpx;
							color: #666666;
						}

						&:active {
							background-color: #f0f0f0;
						}
					}

					.stepper-value {
						flex: 1;
						text-align: center;
						font-size: 28rpx;
						color: #333333;
						font-weight: 500;
					}
				}
			}

			.single-input-stepper {
				width: 200rpx;
				height: 80rpx;
				background-color: #f8f8f8;
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 16rpx;

				.stepper-btn {
					width: 48rpx;
					height: 48rpx;
					background-color: #ffffff;
					border-radius: 6rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					border: 1rpx solid #e0e0e0;

					.iconfont {
						font-size: 24rpx;
						color: #666666;
					}

					&:active {
						background-color: #f0f0f0;
					}
				}

				.stepper-value {
					flex: 1;
					text-align: center;
					font-size: 28rpx;
					color: #333333;
					font-weight: 500;
				}
			}
		}
	}

	// 上传验光单照片
	.upload-section {
		background-color: #ffffff;
		margin: 24rpx 32rpx;
		border-radius: 16rpx;
		padding: 32rpx;

		.upload-title {
			font-size: 28rpx;
			color: #333333;
			margin-bottom: 24rpx;
		}

		.upload-area {
			width: 100%;
			height: 200rpx;
			background-color: #f8f8f8;
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 2rpx dashed #d9d9d9;

			.upload-placeholder {
				display: flex;
				align-items: center;
				justify-content: center;

				.upload-icon {
					font-size: 48rpx;
					color: #999999;
				}
			}

			.uploaded-image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}
		}
	}

	// 设置默认验光单
	.default-section {
		background-color: #ffffff;
		margin: 24rpx 32rpx;
		border-radius: 16rpx;
		padding: 32rpx;
		display: flex;
		align-items: center;

		checkbox-group {
			display: flex;
			align-items: center;
			width: 100%;
		}

		.default-checkbox {
			margin-right: 16rpx;
		}

		.default-text {
			font-size: 28rpx;
			color: #333333;
		}
	}

	// 保存按钮
	.save-section {
		padding: 40rpx 32rpx;
		padding-bottom: calc(40rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(40rpx + env(safe-area-inset-bottom));

		.save-btn {
			width: 100%;
			height: 88rpx;
			background-color: #BDFD5B;
			border-radius: 44rpx;
			border: none;
			font-size: 32rpx;
			color: #333333;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;

			&:active {
				background-color: #8AEF8A;
			}
		}
	}

	// 复选框样式
	/deep/ checkbox .wx-checkbox-input {
		width: 36rpx !important;
		height: 36rpx !important;
		border-radius: 4rpx !important;
		border: 2rpx solid #d9d9d9 !important;
	}

	/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
		background-color: #BDFD5B !important;
		border-color: #BDFD5B !important;
	}

	/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
		content: '✓';
		font-size: 24rpx;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		line-height: 1;
	}

	/deep/ checkbox .uni-checkbox-input {
		width: 36rpx !important;
		height: 36rpx !important;
		border-radius: 4rpx !important;
		border: 2rpx solid #d9d9d9 !important;
	}

	/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {
		background-color: #BDFD5B !important;
		border-color: #BDFD5B !important;
	}

	/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked::before {
		content: '✓';
		font-size: 24rpx;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		line-height: 1;
	}
</style>
