<template>
	<view class="user_about" :data-theme="theme">
		<view>
			<view class="text" :class="{cancelTxt: type == 'useraccountcancelnoticeinfo'}">
				<jyf-parser :html="agreementData?agreementData.replace(/<br\/>/ig, ''):''" ref="article" :tag-style="tagStyle"></jyf-parser>
			</view>
			<view class="btn" >
				<button v-if="type != 'useraccountcancelnoticeinfo'" @tap="navigateBack" class="sure-btn">确定</button>
				<button v-else @click="cancelBtn" class="sure-btn">申请注销</button>
			</view>
		</view>
		<view class="cancel" v-if="type == 'useraccountcancelnoticeinfo' && loaded">
			<view class="checkbox" @click="setCheck">
				<text v-if="!check" class="iconfont icon-weixuanzhong"></text>
				<text v-else class="iconfont icon-xuanzhong1"></text>
				<view class="checkbox-text ml10">已阅读并同意<view class="font font-color" @click.stop="toCancel">《注销协议》</view></view>
			</view>
		</view>
		<view class="outMoal" v-if="moal">
			<view class="box">
				<view class="title">该账号将永久注销</view>
				<view class="moalBtn">
					<view class="ok" @click="ok">确定</view>
					<view class="no" @click="cancelMoal">取消</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		agreementInfo
	} from '@/api/api.js'
	import {
		userOut,
	} from '@/api/user.js'
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		name: 'user_about',
		data() {
			return {
				theme: app.globalData.theme,
				type: '',
				agreementData: '',
				check: false,
				moal: false,
				loaded: false,
				tagStyle: {
					img: 'width:100%;display:block;'
				},
			}
		},
		onLoad: function(options) {
			this.type = options.from;
			this.setTitle(this.type);
			this.getCacheinfo();
		},
		methods: {
			navigateBack() {
				uni.navigateBack({
					delta: 1
				})
			},
			toCancel() {
				uni.navigateTo({
					url: '/pages/users/agreement_info/index?from=useraccountcancelinfo'
				})
			},
			setCheck() {
				this.check = !this.check
			},
			ok() {
				uni.showLoading({
					title: '注销中',
					mask: true
				});
				this.moal = false;
				userOut().then(res => {
					this.$store.commit("LOGOUT");
					uni.hideLoading();
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						});
					}, 500);
				}).then(err => {
					uni.hideLoading()
					this.$util.Tips({
						title: err
					});
				});
			},
			cancelBtn() {
				if (!this.check) {
					return uni.showToast({
						title: "请勾选已阅读",
						icon: 'none',
						duration: 2000,
					})
				} else {
					this.moal = true
				}
			},
			cancelMoal() {
				this.moal = false
				this.check = false
			},
			getCacheinfo() {
				this.loaded = false;
				agreementInfo(this.type).then(res => {
					this.agreementData = JSON.parse(res.data).agreement
					this.loaded = true;
				})
			},
			setTitle(e) {
				switch (e) {
					case 'aboutusinfo':
						uni.setNavigationBarTitle({
							title: '关于我们协议'
						})
						break;
					case 'intelligentinfo':
						uni.setNavigationBarTitle({
							title: '平台资质证明'
						})
						break;
					case 'merincomminginfo':
						uni.setNavigationBarTitle({
							title: '商户入驻协议'
						})
						break;
					case 'useraccountcancelinfo':
						uni.setNavigationBarTitle({
							title: '用户注销协议'
						})
						break;
					case 'useraccountcancelnoticeinfo':
						uni.setNavigationBarTitle({
							title: '用户注销声明'
						})
						break;
					case 'userinfo':
						uni.setNavigationBarTitle({
							title: '用户注册协议'
						})
						break;
					default:
						uni.setNavigationBarTitle({
							title: '用户隐私协议'
						})
						break;
				}
			}
		}
	}
</script>

<style lang="scss">
	.user_about {
		.text {
			font-size: 30rpx;
			font-weight: 400;
			padding: 30rpx;
			color: #282828;
			padding-bottom: 120rpx;
			padding-bottom: calc(120rpx + constant(safe-area-inset-bottom) / 2);
			padding-bottom: calc(120rpx + env(safe-area-inset-bottom) / 2);
		}

		.cancelTxt {
			overflow: hidden;
			overflow-y: auto;
			padding-bottom: 200rpx;
			padding-bottom: calc(200rpx + constant(safe-area-inset-bottom) / 2);
			padding-bottom: calc(200rpx + env(safe-area-inset-bottom) / 2);

			image {
				max-width: 100%;
			}
		}

		.cancel {
			position: fixed;
			left: 0;
			z-index: 1;
			width: 100%;
			bottom: 160rpx;
			bottom: calc(160rpx + constant(safe-area-inset-bottom) / 2);
			bottom: calc(160rpx + env(safe-area-inset-bottom) / 2);

			.checkbox {
				width: 100%;
				text-align: center;
				margin: 0 auto;
				font-size: 24rpx;
				font-weight: 400;
				display: flex;
				justify-content: center;
				align-items: center;

				span {
					margin-left: 5rpx;
				}

				.font {
					@include main_color(theme);
					font-style: normal;
				}

				.iconfont {
					font-size: 24rpx;
				}
				
				.checkbox-text {
					display: flex;
				}
			}

			.btn {
				width: 690rpx;
				height: 90rpx;
				@include linear-gradient(theme);
				border-radius: 45rpx;
				margin: 0 auto;
				margin-top: 30rpx;
				text-align: center;
				line-height: 90rpx;
				font-size: 32rpx;
				font-weight: 400;
				color: #FFFFFF;
			}
		}
	}

	.outMoal {
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: center;

		.box {
			position: fixed;
			width: 590rpx;
			height: 258rpx;
			background: #FFFFFF;
			opacity: 1;
			border-radius: 20rpx;
			text-align: center;
			padding: 50rpx;

			.title {
				font-size: 30rpx;
				font-weight: 600;
				color: #282828;
			}

			.moalBtn {
				margin-top: 43rpx;
				display: flex;
				justify-content: space-between;

				.ok {
					width: 234rpx;
					height: 66rpx;
					@include coupons_border_color(theme);
					border-radius: 33rpx;
					font-size: 26rpx;
					line-height: 66rpx;
					@include main_color(theme);
				}

				.no {
					width: 234rpx;
					height: 66rpx;
					@include linear-gradient(theme);
					border-radius: 33rpx;
					font-size: 26rpx;
					color: #FFFFFF;
					line-height: 66rpx;
				}
			}
		}
	}
	.btn {
		width: 100%;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		padding: 17rpx 0;
		padding-bottom: 15rpx;
		padding-bottom: calc(15rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);
		.sure-btn {
			width: 690rpx;
			height: 86rpx;
			margin: 0 auto;
			line-height: 86rpx;
			color: #FFFFFF;
			border-radius: 43rpx;
			@include main_bg_color(theme);
			font-size: 32rpx;
		}
	}
</style>
