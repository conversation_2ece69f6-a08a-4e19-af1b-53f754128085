<template>
	<view class="pay_status">
		<view style="display: flex;justify-content: center;margin-top: 40rpx;">
			<img :src="statusSrc" alt="" mode="widthFix" style="width: 300rpx;" />
		</view>
		<view style="font-size: 32rpx;text-align: center;padding: 20rpx;font-weight: 600;">{{statusTitle}}</view>
		<view  style="font-size: 24rpx;text-align: center;color: #999999;padding: 0 60rpx;">{{statusDsc}}</view>
<!-- 		<view class="pay_success_btn" v-if="status==1">
			<button class='but' @click="goMoney">查看储值中心</button>
		</view> -->
		<view v-if="status==0" class="pay_lose_btn" >
			<button class='but active' @click="goMoney">不提现了,去逛逛</button>
			<button class='but' @click="goBack">返回重新提现</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				status: '',
				statusSrc: '',
				statusTitle: '',
				statusDsc: '',
			}
		},
		onLoad(options) {
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#F5F5F5',
			});
			this.status = options.status
			this.statusSrc = options.status==1?'/static/images/pay_sucess.png':'/static/images/pay_lose.png';
			this.statusTitle = options.status==1?'提现申请成功':'提现申请失败';
			this.statusDsc = options.status==1?`提现¥${options.price}至余额已申请成功，正在等待处理。预计7个工作日到账`:`提现¥${options.price}至余额已申请失败，请重新申请`;
		},
		methods: {
			goMoney() {
				uni.navigateTo({
					url: '/pages/users/user_money/index'
				})
			},
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style scoped lang="scss">
	.pay_success_btn{
		margin-top: 10vh;
		.but {
			// color: #fff;
			font-size: 30rpx;
			width: 80vw;
			height: 86rpx;
			border-radius: 20rpx;
			margin: 0 auto;
			background-color: #BDFD5B;
			line-height: 86rpx;
		}
	}
	.pay_lose_btn{
		padding: 0 24rpx;margin-top: 10vh;display: flex;
		.but {
			// color: #fff;
			font-size: 30rpx;
			width: 46%;
			height: 86rpx;
			border-radius: 20rpx;
			margin: 0 auto;
			background-color: #BDFD5B;
			line-height: 86rpx;
		}
		.active{
			color: #999999;
			background-color: transparent;
			border: 1px solid #999999;
		}
	}
</style>