<template>
	<view>
		<view class="integral-glasses-window" :class="isVisible ? 'show' : ''">
			<!-- 弹框头部 -->
			<view class="popup-header">
				<view class="popup-title">选择规格</view>
				<view class="close-btn" @click="closeWindow">
					<text class="iconfont icon-guanbi"></text>
				</view>
			</view>

			<!-- 商品信息区域 -->
			<view class="product-info-section">
				<view class="product-image">
					<image :src="showLensInfo && currentLensImage ? currentLensImage : (selectedSku.image || productInfo.image)"></image>
				</view>
				<view class="product-details">
					<!-- 积分价格信息 -->
					<view class="price-info">
						<view class="integral-price">
							<text class="price-number">{{ getTotalIntegralPrice() }}</text>
							<text class="price-unit">积分</text>
						</view>
						<view class="price-note">
							统一价格：{{ productInfo.integralPrice }}积分
						</view>
					</view>
					<!-- 商品名称 -->
					<view class="product-name">
						{{ showLensInfo && currentLensName ? currentLensName : productInfo.name }}
					</view>
					<!-- 库存信息 -->
					<view class="stock-info">
						库存：{{ selectedSku.stock || 0 }}件
					</view>
				</view>
			</view>

			<!-- 规格选择区域 -->
			<view class="spec-selection-area">
				<!-- 镜框颜色选择 -->
				<view class="spec-item" v-for="(attr, index) in productAttr" :key="index">
					<view class="spec-title">{{ attr.attrName }}</view>
					<view class="spec-options">
						<view 
							class="spec-option"
							:class="selectedAttrs[attr.attrName] === option ? 'selected' : ''"
							v-for="option in attr.attrValues"
							:key="option"
							@click="selectAttr(attr.attrName, option)"
						>
							{{ option }}
						</view>
					</view>
				</view>

				<!-- 验光单选择 -->
				<view class="optometry-section">
					<view class="optometry-title">验光单</view>
					<view class="optometry-options">
						<view class="optometry-option" :class="isFlatLight ? 'selected' : ''" @click="selectFlatLight">
							选择平镜
						</view>
						<view class="optometry-option" :class="optometry ? 'selected' : ''" @click="goOptometryList">
							选择验光单
							<text class="iconfont icon-jiantou" style="font-size: 22rpx;"></text>
						</view>
					</view>
					<view v-if="optometry" class="selected-optometry">
						<view class="optometry-name">{{ optometry.name }}</view>
					</view>
				</view>

				<!-- 选择镜片 -->
				<view class="lens-section">
					<view class="lens-title">选择镜片</view>
					<view class="lens-list" v-if="!loading">
						<view class="lens-item"
							:class="index === selectedLensIndex ? 'selected' : ''"
							v-for="(item, index) in lensList"
							:key="index"
							@click="selectLens(index)">
							{{ item.name }}
						</view>
						<view v-if="lensList.length === 0" class="no-lens">暂无可选镜片～</view>
					</view>

					<view v-if="!loading && selectedLensIndex >= 0" class="lens-attr">
						<view class="attr-title">{{ lensList[selectedLensIndex].attrTitle }}</view>
						<view class="attr-options">
							<view class="attr-option"
								:class="aindex === selectedLensAttrIndex ? 'selected' : ''"
								v-for="(aitem, aindex) in lensList[selectedLensIndex].attrValueList"
								:key="aindex"
								@click="selectLensAttr(aindex)">
								{{ aitem.sku }}
							</view>
						</view>
					</view>

					<view v-if="loading" class="loading">
						<text class="iconfont icon-jiazai"></text>
					</view>
				</view>

				<!-- 数量选择 -->
				<view class="quantity-section">
					<view class="quantity-title">数量</view>
					<view class="quantity-controls">
						<view 
							class="quantity-btn decrease"
							:class="quantity <= 1 ? 'disabled' : ''"
							@click="decreaseQuantity"
						>
							-
						</view>
						<input 
							class="quantity-input"
							type="number"
							v-model="quantity"
							@input="onQuantityInput"
							min="1"
							:max="selectedSku.stock || 999"
						/>
						<view 
							class="quantity-btn increase"
							:class="quantity >= (selectedSku.stock || 999) ? 'disabled' : ''"
							@click="increaseQuantity"
						>
							+
						</view>
					</view>
				</view>
			</view>

			<!-- 底部按钮 -->
			<view class="bottom-buttons">
				<view class="confirm-btn" @click="confirmSelection">
					确定兑换
				</view>
			</view>
		</view>
		<view class="mask" v-if="isVisible" @click="closeWindow"></view>
	</view>
</template>

<script>
import { getLens } from '@/api/product.js';

export default {
	props: {
		isVisible: {
			type: Boolean,
			default: false
		},
		productInfo: {
			type: Object,
			default: () => ({})
		},
		productValue: {
			type: Object,
			default: () => ({})
		},
		productAttr: {
			type: Array,
			default: () => []
		},
		merId: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			selectedAttrs: {}, // 选中的属性
			quantity: 1,
			selectedSku: {}, // 当前选中的SKU
			// 验光单相关
			isFlatLight: true,
			optometry: null,
			maxS: 0,
			maxC: 0,
			// 镜片相关
			lensList: [],
			selectedLensIndex: -1,
			selectedLensAttrIndex: -1,
			currentLensPrice: 0,
			currentLensName: '',
			currentLensImage: '',
			showLensInfo: false,
			loading: false
		};
	},
	watch: {
		productAttr: {
			handler(newAttrs) {
				if (newAttrs && newAttrs.length > 0) {
					this.initializeSelection();
				}
			},
			immediate: true
		},
		selectedAttrs: {
			handler() {
				this.updateSelectedSku();
			},
			deep: true
		},
		isVisible: {
			handler(newVal) {
				if (newVal) {
					// 弹窗打开时初始化
					this.selectFlatLight();
					// 确保初始化规格选择
					this.$nextTick(() => {
						if (this.productAttr && this.productAttr.length > 0) {
							this.initializeSelection();
						}
					});
				}
			}
		}
	},
	mounted() {
		uni.$on('checkoptometry', this.checkOptometry);
	},
	beforeDestroy() {
		uni.$off('checkoptometry', this.checkOptometry);
	},
	methods: {
		// 初始化选择
		initializeSelection() {
			const attrs = {};
			this.productAttr.forEach(attr => {
				if (attr.attrValues && attr.attrValues.length > 0) {
					// 默认选中第一个选项
					attrs[attr.attrName] = attr.attrValues[0];
				}
			});
			this.selectedAttrs = attrs;
			// 确保更新选中的SKU
			this.$nextTick(() => {
				this.updateSelectedSku();
			});
		},

		// 选择属性
		selectAttr(attrName, option) {
			this.$set(this.selectedAttrs, attrName, option);
		},

		// 更新选中的SKU
		updateSelectedSku() {
			if (!this.productValue || Object.keys(this.selectedAttrs).length === 0) {
				this.selectedSku = {};
				return;
			}

			// 根据选中的属性找到对应的SKU
			let foundSku = null;
			for (let sku in this.productValue) {
				const skuData = this.productValue[sku];
				if (skuData.attrValue) {
					try {
						const attrValue = JSON.parse(skuData.attrValue);
						let match = true;
						
						for (let attrName in this.selectedAttrs) {
							if (attrValue[attrName] !== this.selectedAttrs[attrName]) {
								match = false;
								break;
							}
						}
						
						if (match) {
							foundSku = skuData;
							break;
						}
					} catch (e) {
						console.error('解析SKU属性失败:', e);
					}
				}
			}

			if (foundSku) {
				this.selectedSku = foundSku;
				// 如果当前数量超过库存，重置为库存数量
				if (this.quantity > foundSku.stock) {
					this.quantity = Math.max(1, foundSku.stock);
				}
			} else {
				// 如果没有找到匹配的SKU，清空选中的SKU
				this.selectedSku = {};
				console.warn('未找到匹配的SKU，当前选中属性:', this.selectedAttrs);
			}
		},

		// 选择平镜
		selectFlatLight() {
			this.isFlatLight = true;
			this.optometry = null;
			this.maxS = 0;
			this.maxC = 0;
			this.selectedLensIndex = -1;
			this.selectedLensAttrIndex = -1;
			this.currentLensPrice = 0;
			this.currentLensName = '';
			this.currentLensImage = '';
			this.showLensInfo = false;
			this.getLensList();
		},

		// 前往验光单列表
		goOptometryList() {
			uni.navigateTo({
				url: '/pages/optometry/user_optometry_list/index?isCheck=true'
			});
		},

		// 选择验光单
		checkOptometry(optometry) {
			if (optometry && optometry.url) {
				let imgstart = optometry.url.indexOf("crmebimage");
				if (imgstart > 0) {
					optometry.url = optometry.url.substring(imgstart);
				}
			}
			this.optometry = optometry;
			this.isFlatLight = false;
			this.selectedLensIndex = -1;
			this.selectedLensAttrIndex = -1;
			this.currentLensPrice = 0;
			this.currentLensName = '';
			this.currentLensImage = '';
			this.showLensInfo = false;
			this.checkMaxSC();
			if (this.lensList.length === 0) {
				this.getLensList();
			}
		},

		// 检查最大S和C值
		checkMaxSC() {
			if (!this.optometry) {
				this.maxS = 0;
				this.maxC = 0;
				this.lensList = [];
				return;
			}

			let dLS = this.optometry.data.LS;
			let dRS = this.optometry.data.RS;
			let maxS = 0;
			if (dLS < 0 || dRS < 0) {
				if (dLS < maxS) maxS = dLS;
				if (dRS < maxS) maxS = dRS;
			} else {
				if (dLS > maxS) maxS = dLS;
				if (dRS > maxS) maxS = dRS;
			}
			
			let dLC = this.optometry.data.LC;
			let dRC = this.optometry.data.RC;
			let maxC = 0;
			if (dLC < maxC) maxC = dLC;
			if (dRC < maxC) maxC = dRC;
			
			this.maxS = maxS;
			this.maxC = maxC;
			this.lensList = [];
		},

		// 获取镜片列表
		getLensList() {
			if (this.loading) return;
			this.loading = true;
			getLens({
				merId: this.merId,
				s: this.maxS,
				c: this.maxC
			}).then(res => {
				let list = res.data || [];
				list.forEach((product) => {
					if (product.attrValueList && product.attrValueList.length > 0) {
						let attrTitle = "";
						try {
							let attrValue = JSON.parse(product.attrValueList[0].attrValue);
							if (attrValue) {
								for (var key in attrValue) {
									attrTitle = key;
								}
							}
						} catch (e) {
							attrTitle = "规格";
						}
						product.attrTitle = attrTitle;
					}
				});
				this.$set(this, 'lensList', list);
				this.loading = false;
			}).catch(err => {
				this.loading = false;
			});
		},

		// 选择镜片
		selectLens(index) {
			this.selectedLensIndex = index;
			this.selectedLensAttrIndex = 0;
			this.updateLensInfo();
		},

		// 选择镜片属性
		selectLensAttr(aindex) {
			this.selectedLensAttrIndex = aindex;
			this.updateLensInfo();
		},

		// 更新镜片信息
		updateLensInfo() {
			if (this.selectedLensIndex >= 0 && this.selectedLensAttrIndex >= 0) {
				let lens = this.lensList[this.selectedLensIndex];
				let lensPrice = parseFloat(lens.attrValueList[this.selectedLensAttrIndex].price) || 0;

				this.currentLensPrice = lensPrice;
				this.currentLensName = lens.name + lens.attrValueList[this.selectedLensAttrIndex].sku;
				this.currentLensImage = lens.attrValueList[this.selectedLensAttrIndex].image;
				this.showLensInfo = true;
			} else {
				this.currentLensPrice = 0;
				this.currentLensName = '';
				this.currentLensImage = '';
				this.showLensInfo = false;
			}
		},

		// 获取总积分价格
		getTotalIntegralPrice() {
			return this.productInfo.integralPrice || 0;
		},

		// 减少数量
		decreaseQuantity() {
			if (this.quantity > 1) {
				this.quantity--;
			}
		},

		// 增加数量
		increaseQuantity() {
			const maxStock = this.selectedSku.stock || 999;
			if (this.quantity < maxStock) {
				this.quantity++;
			}
		},

		// 数量输入处理
		onQuantityInput(e) {
			let value = parseInt(e.detail.value) || 1;
			const maxStock = this.selectedSku.stock || 999;
			
			if (value < 1) value = 1;
			if (value > maxStock) value = maxStock;
			
			this.quantity = value;
		},

		// 确认选择
		confirmSelection() {
			// 检查是否选择了所有必需的属性
			const requiredAttrs = this.productAttr.length;
			const selectedAttrs = Object.keys(this.selectedAttrs).length;
			
			if (requiredAttrs > 0 && selectedAttrs < requiredAttrs) {
				uni.showToast({
					title: '请选择完整规格',
					icon: 'none'
				});
				return;
			}

			if (!this.selectedSku || !this.selectedSku.id) {
				uni.showToast({
					title: '请选择规格',
					icon: 'none'
				});
				return;
			}

			if (this.selectedSku.stock < this.quantity) {
				uni.showToast({
					title: '库存不足',
					icon: 'none'
				});
				return;
			}

			// 准备自定义数据
			let customData = {};
			if (!this.isFlatLight && this.optometry) {
				customData.optometry = this.optometry;
			}
			if (this.selectedLensIndex >= 0 && this.selectedLensAttrIndex >= 0) {
				let lens = this.lensList[this.selectedLensIndex];
				customData.lens = {
					name: lens.name + lens.attrValueList[this.selectedLensAttrIndex].sku,
					productId: lens.id,
					attrValueId: lens.attrValueList[this.selectedLensAttrIndex].id,
					num: 1
				};
				customData.price = this.currentLensPrice;
			}

			const selectionData = {
				sku: this.selectedSku,
				quantity: this.quantity,
				selectedAttrs: this.selectedAttrs,
				totalIntegral: this.getTotalIntegralPrice() * this.quantity,
				customData: customData
			};

			this.$emit('confirm', selectionData);
		},

		// 关闭弹窗
		closeWindow() {
			this.$emit('close');
		}
	}
};
</script>

<style lang="scss" scoped>
.integral-glasses-window {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	transform: translateY(100%);
	transition: transform 0.3s ease;
	z-index: 999;
	max-height: 80vh;
	overflow-y: auto;

	&.show {
		transform: translateY(0);
	}
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #222222;
	}

	.close-btn {
		padding: 10rpx;
		
		.iconfont {
			font-size: 32rpx;
			color: #999999;
		}
	}
}

.product-info-section {
	display: flex;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.product-image {
		width: 150rpx;
		height: 150rpx;
		margin-right: 20rpx;

		image {
			width: 100%;
			height: 100%;
			border-radius: 12rpx;
		}
	}

	.product-details {
		flex: 1;

		.price-info {
			margin-bottom: 16rpx;

			.integral-price {
				display: flex;
				align-items: baseline;

				.price-number {
					font-size: 36rpx;
					color: #ff2222;
					font-weight: bold;
				}

				.price-unit {
					font-size: 24rpx;
					color: #ff2222;
					margin-left: 8rpx;
				}
			}

			.price-note {
				font-size: 22rpx;
				color: #999999;
				margin-top: 8rpx;
			}
		}

		.product-name {
			font-size: 28rpx;
			color: #222222;
			line-height: 40rpx;
			margin-bottom: 12rpx;
		}

		.stock-info {
			font-size: 24rpx;
			color: #999999;
		}
	}
}

.spec-selection-area {
	padding: 30rpx;
}

.spec-item {
	margin-bottom: 40rpx;

	.spec-title {
		font-size: 28rpx;
		color: #222222;
		margin-bottom: 20rpx;
		font-weight: 500;
	}

	.spec-options {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;

		.spec-option {
			padding: 16rpx 32rpx;
			border: 2rpx solid #f0f0f0;
			border-radius: 8rpx;
			font-size: 26rpx;
			color: #666666;
			background: #f8f8f8;

			&.selected {
				border-color: #BDFD5B;
				background: #BDFD5B;
				color: #222222;
			}
		}
	}
}

.optometry-section {
	margin-bottom: 40rpx;

	.optometry-title {
		font-size: 28rpx;
		color: #222222;
		margin-bottom: 20rpx;
		font-weight: 500;
	}

	.optometry-options {
		display: flex;
		gap: 20rpx;
		margin-bottom: 20rpx;

		.optometry-option {
			padding: 16rpx 32rpx;
			border: 2rpx solid #f0f0f0;
			border-radius: 8rpx;
			font-size: 26rpx;
			color: #666666;
			background: #f8f8f8;
			display: flex;
			align-items: center;

			&.selected {
				border-color: #BDFD5B;
				background: #BDFD5B;
				color: #222222;
			}
		}
	}

	.selected-optometry {
		background: #f8f8f8;
		padding: 20rpx;
		border-radius: 8rpx;

		.optometry-name {
			font-size: 26rpx;
			color: #333333;
		}
	}
}

.lens-section {
	margin-bottom: 40rpx;

	.lens-title {
		font-size: 28rpx;
		color: #222222;
		margin-bottom: 20rpx;
		font-weight: 500;
	}

	.lens-list {
		margin-bottom: 20rpx;

		.lens-item {
			padding: 16rpx 32rpx;
			border: 2rpx solid #f0f0f0;
			border-radius: 8rpx;
			font-size: 26rpx;
			color: #666666;
			background: #f8f8f8;
			margin-bottom: 16rpx;

			&.selected {
				border-color: #BDFD5B;
				background: #BDFD5B;
				color: #222222;
			}
		}

		.no-lens {
			text-align: center;
			color: #999999;
			padding: 40rpx 0;
		}
	}

	.lens-attr {
		.attr-title {
			font-size: 26rpx;
			color: #333333;
			margin-bottom: 16rpx;
		}

		.attr-options {
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;

			.attr-option {
				padding: 12rpx 24rpx;
				border: 2rpx solid #f0f0f0;
				border-radius: 6rpx;
				font-size: 24rpx;
				color: #666666;
				background: #f8f8f8;

				&.selected {
					border-color: #BDFD5B;
					background: #BDFD5B;
					color: #222222;
				}
			}
		}
	}

	.loading {
		text-align: center;
		padding: 40rpx 0;
		color: #999999;
	}
}

.quantity-section {
	.quantity-title {
		font-size: 28rpx;
		color: #222222;
		margin-bottom: 20rpx;
		font-weight: 500;
	}

	.quantity-controls {
		display: flex;
		align-items: center;
		gap: 20rpx;

		.quantity-btn {
			width: 60rpx;
			height: 60rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			color: #666666;
			background: #ffffff;

			&.disabled {
				color: #cccccc;
				border-color: #f0f0f0;
			}
		}

		.quantity-input {
			width: 120rpx;
			height: 60rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 8rpx;
			text-align: center;
			font-size: 28rpx;
			background: #ffffff;
		}
	}
}

.bottom-buttons {
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;

	.confirm-btn {
		width: 100%;
		height: 80rpx;
		background: #BDFD5B;
		color: #222222;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 998;
}
</style> 