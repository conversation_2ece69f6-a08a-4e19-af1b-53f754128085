<template>
	<view class="empty-box">
		<image :src="src"></image>
		<view class="txt">{{title}}</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	export default{
		props: {
			title: {
				type: String,
				default: '暂无记录',
			},
			src: {
				type: String,
				default: "/pages/aastatictoT/static/images/noCoupon.png",
			}
		},
	}
	
</script>

<style lang="scss">
	.empty-box{
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 200rpx;
		padding-bottom: 60rpx;
		position: relative;

		image{
			width: 414rpx;
			height: 305rpx;
		}
		.txt{
			font-size: 26rpx;
			color: #999;
			position: relative;
			top: -40rpx;
		}
	}
</style>
