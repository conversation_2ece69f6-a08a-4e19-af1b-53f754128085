<template>
	<view>
		<view class="product-window" :class="(attr.cartAttr === true ? 'on' : '')">

			<!-- 弹框头部 -->
			<view class="popup-header">
				<view class="popup-title">规格选择</view>
				<view class="close-btn" @click="closeAttr">
					<text class="iconfont icon-guanbi"></text>
				</view>
			</view>

			<!-- 商品信息区域 -->
			<view class="product-info-section">
				<view class="product-image" @click="showImg()">
					<image :src="showLensName && curLenImage ? curLenImage : attr.productSelect.image"></image>
				</view>
				<view class="product-details">
					<!-- 价格信息 -->
					<view class="price-info">
						<view class="current-price">
							¥<text class="price-number">{{ getCurrentPrice() }}</text>
							<text class="price-tag">现价</text>
						</view>
						<view class="original-price" v-if="attr.productSelect.otPrice">
							¥{{ getOriginalPrice() }}
						</view>
					</view>
					<!-- 商品名称 -->
					<view class="product-name">
						{{ showLensName ? curLenName : attr.productSelect.name }}
					</view>
				</view>
			</view>

			<!-- 规格选择区域 -->
			<view class="spec-selection-area">
				<!-- 颜色选择 -->
				<view class="spec-item" v-for="(item, indexw) in attr.productAttr" :key="indexw">
					<view class="spec-title">{{ item.attrName }} ({{ getSelectedCount(item) }})</view>
					<view class="spec-options">
						<view class="spec-option"
							:class="item.index === itemn ? 'selected' : ''"
							v-for="(itemn, indexn) in item.attrValues"
							@click="checkAttr(indexw, indexn)"
							:key="indexn">
							{{ itemn }}
						</view>
					</view>
				</view>

				<!-- 验光单选择 -->
				<view class="optometry-section">
					<view class="optometry-title">验光单</view>
					<view class="optometry-options">
						<view class="optometry-option" :class="isFlatLight ? 'selected' : ''" @click="selectFlatLight">
							选择平镜
						</view>
						<view class="optometry-option" :class="optometry ? 'selected' : ''" @click="goOptometryList">
							选择验光单
							<text class="iconfont icon-jiantou" style="font-size: 22rpx;"></text>
						</view>
					</view>
					<view v-if="optometry" class="selected-optometry">
						<view class="optometry-name">{{ optometry.name }}</view>
						<view class="optometry-image" v-if="optometry.url">
							<text class="iconfont icon-tupian1"></text>
						</view>
					</view>
				</view>

				<!-- 选择镜片 -->
				<view class="lens-section">
					<view class="lens-title">选择镜片</view>
					<view class="lens-list" v-if="!loading">
						<view class="lens-item"
							:class="index === lensSelectedIndex ? 'selected' : ''"
							v-for="(item, index) in lensList"
							:key="index"
							@click="checkLens(index)">
							{{ item.name }}
						</view>
						<view v-if="lensList.length === 0" class="no-lens">暂无可选镜片～</view>
					</view>

					<view v-if="!loading && lensSelectedIndex >= 0" class="lens-attr">
						<view class="attr-title">{{ lensList[lensSelectedIndex].attrTitle }}</view>
						<view class="attr-options">
							<view class="attr-option"
								:class="aindex === lensAttrSelectedIndex ? 'selected' : ''"
								v-for="(aitem, aindex) in lensList[lensSelectedIndex].attrValueList"
								:key="aindex"
								@click="checkLensAttr(aindex)">
								{{ aitem.sku }}
							</view>
						</view>
					</view>

					<view v-if="loading" class="loading">
						<text class="iconfont icon-jiazai"></text>
					</view>
				</view>
			</view>

			<!-- 底部按钮 -->
			<view class="bottom-buttons">
				<view class="add-cart-btn" @click="joinCart">加入购物车</view>
				<view class="buy-now-btn" @click="goBuy">立即购买</view>
			</view>
		</view>
		<view class="mask" @touchmove.prevent :hidden="attr.cartAttr === false" @click="closeAttr"></view>
	</view>
</template>

<script>
	import {
		getLens
	} from '@/api/product.js';
	export default {
		props: {
			attr: {
				type: Object,
				default: () => {}
			},
			merId: {
				type: Number,
				value: 0
			},
		},
		data() {
			return {
				step: 0,
				isFlatLight: true,
				optometry: null,
				maxS: 0,
				maxC: 0,
				lensList: [],
				lensSelectedIndex: -1,
				lensAttrSelectedIndex: -1,
				curLenPrice: 0,
				curLenName: '',
				curLenImage: '',
				showLensName: false,
				loading: false,
				stepLensScrollIntoView: ''
			};
		},
		mounted() {
			uni.$on('checkoptometry', this.checkOptometry);
			// 初始化时默认选择平镜并加载镜片列表
			this.selectFlatLight();
		},
		beforeDestroy() {
			uni.$off('checkoptometry', this.checkOptometry);
		},
		methods: {
			closeAttr: function() {
				this.$emit('closeAttr');
			},

			checkAttr: function(indexw, indexn) {
				let that = this;
				that.$emit("attrVal", {
					indexw: indexw,
					indexn: indexn
				});
				this.$set(this.attr.productAttr[indexw], 'index', this.attr.productAttr[indexw].attrValues[indexn]);

				let value = that.getCheckedValue().join(",");
				that.$emit("changeAttr", value);
			},

			getCheckedValue: function() {
				let productAttr = this.attr.productAttr;
				let value = [];
				for (let i = 0; i < productAttr.length; i++) {
					for (let j = 0; j < productAttr[i].attrValues.length; j++) {
						if (productAttr[i].index === productAttr[i].attrValues[j]) {
							value.push(productAttr[i].attrValues[j]);
						}
					}
				}
				return value;
			},

			goOptometryList: function() {
				uni.navigateTo({
					url: '/pages/optometry/user_optometry_list/index?isCheck=true'
				});
			},

			// 选择平镜
			selectFlatLight: function() {
				this.isFlatLight = true;
				this.optometry = null;
				this.maxS = 0;
				this.maxC = 0;
				this.lensSelectedIndex = -1;
				this.lensAttrSelectedIndex = -1;
				this.getLensList();
				this.setCustomData();
			},
			
			checkOptometry: function(optometry) {
				if (optometry && optometry.url) {
					let imgstart = optometry.url.indexOf("crmebimage");
					if (imgstart > 0) {
						optometry.url = optometry.url.substring(imgstart);
					}
				}
				this.optometry = optometry;
				this.isFlatLight = false;
				this.lensSelectedIndex = -1;
				this.lensAttrSelectedIndex = -1;
				this.checkMaxSC();
				if (this.lensList.length === 0) {
					this.getLensList();
				}
				this.setCustomData();
			},
			
			checkMaxSC: function() {
				if (!this.optometry) {
					this.maxS = 0;
					this.maxC = 0;
					this.lensList = [];
					return
				}

				let dLS = this.optometry.data.LS;
				let dRS = this.optometry.data.RS;
				let maxS = 0
				if (dLS < 0 || dRS < 0) {
					if (dLS < maxS) maxS = dLS
					if (dRS < maxS) maxS = dRS
				} else {
					if (dLS > maxS) maxS = dLS
					if (dRS > maxS) maxS = dRS
				}
				
				let dLC = this.optometry.data.LC;
				let dRC = this.optometry.data.RC;
				let maxC = 0
				if (dLC < maxC) maxC = dLC
				if (dRC < maxC) maxC = dRC
				
				this.maxS = maxS;
				this.maxC = maxC;
				this.lensList = [];
			},
			
			getLensList: function() {
				if (this.loading) return
				this.loading = true;
				getLens({
					merId: this.merId,
					s: this.maxS,
					c: this.maxC
				}).then(res => {
					let list = res.data || [];
					list.forEach((product) => {
						if (product.attrValueList.length > 0) {
							let attrTitle = "";
							try {
								let attrValue = JSON.parse(product.attrValueList[0].attrValue);
								if (attrValue) {
									for (var key in attrValue) {
										attrTitle = key;
									}
								}
							} catch (e) {
								attrTitle = "规格";
							}
							product.attrTitle = attrTitle;
						}
					});
					this.$set(this, 'lensList', list);
					this.loading = false;
				}).catch(err => {
					this.loading = false;
				});
			},
			
			lensScroll() {
				this.stepLensScrollIntoView = "";
			},
			
			checkLens: function(index) {
				this.lensSelectedIndex = index;
				this.lensAttrSelectedIndex = 0;
				this.setCustomData();
				
				this.stepLensScrollIntoView = "stepLensAttr";
			},
			
			checkLensAttr: function(aindex) {
				this.lensAttrSelectedIndex = aindex;
				this.setCustomData();
			},
			
			setCustomData: function() {
				let customData = {};
				if (!this.isFlatLight && this.optometry) {
					customData.optometry = this.optometry;
				}
				if (this.lensSelectedIndex >= 0 && this.lensAttrSelectedIndex >= 0) {
					let lens = this.lensList[this.lensSelectedIndex];
					let lensPrice = parseFloat(lens.attrValueList[this.lensAttrSelectedIndex].price) || 0;

					customData.lens = {
						name: lens.name + lens.attrValueList[this.lensAttrSelectedIndex].sku,
						productId: lens.id,
						attrValueId: lens.attrValueList[this.lensAttrSelectedIndex].id,
						num: 1
					}
					customData.price = lensPrice;

					// 设置当前镜片价格（不累加，直接替换）
					this.curLenPrice = lensPrice;
					this.curLenName = lens.name;
					this.curLenImage = lens.attrValueList[this.lensAttrSelectedIndex].image;
					this.showLensName = false;
				} else {
					// 没有选择镜片时，重置镜片相关数据
					this.curLenPrice = 0;
					this.curLenName = '';
					this.curLenImage = '';
				}
				this.$emit('setCustomData', customData);
			},
			
			setShowLensName() {
				this.showLensName = true;
			},
			setHideLensName() {
				this.showLensName = false;
			},
			
			stepPre() {
				if (this.step > 0) {
					this.step -= 1;
				}
				if (this.step == 1) {
					this.lensSelectedIndex = -1;
					this.lensAttrSelectedIndex = -1;
				}
				this.setCustomData();
			},

			stepNext(flatLight) {
				if (this.step == 1) {
					this.isFlatLight = flatLight;
					if (!this.isFlatLight && !this.optometry) {
						return this.$util.Tips({
							title: "请先选择您的验光单"
						}); 
					}

					this.lensSelectedIndex = -1;
					this.lensAttrSelectedIndex = -1;
					if (this.isFlatLight) {
						this.maxS = 0;
						this.maxC = 0;
						this.lensList = [];
					} else {
						this.checkMaxSC();
					}
					
					if (this.lensList.length === 0) {
						this.getLensList();
					}
				}
				this.setCustomData();
				
				if (this.step < 2) {
					this.step += 1;
				}
			},

			showImg() {
				this.$emit('getImg');
			},

			goOptometryDetails(optometryId) {
				if(optometryId){
					uni.navigateTo({
						url: '/pages/optometry/user_optometry_details/index?id=' + optometryId
					})
				}
			},
			
			joinCart() {
				this.$emit('joinCart');
			},
			goBuy() {
				this.$emit('goBuy');
			},
			// 获取已选择的规格数量
			getSelectedCount(item) {
				return item.index ? 1 : 0;
			},

			// 获取当前价格（基础价格 + 镜片价格）
			getCurrentPrice() {
				const basePrice = parseFloat(this.attr.productSelect.price) || 0;
				const lensPrice = parseFloat(this.curLenPrice) || 0;
				return (basePrice + lensPrice).toFixed(2);
			},

			// 获取原价（如果有镜片，原价也要加上镜片价格）
			getOriginalPrice() {
				const baseOriginalPrice = parseFloat(this.attr.productSelect.otPrice) || 0;
				const lensPrice = parseFloat(this.curLenPrice) || 0;
				return (baseOriginalPrice + lensPrice).toFixed(2);
			}
		}
	}
</script>

<style scoped lang="scss">
	.product-window {
		position: fixed;
		bottom: 0;
		width: 100%;
		left: 0;
		background-color: #fff;
		z-index: 377;
		border-radius: 24rpx 24rpx 0 0;
		padding-bottom: 40rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 40rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
		transform: translate3d(0, 100%, 0);
		transition: all .2s cubic-bezier(0, 0, .25, 1);
		max-height: 80vh;
		overflow: hidden;
	}

	.product-window.on {
		transform: translate3d(0, 0, 0);
	}

	// 弹框头部
	.popup-header {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 32rpx 40rpx 24rpx;
		position: relative;

		.popup-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333333;
		}

		.close-btn {
			position: absolute;
			right: 40rpx;
			top: 32rpx;
			width: 48rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.iconfont {
				font-size: 32rpx;
				color: #999999;
			}
		}
	}

	// 商品信息区域
	.product-info-section {
		display: flex;
		padding: 32rpx 40rpx;

		.product-image {
			width: 160rpx;
			height: 160rpx;
			margin-right: 24rpx;
			flex-shrink: 0;
			background-color: #f5f5f5;
			border-radius: 12rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}
		}

		.product-details {
			flex: 1;

			.price-info {
				margin-bottom: 16rpx;

				.current-price {
					color: #FF2222;
					font-size: 28rpx;
					display: flex;
					align-items: baseline;
					margin-bottom: 8rpx;

					.price-number {
						font-size: 48rpx;
						font-weight: 600;
					}

					.price-tag {
						font-size: 24rpx;
						background-color: #FF2222;
						color: #fff;
						padding: 2rpx 8rpx;
						border-radius: 4rpx;
						margin-left: 8rpx;
					}
				}

				.original-price {
					color: #999999;
					font-size: 24rpx;
					text-decoration: line-through;
				}
			}

			.product-name {
				font-size: 28rpx;
				color: #333333;
				line-height: 1.4;
				word-break: break-all;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
			}
		}
	}

	// 规格选择区域
	.spec-selection-area {
		max-height: 50vh;
		overflow-y: auto;
		padding: 0 40rpx;

		// 规格选择项
		.spec-item {
			margin-bottom: 40rpx;

			.spec-title {
				font-size: 28rpx;
				color: #333333;
				margin-bottom: 24rpx;
				font-weight: 500;
			}

			.spec-options {
				display: flex;
				flex-wrap: wrap;
				gap: 16rpx;

				.spec-option {
					padding: 16rpx 32rpx;
					border: 2rpx solid #f0f0f0;
					border-radius: 8rpx;
					font-size: 26rpx;
					color: #333333;
					background-color: #f8f8f8;
					transition: all 0.2s;

					&.selected {
						background-color: #BDFD5B;
						border-color: #BDFD5B;
						color: #222222;
					}
				}
			}
		}

		// 验光单选择
		.optometry-section {
			margin-bottom: 40rpx;

			.optometry-title {
				font-size: 28rpx;
				color: #333333;
				margin-bottom: 24rpx;
				font-weight: 500;
			}

			.optometry-options {
				display: flex;
				gap: 16rpx;

				.optometry-option {
					flex: 1;
					padding: 24rpx;
					border: 2rpx solid #f0f0f0;
					border-radius: 8rpx;
					font-size: 26rpx;
					color: #333333;
					background-color: #f8f8f8;
					text-align: center;
					position: relative;
					transition: all 0.2s;

					&.selected {
						background-color: #BDFD5B;
						border-color: #BDFD5B;
						color: #222222;
					}

					.arrow-icon {
						position: absolute;
						right: 16rpx;
						top: 50%;
						transform: translateY(-50%);
						color: #999999;
					}
				}
			}

			.selected-optometry {
				margin-top: 16rpx;
				padding: 16rpx;
				background-color: #f8f8f8;
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.optometry-name {
					font-size: 26rpx;
					color: #333333;
				}

				.optometry-image {
					color: #9AFF9A;
					font-size: 32rpx;
				}
			}
		}
	}
		// 镜片选择
		.lens-section {
			margin-bottom: 40rpx;

			.lens-title {
				font-size: 28rpx;
				color: #222222;
				margin-bottom: 24rpx;
				font-weight: 500;
			}

			.lens-list {
				margin-bottom: 24rpx;

				.lens-item {
					display: inline-block;
					padding: 16rpx 32rpx;
					border: 2rpx solid #f0f0f0;
					border-radius: 8rpx;
					font-size: 26rpx;
					color: #666666;
					background-color: #f8f8f8;
					margin: 0 16rpx 16rpx 0;
					transition: all 0.2s;

					&.selected {
						background-color: #BDFD5B;
						border-color: #BDFD5B;
						color: #222222;
					}
				}

				.no-lens {
					text-align: center;
					color: #999999;
					font-size: 26rpx;
					padding: 40rpx 0;
				}
			}

			.lens-attr {
				.attr-title {
					font-size: 28rpx;
					color: #333333;
					margin-bottom: 24rpx;
					font-weight: 500;
				}

				.attr-options {
					display: flex;
					flex-wrap: wrap;
					gap: 16rpx;

					.attr-option {
						padding: 16rpx 32rpx;
						border: 2rpx solid #f0f0f0;
						border-radius: 8rpx;
						font-size: 26rpx;
						color: #333333;
						background-color: #f8f8f8;
						transition: all 0.2s;

						&.selected {
							background-color: #BDFD5B;
							border-color: #BDFD5B;
							color: #222222;
						}
					}
				}
			}

			.loading {
				text-align: center;
				padding: 40rpx 0;
				color: #999999;
				font-size: 32rpx;
				animation: rotate 1s linear infinite;
			}
		}



	// 底部按钮
	.bottom-buttons {
		padding: 32rpx 40rpx 0;
		display: flex;
		gap: 24rpx;

		.add-cart-btn {
			flex: 1;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			font-size: 28rpx;
			color: #FFFFFF;
			background-color: #FF8125;
			border: 2rpx solid #FF8125;
			border-radius: 25rpx;
			transition: all 0.2s;

			&:active {
				background-color: #fff5f0;
			}
		}

		.buy-now-btn {
			flex: 1;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			font-size: 28rpx;
			color: #222222;
			background-color: #BDFD5B;
			border-radius: 25rpx;
			transition: all 0.2s;

			&:active {
				background-color: #8AEF8A;
			}
		}
	}

	// 遮罩层
	.mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 376;
	}

</style>
