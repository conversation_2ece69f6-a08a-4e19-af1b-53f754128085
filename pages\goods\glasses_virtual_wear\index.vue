<template>
	<view :data-theme="theme">
		<canvas :style="{width: canvasWidth + 'px', height: canvasHeight + 'px', top: (-canvasHeight - 10) + 'px'}" canvas-id="photo" id="photo"></canvas>
		<view class="image-container">
			<image class="canvas-img" mode="aspectFit" :src="canvasImg" @click="preview"></image>
			<view class="tip-text">正面照效果更好哦</view>
		</view>
		<view class="glasses-section">
				<scroll-view class="glasses-list" scroll-x="true">
					<view v-for="(item, index) in skuList" class="item" :class="curSkuIndex == index && hasGlassesSelected ? 'on' : ''" @click="chooseSku(index)">
						<image :class="item.loaded ? 'loaded' : 'loading'" @load="glassImgLoad(index)" :key="index" :src="item.wearImage"></image>
						<view class="sku">{{item.sku}}</view>
					</view>
				</scroll-view>
			</view>
		<view class="footer">

			<view class="btn-section">
				<view class="left-controls">
					<view class="picker-btn">
						<picker mode="selector" @change="bindPDChange" :value="PD_index" :range="PD_list">
							<view class="picker-text">{{PD_list[PD_index]}}</view>
						</picker>
						<view class="picker-arrow">></view>
					</view>
				</view>
				<view class="photo-choose" @click="choosePhoto">
					<view class="iconfont icon-xiangji xiangji"></view>
				</view>
				<view class="buy" @click="goBuy">立即购买</view>
			</view>
		</view>
		<view class="photo-choose-ways" :class="photoChoosing ? 'on' : ''">
			<view class="button" @click="choosePhotoFromCamera">拍照</view>
			<view class="button" @click="choosePhotoFromAlbum">从手相册选择</view>
			<view class="button" @click="choosePhotoFromHistory">从试戴记录选择</view>
			<view class="cancel" @click="choosePhotoWaysClose">取消</view>
		</view>
		<view class="mask" v-if="photoChoosing" @click="choosePhotoWaysClose"></view>
	</view>
</template>

<script>
	import {
		mapGetters
	} from "vuex";
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		base64src
	} from '@/utils/base64src.js'
	import {
		mpQrcode,
	} from '@/api/api.js'
	import {
		getVirtualWearModel,
		virtualWear
	} from '@/api/user.js';
	let app = getApp();
	export default {
		computed: mapGetters(['isLogin', 'uid']),
		data() {
			return {
				theme: app.globalData.theme,
				
				productId: 0,
				skuList: [],
				curSkuIndex: -1, // 修改：默认不选择任何眼镜
				promotionCode: null,
				
				pixelRatio: 1,
				windowWidth: 0,
				windowHeight: 0,
				statusBarHeight: 0,
				
				canvasContext: null,
				canvasWidth: 0,
				canvasHeight: 0,
				scaleRatio: 1,
				
				canvasImgWidth: 0,
				canvasImgHeight: 0,
				canvasImg: null,

				photoUrl: null,
				photoTempFilePath: null,
				photoAnalyzeFace: null,

				previewFilePath: null,
				photoChoosing: false,
				PD_index: 0,
				PD_list: ["请选择瞳距"],

				// 面部识别显示控制 - 改为直接控制，不需要按钮
				showFaceDetection: false,
				
				// 添加人脸图片叠加功能
				showFaceImageOverlay: true,
				isDefaultModel: true,
				particularsImagePath: 'https://image.letengshengtai.com/ooseekimage/public/product/2025/07/29/9bd25b8f6fac458cbb48d406040847fe8t1wddhlt9.png',

				// 添加眼镜选择状态控制
				hasGlassesSelected: false, // 是否已选择眼镜
				
			}
		},
		onLoad(options) {
			// console.log(options)
			this.productId = options.id ? options.id : 0;
			this.merId = options.mid || ''
			this.skuList = options.skuList ? JSON.parse(options.skuList) : [];
			this.skuList.forEach((item) => {
				item.loaded = false;
				if (item.spec) {
					let values = item.spec.split('-');
					if (values.length >= 4) {
						item.gw = parseInt(values[3]);
					}
				}
			});

			let winfo = uni.getWindowInfo();
			this.pixelRatio = winfo.pixelRatio;
			this.windowWidth = winfo.windowWidth;
			this.windowHeight = winfo.windowHeight - winfo.statusBarHeight;
			this.statusBarHeight = winfo.statusBarHeight;
			this.canvasWidth = this.windowWidth;
			this.canvasHeight = this.windowHeight;

			this.canvasContext = uni.createCanvasContext('photo');
			this.canvasContext.save();
			
			let PD_value_list = [],
				cur_PD = 50;
			while (cur_PD <= 80) {
				PD_value_list.push(cur_PD)
				cur_PD += 1
			}
			this.PD_list = this.PD_list.concat(PD_value_list);
			
			uni.$on('virtualWearFromHistory', this.virtualWearFromHistory);
			
			if (this.promotionCode == null) {
				uni.showLoading({
					title: '正在加载中',
					mask: true
				});
				
				this.getQrcode();
			}
			this.getVirtualWearModel();
		},
		
		onUnload() {
			uni.$off('virtualWearFromHistory', this.virtualWearFromHistory);
		},
		
		onShow() {
			if (this.isLogin === false) {
				toLogin();
			}
		},

		methods: {
			glassImgLoad(index) {
				this.skuList[index].loaded = true;
				this.$set(this.skuList, index, this.skuList[index]);
			},
			
			bindPDChange: function(e) {
				this.PD_index = e.detail.value;
				this.dress();
			},
			
			chooseSku(index) {
				this.$set(this, 'curSkuIndex', index);
				this.$set(this, 'hasGlassesSelected', true); // 标记已选择眼镜
				this.dress();
			},

			preview() {
				if (this.canvasImg != null) {
					uni.previewImage({
						urls: [this.canvasImg],
						current: this.canvasImg
					});
				}
			},
			
			getVirtualWearModel() {
				getVirtualWearModel().then(res => {
					if (res && res.code == 200 && res.data) {
						try {
							let model = res.data[0];
							if (model.image && model.data) {
								this.isDefaultModel = true;
								this.photoUrl = model.image;
								this.photoAnalyzeFace = JSON.parse(model.data);
								
								uni.downloadFile({
									url: this.photoUrl,
									success: (res) => {
										this.photoTempFilePath = res.tempFilePath;
										// if (this.promotionCode != null) {
										// 	this.dress();
										// }
										this.dress();
									},
									fail: () => {
										uni.hideLoading();
									}
								});
							}
						} catch(err) {
							console.log(err);
							uni.hideLoading();
						}
					} else {
						uni.hideLoading();
					}
				}).catch(() => {
					uni.hideLoading();
				});
			},

			getQrcode() {
				let data = {
					pid: this.uid,
					id: this.productId,
					mid: this.merId,
					path: 'pages/goods/goods_details/index'
				}
				mpQrcode(data).then(res => {
					base64src(res.data.code, Date.now(), res => {
						this.promotionCode = res;
						if (this.photoTempFilePath != null && this.photoAnalyzeFace != null) {
							this.dress();
						}
					});
				}).catch(err => {
					this.promotionCode = null;
					uni.hideLoading();
				});
			},
			
			choosePhoto() {
				this.$set(this, 'photoChoosing', true);
			},
			
			choosePhotoWaysClose() {
				this.$set(this, 'photoChoosing', false);
			},
			
			choosePhotoFromHistory(){
				this.choosePhotoWaysClose();
				uni.navigateTo({
					url: '/pages/goods/virtual_wear_list/index'
				});
			},
			
			virtualWearFromHistory(result) {
				this.photoUrl = result.image;
				this.photoAnalyzeFace = result.data;
				this.isDefaultModel = false;

				// 重置眼镜选择状态，确保显示人脸图片叠加
				this.$set(this, 'curSkuIndex', -1);
				this.$set(this, 'hasGlassesSelected', false);
				this.$set(this, 'showFaceImageOverlay', true);
				
				if(this.photoUrl != null) {
					uni.showLoading({
						title: '正在加载照片信息',
						mask: true
					});
					uni.downloadFile({
						url: this.photoUrl,
						success: (res) => {
							this.photoTempFilePath = res.tempFilePath;
							setTimeout(() => {
								this.dress();
							}, 1000);
						},
						fail: () => {
							uni.hideLoading();
							this.$util.Tips({
								title: '网络加载失败'
							});
						}
					});
				}
			},
			
			choosePhotoFromCamera() {
				this.choosePhotoWaysClose();
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['camera'],
					success: res => {
						uni.showLoading({
							title: '正在获取照片信息',
							mask: true
						});
						this.compressPhoto(res.tempFilePaths[0]);
					}
				});
			},
			
			choosePhotoFromAlbum() {
				this.choosePhotoWaysClose();
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album'],
					success: res => {
						uni.showLoading({
							title: '正在获取照片信息',
							mask: true
						});
						this.compressPhoto(res.tempFilePaths[0]);
					}
				});
			},
			
			compressPhoto(originalImagePath) {
				uni.showLoading({
					title: '正在获取照片信息',
					mask: true
				});
				uni.getImageInfo({
					src: originalImagePath,
					success: (imgInfo) => {
						uni.showLoading({
							title: '正在压缩照片',
							mask: true
						});
						
						let imageWidth = imgInfo.width;
						let imageHeight = imgInfo.height;
						let originalSize = {width: imageWidth, height: imageHeight};

						let canvasFrameSize = {width: 1800 / this.pixelRatio, height: 1800 / this.pixelRatio};
						let canvasSize = this.getScaleSize(originalSize, canvasFrameSize);
						this.$set(this, 'canvasWidth', canvasSize.width);
						this.$set(this, 'canvasHeight', canvasSize.height);
						// console.log(originalSize);
						// console.log(canvasFrameSize);
						// console.log(canvasSize);
						
						this.canvasContext.restore();
						this.canvasContext.drawImage(originalImagePath, 0, 0, this.canvasWidth, this.canvasHeight);
						this.canvasContext.draw();
						
						setTimeout(() => {
							uni.canvasToTempFilePath({
								x: 0,
								y: 0,
								canvasId: 'photo',
								fileType: 'jpg',
								success: (cres) => {
									this.upLoad(cres.tempFilePath);
								},
								fail: () => {
									uni.hideLoading();
									this.$util.Tips({
										title: '照片压缩失败'
									});
								}
							});
						}, 500)
					},
					fail: () => {
						uni.hideLoading();
						this.$util.Tips({
							title: '照片信息获取失败'
						});
					}
				});
				
			},
			
			upLoad(tempFilePath) {
				this.photoTempFilePath = null;
				this.photoUrl = null;
				this.photoAnalyzeFace = null;
				this.previewFilePath = null;
				this.isDefaultModel = false;

				// 重置眼镜选择状态，确保显示人脸图片叠加
				this.$set(this, 'curSkuIndex', -1);
				this.$set(this, 'hasGlassesSelected', false);
				this.$set(this, 'showFaceImageOverlay', true);

				this.photoTempFilePath = tempFilePath;
				uni.showLoading({
					title: '正在读取照片',
					mask: true
				});
				wx.getFileSystemManager().readFile({
					filePath: this.photoTempFilePath,
					encoding: 'base64',
					success: rres => {
						this.faceDetect(rres.data);
					},
					fail: () => {
						uni.hideLoading();
						this.$util.Tips({
							title: '照片读取失败'
						});
					}
				})
			},

			faceDetect(photoBase64) {
				uni.showLoading({
					title: '正在分析照片',
					mask: true
				});

				let formData = {
					whitening: 0,		// 美白程度，取值范围[0,100]。0不美白，100代表最高程度。默认值30。
					smoothing: 0,		// 磨皮程度，取值范围[0,100]。0不磨皮，100代表最高程度。默认值10。
					faceLifting: 0,		// 瘦脸程度，取值范围[0,100]。0不瘦脸，100代表最高程度。默认值70。
					eyeEnlarging: 0,	// 大眼程度，取值范围[0,100]。0不大眼，100代表最高程度。默认值70。
					image:  "data:image/jpeg;base64," + photoBase64
				};
				virtualWear(formData).then(res => {
					if (res && res.code == 200 && res.data) {
						this.photoUrl = res.data.image;
						this.photoAnalyzeFace = JSON.parse(res.data.data);
						
						if(this.photoUrl != null) {
							uni.downloadFile({
								url: this.photoUrl,
								success: (res) => {
									this.photoTempFilePath = res.tempFilePath;
									this.dress();
								},
								fail: () => {
									uni.hideLoading();
									this.$util.Tips({
										title: '网络加载失败'
									});
								}
							});
						} else {
							this.dress();
						}
					} else {
						uni.hideLoading();
						this.$util.Tips({
							title: '照片分析失败'
						});
					}
				}).catch(() => {
					uni.hideLoading();
					this.$util.Tips({
						title: '网络加载失败'
					});
				});
			},
			
			dress() {
				if (this.photoTempFilePath == null || this.photoAnalyzeFace == null) return;

				uni.showLoading({
					title: '正在生成效果图',
					mask: true
				});
				
				// 添加调试信息
				console.log('面部数据:', this.photoAnalyzeFace);
				if (this.photoAnalyzeFace.faceShapeSet && this.photoAnalyzeFace.faceShapeSet.length > 0) {
					let face = this.photoAnalyzeFace.faceShapeSet[0];
					console.log('面部特征点数据:');
					console.log('- faceProfile:', face.faceProfile ? face.faceProfile.length : '无');
					console.log('- leftEye:', face.leftEye ? face.leftEye.length : '无');
					console.log('- rightEye:', face.rightEye ? face.rightEye.length : '无');
					console.log('- leftEyebrow:', face.leftEyebrow ? face.leftEyebrow.length : '无');
					console.log('- rightEyebrow:', face.rightEyebrow ? face.rightEyebrow.length : '无');
					console.log('- nose:', face.nose ? face.nose.length : '无');
					console.log('- mouth:', face.mouth ? face.mouth.length : '无');
				}
				
				let imageWidth = this.photoAnalyzeFace.imageWidth;
				let imageHeight = this.photoAnalyzeFace.imageHeight;
				let originalSize = {width: imageWidth, height: imageHeight};
				let frameSize = {width: this.windowWidth, height: this.windowHeight};

				let canvasSize = this.getScaleSize(originalSize, frameSize); 
				this.$set(this, 'canvasWidth', canvasSize.width);
				this.$set(this, 'canvasHeight', canvasSize.height);
				this.scaleRatio = canvasSize.scaleRatio;
				// console.log(canvasSize);

				this.canvasContext.restore();
				this.canvasContext.drawImage(this.photoTempFilePath, 0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制面部识别标记
				if (this.showFaceDetection) {
					this.drawFaceDetection();
				}

				if (this.promotionCode != null) {
					let codeWidth = 280 / this.pixelRatio;
					let codeHeight = 280 / this.pixelRatio;
					this.canvasContext.drawImage(this.promotionCode, this.canvasWidth - codeWidth - 20, this.canvasHeight - codeHeight - 35, codeWidth, codeHeight);

					this.canvasContext.setFontSize(36 / this.pixelRatio);
					this.canvasContext.fillStyle = "#ffffff";
					this.canvasContext.fillText("长按或扫描查看", this.canvasWidth - codeWidth - 14, this.canvasHeight - 15);
				}

				// 检查是否需要绘制人脸图片叠加或眼镜
				if (this.showFaceImageOverlay && !this.isDefaultModel && !this.hasGlassesSelected) {
					// 绘制人脸图片叠加 - 只有在未选择眼镜时才显示
					this.drawFaceImageOverlay();
				} else {
					// 绘制眼镜或直接完成绘制
					this.getGlassesInfo();
				}
			},
			
			getGlassesInfo() {
				// 如果没有选择眼镜，直接绘制画布并返回
				if (this.curSkuIndex < 0 || !this.hasGlassesSelected) {
					this.canvasContext.draw();
					this.$set(this, 'canvasImg', null);
					setTimeout(() => {
						uni.canvasToTempFilePath({
							x: 0,
							y: 0,
							width: this.canvasWidth,
							canvasId: 'photo',
							success: (res) => {
								uni.hideLoading();
								this.$set(this, 'canvasImgWidth', this.canvasWidth);
								this.$set(this, 'canvasImgHeight', this.canvasHeight);
								this.$set(this, 'canvasImg', res.tempFilePath);
							},
							fail: () => {
								uni.hideLoading();
								this.$util.Tips({
									title: '试戴失败'
								});
							}
						});
					}, 500);
					return;
				}

				let imagePath = this.skuList[this.curSkuIndex].wearImage;

				let gw = parseInt(this.skuList[this.curSkuIndex].gw);
				let pd = parseInt(this.PD_list[this.PD_index]);
				
				uni.getImageInfo({
					src: imagePath,
					success: (imageInfo) => {
						if (gw > 0 && pd > 0) {
							this.handleFaceWithTrueSize(imageInfo, gw, pd);
						} else {
							this.handleFaces(imageInfo);
						}
					},
					fail: () => {
						this.canvasContext.draw();
						uni.hideLoading();
						
						this.$util.Tips({
							title: '眼镜素材加载失败'
						});
					}
				});
			},
			
			handleFaceWithTrueSize(glassInfo, gw, pd) {
				let faces = this.photoAnalyzeFace.faceShapeSet;
				if (faces && faces.length > 0) {
					try {
						for (let i in faces) {
							let item = faces[i];
							if (item) {
								let eyeLeft = {x: item.leftPupil[0].x * this.scaleRatio, y: item.leftPupil[0].y * this.scaleRatio};
								let eyeRight = {x: item.rightPupil[0].x * this.scaleRatio, y: item.rightPupil[0].y * this.scaleRatio};
								
								// console.log(eyeLeft);
								// console.log(eyeRight);
								// this.drawRound(eyeLeft.x,eyeLeft.y, 2, "red")
								// this.drawRound(eyeRight.x,eyeRight.y, 2, "red")
						
								if (eyeRight.y > eyeLeft.y) {
									//左眼在上，右眼在下
									let ylen = eyeRight.y - eyeLeft.y;
									let xlen = eyeRight.x - eyeLeft.x;
									//两眼间距离
									let zlen = Math.sqrt(xlen*xlen + ylen*ylen);
									//头的偏转角度
									let point = -Math.atan2(-ylen, xlen);
									
									let glassesWidth = gw * zlen / pd;
									let glassesHeight = glassesWidth * glassInfo.height / glassInfo.width;
									
									let xtemp = (glassesWidth - zlen) / 2 * xlen / zlen;
									let ytemp = (glassesWidth - zlen) / 2 * ylen / zlen;
									
									var glassesLeftX = eyeLeft.x - xtemp + (glassesHeight / 3) * ylen / zlen;
									var glassesLeftY = eyeLeft.y - ytemp - (glassesHeight / 3) * xlen / zlen;
									
									this.canvasContext.rotate(point);
									let originalPoint = this.getMoveOriginalPoint(glassesLeftX, glassesLeftY, point);
									this.canvasContext.drawImage(glassInfo.path, originalPoint.x, originalPoint.y, glassesWidth, glassesHeight);
								} else {
									//左眼在下，右眼在上
									let ylen = eyeLeft.y - eyeRight.y;
									let xlen = eyeRight.x - eyeLeft.x;
									//两眼间距离
									let zlen = Math.sqrt(xlen*xlen + ylen*ylen);
									//头的偏转角度
									let point = -Math.atan2(ylen, xlen);;
									
									let glassesWidth = gw * zlen / pd;
									let glassesHeight = glassesWidth * glassInfo.height / glassInfo.width;
									
									let xtemp = (glassesWidth - zlen) / 2 * xlen / zlen;
									let ytemp = (glassesWidth - zlen) / 2 * ylen / zlen;
									
									let glassesLeftX = eyeLeft.x - xtemp - (glassesHeight / 3) * ylen / zlen;
									let glassesLeftY = eyeLeft.y + ytemp - (glassesHeight / 3) * xlen / zlen;
									
									this.canvasContext.rotate(point);
									let originalPoint = this.getMoveOriginalPoint(glassesLeftX, glassesLeftY, point);
									this.canvasContext.drawImage(glassInfo.path, originalPoint.x, originalPoint.y, glassesWidth, glassesHeight);
								}
							}
						}
					} catch (err) {
						console.log(err);
					}
				}
				
				this.canvasContext.draw();
				this.$set(this, 'canvasImg', null);
				setTimeout(() => {
					uni.canvasToTempFilePath({
						x: 0,
						y: 0,
						width: this.canvasWidth,
						canvasId: 'photo',
						success: (res) => {
							uni.hideLoading();
							this.$set(this, 'canvasImgWidth', this.canvasWidth);
							this.$set(this, 'canvasImgHeight', this.canvasHeight);
							this.$set(this, 'canvasImg', res.tempFilePath);
						},
						fail: () => {
							uni.hideLoading();
							this.$util.Tips({
								title: '试戴失败'
							});
						}
					});
				}, 500);
			},
			
			handleFaces(glassInfo) {
				let faces = this.photoAnalyzeFace.faceShapeSet;
				if (faces && faces.length > 0) {
					try {
						for (let i in faces) {
							let item = faces[i];
							if (item) {
								let eyeLeft = {x: item.leftEye[0].x * this.scaleRatio, y: item.leftEye[0].y * this.scaleRatio};
								let eyeRight = {x: item.rightEye[0].x * this.scaleRatio, y: item.rightEye[0].y * this.scaleRatio};
						
								let faceLeft = {x: item.faceProfile[0].x * this.scaleRatio, y: item.faceProfile[0].y * this.scaleRatio};;
								let faceRight = {x: item.faceProfile[20].x * this.scaleRatio, y: item.faceProfile[20].y * this.scaleRatio};;
						
								// console.log(eyeLeft);
								// console.log(eyeRight);
								// this.drawRound(eyeLeft.x,eyeLeft.y, 2, "red")
								// this.drawRound(eyeRight.x,eyeRight.y, 2, "red")
								// this.drawRound(faceLeft.x,faceLeft.y, 2, "red")
								// this.drawRound(faceRight.x,faceRight.y, 2, "red")
						
								if (eyeRight.y > eyeLeft.y) {
									//计算脸宽度
									let yflen = faceRight.y - faceLeft.y;
									let xflen = faceRight.x - faceLeft.x;
									let faceWidth = Math.sqrt(xflen*xflen + yflen*yflen) * 1.1;
									
									//左眼在上，右眼在下
									let ylen = eyeRight.y - eyeLeft.y;
									let xlen = eyeRight.x - eyeLeft.x;
									//两眼间距离
									let zlen = Math.sqrt(xlen*xlen + ylen*ylen);
									//头的偏转角度
									let point = -Math.atan2(-ylen, xlen);
									
									let xtemp = (faceWidth - zlen) / 2 * xlen / zlen;
									let ytemp = (faceWidth - zlen) / 2 * ylen / zlen;
									
									//与两眼在同一直线，脸的左侧坐标点
									let faceLeftX = eyeLeft.x - xtemp;
									let faceLeftY = eyeLeft.y - ytemp;
									
									let glassesWidth = faceWidth;
									let glassesHeight = glassesWidth * glassInfo.height / glassInfo.width;
									let glassesLeftX = faceLeftX + (glassesHeight / 3) * ylen / zlen;
									let glassesLeftY = faceLeftY - (glassesHeight / 3) * xlen / zlen;
									
									this.canvasContext.rotate(point);
									let originalPoint = this.getMoveOriginalPoint(glassesLeftX, glassesLeftY, point);
									this.canvasContext.drawImage(glassInfo.path, originalPoint.x, originalPoint.y, glassesWidth, glassesHeight);
								} else {
									//计算脸宽度
									let yflen = faceLeft.y - faceRight.y;
									let xflen = faceRight.x - faceLeft.x;
									let faceWidth = Math.sqrt(xflen*xflen + yflen*yflen) * 1.1;
									
									//左眼在下，右眼在上
									let ylen = eyeLeft.y - eyeRight.y;
									let xlen = eyeRight.x - eyeLeft.x;
									//两眼间距离
									let zlen = Math.sqrt(xlen*xlen + ylen*ylen);
									//头的偏转角度
									let point = -Math.atan2(ylen, xlen);;
									
									let xtemp = (faceWidth - zlen) / 2 * xlen / zlen;
									let ytemp = (faceWidth - zlen) / 2 * ylen / zlen;
									
									//与两眼在同一直线，脸的左侧坐标点
									let faceLeftX = eyeLeft.x - xtemp;
									let faceLeftY = eyeLeft.y + ytemp;
									
									let glassesWidth = faceWidth;
									let glassesHeight = glassesWidth * glassInfo.height / glassInfo.width;
									let glassesLeftX = faceLeftX - (glassesHeight / 3) * ylen / zlen;
									let glassesLeftY = faceLeftY - (glassesHeight / 3) * xlen / zlen;
									
									this.canvasContext.rotate(point);
									let originalPoint = this.getMoveOriginalPoint(glassesLeftX, glassesLeftY, point);
									this.canvasContext.drawImage(glassInfo.path, originalPoint.x, originalPoint.y, glassesWidth, glassesHeight);
								}
							}
						}
					} catch (err) {
						console.log(err);
					}
				}
				
				this.canvasContext.draw();
				this.$set(this, 'canvasImg', null);
				setTimeout(() => {
					uni.canvasToTempFilePath({
						x: 0,
						y: 0,
						width: this.canvasWidth,
						canvasId: 'photo',
						success: (res) => {
							uni.hideLoading();
							this.$set(this, 'canvasImgWidth', this.canvasWidth);
							this.$set(this, 'canvasImgHeight', this.canvasHeight);
							this.$set(this, 'canvasImg', res.tempFilePath);
						},
						fail: () => {
							uni.hideLoading();
							this.$util.Tips({
								title: '试戴失败'
							});
						}
					});
				}, 500);
			},
			
			getScaleSize(originalSize, frameSize) {
				let frameWidth = frameSize.width;
				let frameHeight = frameSize.height;
				
				let originalWidth = originalSize.width;
				let originalHeight = originalSize.height;
				
				let scaleWidth = originalWidth;
				let scaleHeight = originalHeight;
				let scaleRatio = 1;
				
				if (scaleWidth > frameWidth) {
					scaleWidth = frameWidth;
					scaleHeight = originalHeight * scaleWidth / originalWidth;
				}
				if (scaleHeight > frameHeight) {
					scaleHeight = frameHeight;
					scaleWidth = originalWidth * scaleHeight / originalHeight;
				}
				scaleRatio = scaleHeight / originalHeight;
				
				return {width: scaleWidth, height: scaleHeight, scaleRatio: scaleRatio};
			},
			
			getMoveOriginalPoint(targetX, targetY, moveRadian) {
				let targetDistance = Math.sqrt(targetX*targetX + targetY*targetY);
				let originalRadian = (Math.PI*2/360) * 90 - Math.atan2(targetY, targetX) + moveRadian;;
				let originalX = Math.sin(originalRadian) * targetDistance;
				let originalY = Math.sin((Math.PI*2/360) * 90 - originalRadian) * targetDistance;
				return {x: originalX, y: originalY};
			},
			
			drawRound(x, y, r, bColor) {
				if (this.canvasContext != null) {
					this.canvasContext.fillStyle = bColor;
					this.canvasContext.beginPath();
					this.canvasContext.arc(x, y, r, 0, 2*Math.PI);
					this.canvasContext.stroke();
					this.canvasContext.closePath();
					if (bColor) {
						this.canvasContext.fill();
					}
				}
			},

			// 绘制虚线
			drawDashedLine(points, closed = false) {
				if (!points || points.length < 2) return;

				this.canvasContext.setLineDash([5, 5]); // 设置虚线样式
				this.canvasContext.strokeStyle = '#ffffff';
				this.canvasContext.lineWidth = 2;
				this.canvasContext.beginPath();

				// 移动到第一个点
				this.canvasContext.moveTo(points[0].x * this.scaleRatio, points[0].y * this.scaleRatio);

				// 连接所有点
				for (let i = 1; i < points.length; i++) {
					this.canvasContext.lineTo(points[i].x * this.scaleRatio, points[i].y * this.scaleRatio);
				}

				// 如果需要闭合路径
				if (closed) {
					this.canvasContext.closePath();
				}

				this.canvasContext.stroke();
				this.canvasContext.setLineDash([]); // 重置虚线样式
			},

			// 绘制面部识别标记
			drawFaceDetection() {
				if (!this.photoAnalyzeFace || !this.photoAnalyzeFace.faceShapeSet) return;

				let faces = this.photoAnalyzeFace.faceShapeSet;
				if (faces && faces.length > 0) {
					for (let i in faces) {
						let face = faces[i];
						if (face) {
							// 添加调试信息
							console.log('开始绘制面部识别标记...');
							
							// 设置虚线样式 - 更浅淡的颜色
							this.canvasContext.setLineDash([6, 4]); // 虚线样式
							this.canvasContext.strokeStyle = 'rgba(255, 255, 255, 0.3)'; // 更淡的白色
							this.canvasContext.lineWidth = 1.5; // 稍微细一点
							this.canvasContext.shadowColor = 'rgba(0, 0, 0, 0.2)';
							this.canvasContext.shadowBlur = 1;
							this.canvasContext.shadowOffsetX = 0.5;
							this.canvasContext.shadowOffsetY = 0.5;

							// 绘制完整面部轮廓（包括额头）
							if (face.faceProfile && face.faceProfile.length > 0) {
								console.log('绘制面部轮廓，包含额头');
								this.drawCompleteFaceContour(face);
							}

							// 绘制左眼轮廓
							if (face.leftEye && face.leftEye.length > 0) {
								this.drawEyeContour(face.leftEye);
							}

							// 绘制右眼轮廓
							if (face.rightEye && face.rightEye.length > 0) {
								this.drawEyeContour(face.rightEye);
							}

							// 绘制左眉毛
							if (face.leftEyebrow && face.leftEyebrow.length > 0) {
								this.drawEyebrowContour(face.leftEyebrow);
							}

							// 绘制右眉毛
							if (face.rightEyebrow && face.rightEyebrow.length > 0) {
								this.drawEyebrowContour(face.rightEyebrow);
							}

							// 绘制鼻子轮廓
							if (face.nose && face.nose.length > 0) {
								this.drawNoseContour(face.nose);
							}

							// 绘制嘴巴轮廓
							if (face.mouth && face.mouth.length > 0) {
								this.drawMouthContour(face.mouth);
							}

							// 绘制额头区域
							console.log('绘制额头区域横线');
							this.drawForeheadArea(face);

							// 绘制高科技扫描线效果
							console.log('绘制扫描线效果');
							this.drawScanLines(face);

							// 重置阴影和虚线
							this.canvasContext.shadowColor = 'transparent';
							this.canvasContext.shadowBlur = 0;
							this.canvasContext.shadowOffsetX = 0;
							this.canvasContext.shadowOffsetY = 0;
							this.canvasContext.setLineDash([]);
							
							console.log('面部识别标记绘制完成');
						}
					}
				}
			},

			// 绘制完整面部轮廓（包括额头）
			drawCompleteFaceContour(face) {
				if (!face.faceProfile || face.faceProfile.length < 3) return;

				// 获取面部关键点
				let facePoints = face.faceProfile;
				let leftEyebrow = face.leftEyebrow || [];
				let rightEyebrow = face.rightEyebrow || [];

				this.canvasContext.beginPath();

				// 从下巴开始绘制面部轮廓
				this.canvasContext.moveTo(facePoints[0].x * this.scaleRatio, facePoints[0].y * this.scaleRatio);

				// 绘制左侧面部轮廓到太阳穴
				for (let i = 1; i < Math.floor(facePoints.length / 2); i++) {
					this.canvasContext.lineTo(facePoints[i].x * this.scaleRatio, facePoints[i].y * this.scaleRatio);
				}

				// 计算额头位置 - 重新设计算法
				let leftTemple = facePoints[Math.floor(facePoints.length / 2) - 1];
				let rightTemple = facePoints[Math.floor(facePoints.length / 2)];
				
				// 获取眼睛位置作为参考
				let leftEye = face.leftEye && face.leftEye.length > 0 ? face.leftEye : [];
				let rightEye = face.rightEye && face.rightEye.length > 0 ? face.rightEye : [];
				
				let foreheadHeight;
				let eyeLevel;
				let faceWidth = Math.abs(rightTemple.x - leftTemple.x);
				
				// 优先使用眉毛数据计算额头
				if (leftEyebrow.length > 0 && rightEyebrow.length > 0) {
					let leftEyeTop = leftEye.length > 0 ? leftEye[1] : leftEyebrow[2];
					let rightEyeTop = rightEye.length > 0 ? rightEye[1] : rightEyebrow[2];
					let eyebrowHeight = Math.abs(leftEyebrow[2].y - leftEyeTop.y);
					foreheadHeight = Math.max(eyebrowHeight * 2.5, faceWidth * 0.3); // 使用面部宽度作为参考
					eyeLevel = Math.min(leftEyebrow[2].y, rightEyebrow[2].y);
				}
				// 如果没有眉毛数据，使用眼睛数据估算（主要情况）
				else if (leftEye.length > 0 && rightEye.length > 0) {
					// 眼睛顶部位置
					eyeLevel = Math.min(leftEye[1].y, rightEye[1].y);
					// 根据面部比例估算额头高度 - 额头通常占眼睛到下巴距离的40-50%
					let chinY = Math.max(...facePoints.map(p => p.y));
					let eyeToChinDistance = chinY - eyeLevel;
					foreheadHeight = eyeToChinDistance * 0.45; // 额头高度为眼睛到下巴距离的45%
					
					// 向上延伸眼睛位置作为估算的眉毛位置
					eyeLevel = eyeLevel - eyeToChinDistance * 0.08; // 眉毛通常在眼睛上方约8%的距离
				}
				// 最后的fallback方案 - 使用面部轮廓估算
				else {
					// 找到面部轮廓的最高点作为眼睛大概位置
					eyeLevel = Math.min(...facePoints.map(p => p.y));
					let faceHeight = Math.max(...facePoints.map(p => p.y)) - eyeLevel;
					// 重新计算，眼睛应该在面部上半部分
					eyeLevel = eyeLevel + faceHeight * 0.25; // 眼睛位置大约在面部25%处
					foreheadHeight = faceHeight * 0.35; // 额头占面部高度的35%
				}

				// 计算额头关键点 - 创建更自然的额头形状
				// 额头左侧点（向外扩展并稍微向上）
				let foreheadLeft = {
					x: leftTemple.x - faceWidth * 0.15, // 更大的外扩
					y: eyeLevel - foreheadHeight * 0.8 // 稍微低一点
				};
				
				// 额头中心点（最高点）
				let foreheadCenter = {
					x: (leftTemple.x + rightTemple.x) / 2,
					y: eyeLevel - foreheadHeight * 1.1 // 中心点最高
				};
				
				// 额头右侧点
				let foreheadRight = {
					x: rightTemple.x + faceWidth * 0.15, // 更大的外扩
					y: eyeLevel - foreheadHeight * 0.8 // 与左侧对称
				};

				// 绘制额头曲线 - 使用两段贝塞尔曲线创建更自然的额头形状
				this.canvasContext.quadraticCurveTo(
					foreheadLeft.x * this.scaleRatio,
					foreheadLeft.y * this.scaleRatio,
					foreheadCenter.x * this.scaleRatio,
					foreheadCenter.y * this.scaleRatio
				);
				this.canvasContext.quadraticCurveTo(
					foreheadRight.x * this.scaleRatio,
					foreheadRight.y * this.scaleRatio,
					rightTemple.x * this.scaleRatio,
					rightTemple.y * this.scaleRatio
				);

				// 绘制右侧面部轮廓
				for (let i = Math.floor(facePoints.length / 2); i < facePoints.length; i++) {
					this.canvasContext.lineTo(facePoints[i].x * this.scaleRatio, facePoints[i].y * this.scaleRatio);
				}

				this.canvasContext.closePath();
				this.canvasContext.stroke();
			},

			// 绘制眼睛轮廓
			drawEyeContour(points) {
				if (!points || points.length < 3) return;

				this.canvasContext.beginPath();
				this.canvasContext.moveTo(points[0].x * this.scaleRatio, points[0].y * this.scaleRatio);

				for (let i = 1; i < points.length; i++) {
					this.canvasContext.lineTo(points[i].x * this.scaleRatio, points[i].y * this.scaleRatio);
				}

				this.canvasContext.closePath();
				this.canvasContext.stroke();
			},

			// 绘制眉毛轮廓
			drawEyebrowContour(points) {
				if (!points || points.length < 2) return;

				this.canvasContext.beginPath();
				this.canvasContext.moveTo(points[0].x * this.scaleRatio, points[0].y * this.scaleRatio);

				for (let i = 1; i < points.length; i++) {
					this.canvasContext.lineTo(points[i].x * this.scaleRatio, points[i].y * this.scaleRatio);
				}

				this.canvasContext.stroke();
			},

			// 绘制鼻子轮廓
			drawNoseContour(points) {
				if (!points || points.length < 2) return;

				this.canvasContext.beginPath();
				this.canvasContext.moveTo(points[0].x * this.scaleRatio, points[0].y * this.scaleRatio);

				for (let i = 1; i < points.length; i++) {
					this.canvasContext.lineTo(points[i].x * this.scaleRatio, points[i].y * this.scaleRatio);
				}

				this.canvasContext.stroke();
			},

			// 绘制嘴巴轮廓
			drawMouthContour(points) {
				if (!points || points.length < 3) return;

				this.canvasContext.beginPath();
				this.canvasContext.moveTo(points[0].x * this.scaleRatio, points[0].y * this.scaleRatio);

				for (let i = 1; i < points.length; i++) {
					this.canvasContext.lineTo(points[i].x * this.scaleRatio, points[i].y * this.scaleRatio);
				}

				this.canvasContext.closePath();
				this.canvasContext.stroke();
			},

			// 绘制额头区域
			drawForeheadArea(face) {
				let leftEyebrow = face.leftEyebrow || [];
				let rightEyebrow = face.rightEyebrow || [];
				let leftEye = face.leftEye || [];
				let rightEye = face.rightEye || [];
				
				let eyebrowCenterY, eyebrowWidth, foreheadHeight;
				
				// 优先使用眉毛数据
				if (leftEyebrow.length > 0 && rightEyebrow.length > 0) {
					eyebrowCenterY = (leftEyebrow[2].y + rightEyebrow[2].y) / 2;
					eyebrowWidth = Math.abs(rightEyebrow[4].x - leftEyebrow[0].x);
					foreheadHeight = eyebrowWidth * 0.5; // 增加额头高度
				}
				// 如果没有眉毛数据，使用眼睛数据（主要情况）
				else if (leftEye.length > 0 && rightEye.length > 0) {
					let eyeLevel = Math.min(leftEye[1].y, rightEye[1].y);
					// 根据面部比例计算
					let facePoints = face.faceProfile || [];
					if (facePoints.length > 0) {
						let chinY = Math.max(...facePoints.map(p => p.y));
						let eyeToChinDistance = chinY - eyeLevel;
						foreheadHeight = eyeToChinDistance * 0.45;
						eyebrowCenterY = eyeLevel - eyeToChinDistance * 0.08; // 估算眉毛位置
					} else {
						// fallback
						eyebrowCenterY = eyeLevel - 50; // 简单向上偏移
						foreheadHeight = 80;
					}
					eyebrowWidth = Math.abs(rightEye[3].x - leftEye[0].x);
				}
				// 如果都没有，使用面部轮廓数据
				else if (face.faceProfile && face.faceProfile.length > 0) {
					let facePoints = face.faceProfile;
					let minY = Math.min(...facePoints.map(p => p.y));
					let maxY = Math.max(...facePoints.map(p => p.y));
					let maxX = Math.max(...facePoints.map(p => p.x));
					let minX = Math.min(...facePoints.map(p => p.x));
					
					let faceHeight = maxY - minY;
					eyebrowCenterY = minY + faceHeight * 0.33; // 估算眉毛位置
					eyebrowWidth = maxX - minX;
					foreheadHeight = faceHeight * 0.35;
				}
				else {
					return; // 没有可用数据
				}

				// 绘制5条额头横线，营造扫描效果
				for (let i = 0; i < 5; i++) {
					let lineY = eyebrowCenterY - (foreheadHeight / 5) * (i + 1);
					let lineStartX, lineEndX;
					
					// 根据可用数据计算线条起止点
					if (leftEyebrow.length > 0 && rightEyebrow.length > 0) {
						lineStartX = leftEyebrow[0].x - 20 + i * 6;
						lineEndX = rightEyebrow[4].x + 20 - i * 6;
					} else if (leftEye.length > 0 && rightEye.length > 0) {
						lineStartX = leftEye[0].x - 25 + i * 8;
						lineEndX = rightEye[3].x + 25 - i * 8;
					} else if (face.faceProfile && face.faceProfile.length > 0) {
						let facePoints = face.faceProfile;
						let leftPoint = facePoints[Math.floor(facePoints.length * 0.2)];
						let rightPoint = facePoints[Math.floor(facePoints.length * 0.8)];
						lineStartX = leftPoint.x + i * 10;
						lineEndX = rightPoint.x - i * 10;
					}

					this.canvasContext.beginPath();
					this.canvasContext.moveTo(lineStartX * this.scaleRatio, lineY * this.scaleRatio);
					this.canvasContext.lineTo(lineEndX * this.scaleRatio, lineY * this.scaleRatio);
					this.canvasContext.stroke();
				}
			},

			// 绘制高科技扫描线效果
			drawScanLines(face) {
				if (!face.faceProfile || face.faceProfile.length === 0) return;

				// 设置扫描线样式 - 更淡的颜色
				this.canvasContext.setLineDash([3, 2]);
				this.canvasContext.strokeStyle = 'rgba(189, 253, 91, 0.25)'; // 更淡的淡绿色扫描线
				this.canvasContext.lineWidth = 1;

				// 获取面部边界
				let minY = Math.min(...face.faceProfile.map(p => p.y));
				let maxY = Math.max(...face.faceProfile.map(p => p.y));
				let minX = Math.min(...face.faceProfile.map(p => p.x));
				let maxX = Math.max(...face.faceProfile.map(p => p.x));

				// 计算额头区域，扩展Y轴范围 - 使用与面部轮廓相同的算法
				let leftEyebrow = face.leftEyebrow || [];
				let rightEyebrow = face.rightEyebrow || [];
				let leftEye = face.leftEye || [];
				let rightEye = face.rightEye || [];
				
				let foreheadHeight;
				let eyeLevel;
				let faceWidth = maxX - minX;
				
				// 优先使用眉毛数据计算额头
				if (leftEyebrow.length > 0 && rightEyebrow.length > 0) {
					eyeLevel = Math.min(leftEyebrow[2].y, rightEyebrow[2].y);
					let leftEyeTop = leftEye.length > 0 ? leftEye[1] : leftEyebrow[2];
					let rightEyeTop = rightEye.length > 0 ? rightEye[1] : rightEyebrow[2];
					let eyebrowHeight = Math.abs(leftEyebrow[2].y - leftEyeTop.y);
					foreheadHeight = Math.max(eyebrowHeight * 2.5, faceWidth * 0.3);
				}
				// 如果没有眉毛数据，使用眼睛数据估算（主要情况）
				else if (leftEye.length > 0 && rightEye.length > 0) {
					eyeLevel = Math.min(leftEye[1].y, rightEye[1].y);
					let chinY = maxY;
					let eyeToChinDistance = chinY - eyeLevel;
					foreheadHeight = eyeToChinDistance * 0.45;
					eyeLevel = eyeLevel - eyeToChinDistance * 0.08;
				}
				// 最后的fallback方案
				else {
					eyeLevel = minY;
					let faceHeight = maxY - minY;
					eyeLevel = eyeLevel + faceHeight * 0.25;
					foreheadHeight = faceHeight * 0.35;
				}
				
				// 扩展到额头区域 - 使用更大的扩展范围
				minY = eyeLevel - foreheadHeight * 1.1;
				
				// 稍微扩展X轴范围，包含额头的宽度
				minX = minX - faceWidth * 0.15;
				maxX = maxX + faceWidth * 0.15;

				// 绘制垂直扫描线 - 只显示一条中央竖线
				let totalWidth = maxX - minX;
				let centerX = minX + totalWidth / 2; // 计算中央位置

				this.canvasContext.beginPath();
				this.canvasContext.moveTo(centerX * this.scaleRatio, minY * this.scaleRatio);
				this.canvasContext.lineTo(centerX * this.scaleRatio, maxY * this.scaleRatio);
				this.canvasContext.stroke();

				// 绘制水平扫描线
				let horizontalLineCount = 8; // 增加水平线数量以覆盖额头
				let totalHeight = maxY - minY;
				for (let i = 0; i < horizontalLineCount; i++) {
					let y = minY + (totalHeight / (horizontalLineCount - 1)) * i;

					this.canvasContext.beginPath();
					this.canvasContext.moveTo(minX * this.scaleRatio, y * this.scaleRatio);
					this.canvasContext.lineTo(maxX * this.scaleRatio, y * this.scaleRatio);
					this.canvasContext.stroke();
				}
			},
			
			// 绘制人脸图片叠加
			drawFaceImageOverlay() {
				if (!this.photoAnalyzeFace || !this.photoAnalyzeFace.faceShapeSet) return;

				let faces = this.photoAnalyzeFace.faceShapeSet;
				if (faces && faces.length > 0) {
					// 使用网络图片链接
					let imagePaths = [
						this.particularsImagePath,
					];
					
					console.log('正在尝试加载图片，路径:', imagePaths);
					
					this.tryLoadImage(imagePaths, 0, faces);
				}
			},
			
			// 尝试加载图片的递归方法
			tryLoadImage(paths, index, faces) {
				if (index >= paths.length) {
					console.error('所有图片路径都尝试失败');
					return;
				}

				let currentPath = paths[index];
				console.log('尝试路径:', currentPath);

				// 如果是网络图片，先下载到本地
				if (currentPath.startsWith('http://') || currentPath.startsWith('https://')) {
					uni.downloadFile({
						url: currentPath,
						success: (downloadRes) => {
							console.log('网络图片下载成功:', downloadRes.tempFilePath);
							this.drawImageOnFaces(downloadRes.tempFilePath, faces);
						},
						fail: (err) => {
							console.log('网络图片下载失败:', currentPath, err);
							// 尝试下一个路径
							this.tryLoadImage(paths, index + 1, faces);
						}
					});
				} else {
					// 本地图片直接使用 getImageInfo
					uni.getImageInfo({
						src: currentPath,
						success: (imgInfo) => {
							console.log('本地图片加载成功:', imgInfo);
							this.drawImageOnFaces(currentPath, faces);
						},
						fail: (err) => {
							console.log('本地图片路径失败:', currentPath, err);
							// 尝试下一个路径
							this.tryLoadImage(paths, index + 1, faces);
						}
					});
				}
			},

			// 在人脸上绘制图片的方法
			drawImageOnFaces(imagePath, faces) {
				uni.getImageInfo({
					src: imagePath,
					success: (imgInfo) => {
						console.log('图片信息获取成功:', imgInfo);
						console.log('使用路径进行绘制:', imagePath);
						for (let i in faces) {
							let face = faces[i];
							if (face && face.faceProfile && face.faceProfile.length > 0) {
								console.log('开始在人脸上叠加图片...');
								console.log('缩放比例 scaleRatio:', this.scaleRatio);

								// 尝试基于眼睛位置的简单方法
								if (face.leftEye && face.rightEye && face.leftEye.length > 0 && face.rightEye.length > 0) {
									let overlayInfo = this.calculateEyeBasedOverlay(face, imgInfo);

									// 在画布上绘制图片
									this.canvasContext.drawImage(
										imagePath,
										overlayInfo.x,
										overlayInfo.y,
										overlayInfo.width,
										overlayInfo.height
									);

									console.log('基于眼睛的图片叠加完成', overlayInfo);

									// 重新绘制canvas并生成图片
									this.redrawCanvasWithOverlay();
								} else {
									// 备用方案：基于人脸边界
									let faceBounds = this.calculateFaceBounds(face);

									if (faceBounds) {
										// 计算图片叠加的位置和大小
										let overlayInfo = this.calculateOverlayPosition(faceBounds, imgInfo);

										// 在画布上绘制图片
										this.canvasContext.drawImage(
											imagePath,
											overlayInfo.x,
											overlayInfo.y,
											overlayInfo.width,
											overlayInfo.height
										);

										console.log('人脸图片叠加完成', overlayInfo);

										// 重新绘制canvas并生成图片
										this.redrawCanvasWithOverlay();
									}
								}
							}
						}
					},
					fail: (err) => {
						console.log('获取图片信息失败:', imagePath, err);
					}
				});
			},
			
			// 基于眼睛位置计算图片叠加（新方法）
			calculateEyeBasedOverlay(face, imgInfo) {
				// 获取左右眼的中心点
				let leftEyeCenter = this.getPointsCenter(face.leftEye);
				let rightEyeCenter = this.getPointsCenter(face.rightEye);
				
				console.log('左眼中心:', leftEyeCenter);
				console.log('右眼中心:', rightEyeCenter);
				
				// 计算两眼之间的距离
				let eyeDistance = Math.sqrt(
					Math.pow(rightEyeCenter.x - leftEyeCenter.x, 2) + 
					Math.pow(rightEyeCenter.y - leftEyeCenter.y, 2)
				);
				
				console.log('眼距:', eyeDistance);
				
				// 以眼距作为基准，图片宽度为眼距的3.5倍（恢复原来的尺寸）
				let targetWidth = eyeDistance * 3.5 * this.scaleRatio;
				let targetHeight = targetWidth * (imgInfo.height / imgInfo.width);

				// 计算人脸中心点（两眼中心的中点）
				let faceCenterX = ((leftEyeCenter.x + rightEyeCenter.x) / 2) * this.scaleRatio;
				let faceCenterY = ((leftEyeCenter.y + rightEyeCenter.y) / 2) * this.scaleRatio;

				// 图片位置：以人脸中心为基准，向下偏移（恢复原来的位置计算）
				let overlayX = faceCenterX - targetWidth / 2;
				let overlayY = faceCenterY - targetHeight / 2 + targetHeight * 0.05;
				
				let result = {
					x: overlayX,
					y: overlayY,
					width: targetWidth,
					height: targetHeight,
					eyeDistance: eyeDistance,
					faceCenter: {x: faceCenterX, y: faceCenterY}
				};
				
				console.log('基于眼睛的叠加计算:', result);
				return result;
			},
			
			// 计算点集的中心点
			getPointsCenter(points) {
				if (!points || points.length === 0) return {x: 0, y: 0};
				
				let sumX = points.reduce((sum, p) => sum + p.x, 0);
				let sumY = points.reduce((sum, p) => sum + p.y, 0);
				
				return {
					x: sumX / points.length,
					y: sumY / points.length
				};
			},
			
			// 计算基于画布的图片叠加（更简单的方法）
			calculateCanvasOverlay(imgInfo) {
				// 直接让图片覆盖整个画布，保持比例
				let canvasWidth = this.canvasWidth;
				let canvasHeight = this.canvasHeight;
				
				console.log('画布尺寸:', {canvasWidth, canvasHeight});
				
				// 计算图片缩放比例，保持宽高比
				let scaleX = canvasWidth / imgInfo.width;
				let scaleY = canvasHeight / imgInfo.height;
				
				// 使用较小的缩放比例，确保图片完全在画布内
				let scale = Math.min(scaleX, scaleY);
				
				let targetWidth = imgInfo.width * scale;
				let targetHeight = imgInfo.height * scale;
				
				// 居中显示
				let x = (canvasWidth - targetWidth) / 2;
				let y = (canvasHeight - targetHeight) / 2;
				
				return {
					x: x,
					y: y,
					width: targetWidth,
					height: targetHeight,
					scale: scale
				};
			},
			
			// 计算人脸边界点（上下左右）
			calculateFaceBounds(face) {
				if (!face.faceProfile || face.faceProfile.length === 0) return null;
				
				let facePoints = face.faceProfile;
				
				// 找到人脸轮廓的边界点
				let minX = Math.min(...facePoints.map(p => p.x));
				let maxX = Math.max(...facePoints.map(p => p.x));
				let minY = Math.min(...facePoints.map(p => p.y));
				let maxY = Math.max(...facePoints.map(p => p.y));
				
				console.log('原始人脸边界(缩放前):', {minX, maxX, minY, maxY});
				console.log('原始人脸尺寸:', {width: maxX - minX, height: maxY - minY});
				
				// 使用更合理的扩展比例
				let faceWidth = maxX - minX;
				let faceHeight = maxY - minY;
				
				// 保守的扩展：只扩展20%的额头和10%的其他方向
				let foreheadExtend = faceHeight * 0.2;  // 减少到20%
				let horizontalExtend = faceWidth * 0.05; // 减少到5%
				let chinExtend = faceHeight * 0.05;     // 减少到5%
				
				minY = minY - foreheadExtend;
				minX = minX - horizontalExtend;
				maxX = maxX + horizontalExtend;
				maxY = maxY + chinExtend;
				
				console.log('扩展后边界(缩放前):', {minX, maxX, minY, maxY});
				console.log('扩展后尺寸(缩放前):', {width: maxX - minX, height: maxY - minY});
				
				let bounds = {
					left: minX * this.scaleRatio,
					right: maxX * this.scaleRatio,
					top: minY * this.scaleRatio,
					bottom: maxY * this.scaleRatio,
					width: (maxX - minX) * this.scaleRatio,
					height: (maxY - minY) * this.scaleRatio,
					centerX: ((minX + maxX) / 2) * this.scaleRatio,
					centerY: ((minY + maxY) / 2) * this.scaleRatio
				};
				
				console.log('最终人脸边界(缩放后):', bounds);
				return bounds;
			},
			
			// 计算图片叠加的位置和大小
			calculateOverlayPosition(faceBounds, imgInfo) {
				// 计算图片应该的大小（适应人脸大小，但保持图片比例）
				let faceWidth = faceBounds.width;
				let faceHeight = faceBounds.height;
				
				console.log('人脸尺寸:', {faceWidth, faceHeight});
				console.log('图片尺寸:', {width: imgInfo.width, height: imgInfo.height});
				
				// 大幅增加图片大小适应人脸大小的150%
				let scaleFactor = 1.5;
				let targetWidth = faceWidth * scaleFactor;
				let targetHeight = targetWidth * (imgInfo.height / imgInfo.width);
				
				// 如果按宽度缩放后高度超出人脸高度，则按高度缩放
				if (targetHeight > faceHeight * scaleFactor) {
					targetHeight = faceHeight * scaleFactor;
					targetWidth = targetHeight * (imgInfo.width / imgInfo.height);
				}
				
				// 计算居中位置
				let overlayX = faceBounds.centerX - targetWidth / 2;
				let overlayY = faceBounds.centerY - targetHeight / 2;
				
				let result = {
					x: overlayX,
					y: overlayY,
					width: targetWidth,
					height: targetHeight
				};
				
				console.log('最终叠加信息:', result);
				console.log('人脸中心点:', {centerX: faceBounds.centerX, centerY: faceBounds.centerY});
				
				return result;
			},

			// 重新绘制canvas并生成图片（用于人脸图片叠加）
			redrawCanvasWithOverlay() {
				console.log('重新绘制canvas（包含人脸图片叠加）...');
				this.canvasContext.draw();
				this.$set(this, 'canvasImg', null);
				setTimeout(() => {
					uni.canvasToTempFilePath({
						x: 0,
						y: 0,
						width: this.canvasWidth,
						canvasId: 'photo',
						success: (res) => {
							console.log('canvas重新绘制成功，包含人脸图片叠加');
							uni.hideLoading();
							this.$set(this, 'canvasImgWidth', this.canvasWidth);
							this.$set(this, 'canvasImgHeight', this.canvasHeight);
							this.$set(this, 'canvasImg', res.tempFilePath);
						},
						fail: () => {
							console.log('canvas重新绘制失败');
							uni.hideLoading();
							this.$util.Tips({
								title: '图片生成失败'
							});
						}
					});
				}, 500);
			},

			goBuy() {
				// 检查是否已选择眼镜
				if (!this.hasGlassesSelected || this.curSkuIndex < 0) {
					this.$util.Tips({
						title: '请先选择一款眼镜'
					});
					return;
				}

				let sku = this.skuList[this.curSkuIndex].sku;
				uni.navigateBack({
					delta: 1
				});
				uni.$emit('backgobuy', sku);
			}
		}
	}
</script>

<style lang="scss">
	page {
		overflow: hidden;
		background-color: #f5f5f5 !important;
	}
	canvas {
		position: fixed;
		top: 0;
	}

	.image-container {
		width: 90%;
		background-color: #D5DBD7;
		position: fixed;
		top: 40rpx;
		left: 5%;
		height: calc(100% - 450rpx);
		height: calc(100% - 450rpx - constant(safe-area-inset-bottom) / 2);
		height: calc(100% - 450rpx - env(safe-area-inset-bottom) / 2);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		border-radius: 30rpx;
		padding: 0 40rpx 10rpx 40rpx;
		box-sizing: border-box;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
		position: relative;
	}

	.canvas-img {
		width: 100%;
		height: 885rpx;
		object-fit: cover;
		border-radius: 20rpx;
	}

	.tip-text {
		position: absolute;
		bottom: 30rpx;
		right: 30rpx;
		background-color: rgba(255, 255, 255, 0.9);
		color: #999999;
		font-size: 24rpx;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		border: 1rpx solid #e0e0e0;
	}
	.footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		z-index: 200;
		background-color: #ffffff;
		padding: 0;
		padding-bottom: calc(constant(safe-area-inset-bottom));
		padding-bottom: calc(env(safe-area-inset-bottom));

		.glasses-section {
			margin: 0 30rpx 20rpx 30rpx;
		}

		.glasses-list {
			white-space: nowrap;
			width: 100%;
			height: 200rpx;

			.item {
				display: inline-block;
				width: 185rpx;
				height: 180rpx;
				margin-right: 20rpx;
				border: 2rpx solid transparent;
				border-radius: 15rpx;
				overflow: hidden;
				background-color: #ffffff;
				color: #333;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
				display: inline-grid;
				flex-direction: column;
				align-items: center;
				padding: 15rpx 10rpx;
				box-sizing: border-box;

				image {
					width: 100%;
					height: 65rpx;
					object-fit: contain;
					background-color: transparent;
					padding: 0;
					border: none;
					margin-bottom: 10rpx;
				}
				.loading {
					background: url('@/static/easy-loadimage/loading.gif') no-repeat center;
					background-size: 50rpx;
				}
				.loaded {
					background-color: transparent;
				}

				.sku {
					width: 100%;
					height: auto;
					line-height: 32rpx;
					text-align: center;
					font-size: 22rpx;
					color: #666;
					background-color: transparent;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					padding: 0;
				}
			}
			
			.item:first-child {
				margin-left: 20rpx;
			}
			.item:last-child {
				margin-right: 20rpx;
			}
			
			.item.on {
				border-color: #BDFD5B;
			}
		}
		
		.btn-section {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 40rpx;
			position: relative;

			.left-controls {
				display: flex;
				align-items: center;
				gap: 20rpx;
			}

			.picker-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 280rpx;
				height: 85rpx;
				line-height: 85rpx;
				background-color: #f5f5f5;
				border-radius: 20rpx;
				padding: 0 20rpx;
				position: relative;
				box-sizing: border-box;

				picker {
					z-index: 10;
					color: #333333;
				}

				.picker-text {
					font-size: 26rpx;
					color: #333333;
					flex: 1;
					text-align: left;
				}

				.picker-arrow {
					font-size: 20rpx;
					color: #999999;
					margin-left: 10rpx;
				}
			}

			.photo-choose {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 95rpx;
				height: 95rpx;
				background-color: #FF8125;
				border-radius: 50%;
				position: absolute;
    			top: -26rpx;
				left: 50%;
    			transform: translateX(-50%);

				.xiangji {
					font-size: 40rpx;
					color: #ffffff;
				}
			}

			.buy {
				width: 280rpx;
				height: 85rpx;
				line-height: 85rpx;
				border-radius: 20rpx;
				text-align: center;
				background-color: #BDFD5B;
				color: #222222;
				font-size: 28rpx;
				font-weight: bold;
			}
		}
	}
	.photo-choose-ways {
		width: 100%;
		background-color: #f5f5f5;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 388;
		transform: translate3d(0, 100%, 0);
		transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
		border-radius: 30rpx 30rpx 0 0;
		
		.button {
			background-color: #fff;
			width: 100%;
			height: 98rpx;
			line-height: 98rpx;
			text-align: center;
			font-size: 30rpx;
			color: #333333;
			border-bottom: 1px solid #eee;
			
			&:first-child {
				border-radius: 30rpx 30rpx 0 0;
			}
			&:last-child {
				border-bottom: none;
			}
		}

		.cancel {
			background-color: #fff;
			width: 100%;
			line-height: 98rpx;
			text-align: center;
			font-size: 30rpx;
			color: #333333;
			border-top: 1px solid #eee;
			margin-top: 10rpx;
			height: 98rpx;
			height: calc(98rpx + constant(safe-area-inset-bottom) / 2);
			height: calc(98rpx + env(safe-area-inset-bottom) / 2);
		}
		
		.button:hover, .cancel:hover {
			background-color: #dadada;
		}
	}
	.photo-choose-ways.on {
		transform: translate3d(0, 0, 0);
	}
	.mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 200;
		background-color: rgba(0, 0, 0, 0.6);
	}

	.glasses-section {
			margin: 60rpx 30rpx 20rpx 30rpx;
		}

	.glasses-list {
			white-space: nowrap;
			width: 100%;
			height: 200rpx;

			.item {
				display: inline-block;
				width: 185rpx;
				height: 180rpx;
				margin-right: 20rpx;
				border: 2rpx solid transparent;
				border-radius: 15rpx;
				overflow: hidden;
				background-color: #ffffff;
				color: #333;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
				display: inline-grid;
				flex-direction: column;
				align-items: center;
				padding: 15rpx 10rpx;
				box-sizing: border-box;

				image {
					width: 100%;
					height: 65rpx;
					object-fit: contain;
					background-color: transparent;
					padding: 0;
					border: none;
					margin-bottom: 10rpx;
				}
				.loading {
					background: url('@/static/easy-loadimage/loading.gif') no-repeat center;
					background-size: 50rpx;
				}
				.loaded {
					background-color: transparent;
				}

				.sku {
					width: 100%;
					height: auto;
					line-height: 32rpx;
					text-align: center;
					font-size: 22rpx;
					color: #666666;
					background-color: transparent;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					padding: 0;
				}
			}
			
			.item:first-child {
				margin-left: 20rpx;
			}
			.item:last-child {
				margin-right: 20rpx;
			}
			
			.item.on {
				border-color: #BDFD5B;
			}
		}
</style>
