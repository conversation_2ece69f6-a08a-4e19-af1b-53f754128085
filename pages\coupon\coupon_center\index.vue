<template>
	<view class="coupon-center">

		<!-- 优惠券列表 -->
		<view class="coupon-list">
			<view
				v-for="(coupon, index) in couponList"
				:key="index"
				class="coupon-item"
				:class="coupon.status"
			>
				<!-- 优惠券主体 -->
				<view class="coupon-main">
					<!-- 左侧内容 -->
					<view class="coupon-left">
						<!-- 优惠券图片 -->
						<view class="coupon-image" v-if="coupon.image">
							<image :src="coupon.image" mode="aspectFill"></image>
						</view>

						<!-- 优惠券信息 -->
						<view class="coupon-info">
							<view class="coupon-title">{{coupon.name}}</view>
							<view class="coupon-time">有效期至{{coupon.endTime}}</view>
							<view class="coupon-desc">{{coupon.description}}</view>
						</view>
					</view>

					<!-- 右侧优惠券面值和按钮 -->
					<view class="coupon-right" :class="coupon.status">
						<view class="coupon-value">
							<view class="discount" v-if="coupon.couponType === 2">
								{{coupon.discount}}<text style="font-size: 30rpx;"> 折</text>
							</view>
							<view class="money" v-else>
								<text class="currency">¥</text>
								<text>{{coupon.money}}</text>
							</view>
							<view class="condition" v-if="coupon.minPrice > 0">
								满{{coupon.minPrice}}元
							</view>
							<view class="condition" v-else>
								无门槛
							</view>
						</view>

						<view
							class="coupon-btn"
							:class="coupon.btnClass"
							@click="handleCouponAction(coupon)"
						>
							{{coupon.btnText}}
						</view>
					</view>
				</view>
				<!-- 上方半圆缺口 -->
				<view class="circle-top"></view>
				<!-- 下方半圆缺口 -->
				<view class="circle-bottom"></view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="loading-more" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 没有更多数据 -->
		<view class="no-more" v-if="!loading && loadEnd">
			<text class="no-more-text">没有更多优惠券了</text>
		</view>
	</view>
</template>

<script>
import { getCouponList, receiveCoupon } from '@/api/order.js';

export default {
	data() {
		return {
			statusBarHeight: 0,
			couponList: [],
			page: 1,
			limit: 10,
			loading: false,
			loadEnd: false,
			fromOrderPage: false // 是否从订单页面跳转过来
		};
	},
	onLoad(options) {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;

		// 检查是否从订单页面跳转过来
		if (options && options.from === 'order') {
			this.fromOrderPage = true;
		}

		// 获取优惠券列表
		this.getCouponList();
	},
	onReachBottom() {
		if (!this.loading && !this.loadEnd) {
			this.page++;
			this.getCouponList();
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 获取优惠券列表
		getCouponList() {
			if (this.loading) return;

			this.loading = true;
			let data = {
				category: 0, // 平台券
				publisher: this.fromOrderPage ? 1 : 0, // publisher: 0-全部，1-平台，2-商户。从订单页面跳转时传1
				merId: 0,
				page: this.page,
				limit: this.limit
			};

			getCouponList(data).then(res => {
				this.loading = false;
				if (res.code === 200 && res.data && res.data.list) {
					let newList = this.formatCouponList(res.data.list);
					if (this.page === 1) {
						this.couponList = newList;
					} else {
						this.couponList = this.couponList.concat(newList);
					}

					// 判断是否还有更多数据
					if (newList.length < this.limit) {
						this.loadEnd = true;
					}
				} else {
					// 没有数据时设置空数组
					if (this.page === 1) {
						this.couponList = [];
					}
				}
			}).catch(err => {
				this.loading = false;
				console.error('获取优惠券列表失败:', err);
				// 错误时设置空数组
				if (this.page === 1) {
					this.couponList = [];
				}
			});
		},

		// 格式化优惠券数据
		formatCouponList(list) {
			return list.map(coupon => {
				// 处理图片，优先使用product.image，如果product为null则使用空字符串
				let image = '';
				if (coupon.product && coupon.product.image) {
					image = coupon.product.image;
				}

				let formattedCoupon = {
					...coupon,
					title: coupon.name || '优惠券',
					endTime: coupon.useEndTimeStr || '2025.05.22',
					description: this.getCouponDescription(coupon),
					btnText: this.getCouponBtnText(coupon),
					btnClass: this.getCouponBtnClass(coupon),
					status: this.getCouponStatus(coupon),
					image: image
				};
				
				return formattedCoupon;
			});
		},
		
		// 获取优惠券描述
		getCouponDescription(coupon) {
			// 根据category显示不同类型的备注信息
			let categoryText = '';
			switch (coupon.category) {
				case 1:
					categoryText = '商家券';
					break;
				case 2:
					categoryText = '商品券';
					break;
				case 3:
					categoryText = '平台券';
					break;
				default:
					categoryText = '优惠券';
			}

			return `备注：${categoryText}`	
			
		},
		
		// 获取按钮文字
		getCouponBtnText(coupon) {
			if (coupon.isUse) {
				return '已领取';
			} else if (coupon.isLimited && coupon.lastTotal <= 0) {
				return '已抢完';
			} else {
				return coupon.isReceived ? '再次领取' : '立即领取';
			}
		},
		
		// 获取按钮样式类
		getCouponBtnClass(coupon) {
			if (coupon.isUse) {
				return 'received';
			} else if (coupon.isLimited && coupon.lastTotal <= 0) {
				return 'sold-out';
			} else {
				return 'available';
			}
		},
		
		// 获取优惠券状态
		getCouponStatus(coupon) {
			if (coupon.isUse) {
				return 'received';
			} else if (coupon.isLimited && coupon.lastTotal <= 0) {
				return 'sold-out';
			} else {
				return 'available';
			}
		},
		
		// 处理优惠券操作
		handleCouponAction(coupon) {
			if (coupon.isUse) {
				this.$util.Tips({
					title: '已领取过该优惠券',
					icon: 'none'
				});
				return;
			}

			if (coupon.isLimited && coupon.lastTotal <= 0) {
				this.$util.Tips({
					title: '优惠券已领完',
					icon: 'none'
				});
				return;
			}

			// 调用领取优惠券接口
			this.receiveCouponAction(coupon);
		},

		// 领取优惠券
		receiveCouponAction(coupon) {
			// 显示加载状态
			uni.showLoading({
				title: '领取中...',
				mask: true
			});

			receiveCoupon(coupon.id).then(res => {
				uni.hideLoading();

				if (res.code === 200) {
					// 领取成功
					this.$util.Tips({
						title: '领取成功',
						icon: 'success'
					});

					// 更新优惠券状态
					coupon.isUse = true;
					coupon.btnText = '已领取';
					coupon.btnClass = 'received';
					coupon.status = 'received';

					// 如果有库存限制，减少剩余数量
					if (coupon.isLimited && coupon.lastTotal > 0) {
						coupon.lastTotal--;
					}
				} else {
					// 领取失败
					this.$util.Tips({
						title: res.message || '领取失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				uni.hideLoading();
				console.error('领取优惠券失败:', err);

				// 根据错误信息显示不同提示
				let errorMsg = '领取失败，请稍后重试';
				if (err.message) {
					if (err.message.includes('已领取')) {
						errorMsg = '您已领取过该优惠券';
					} else if (err.message.includes('库存不足') || err.message.includes('已抢完')) {
						errorMsg = '优惠券已被抢完';
					} else if (err.message.includes('登录')) {
						errorMsg = '请先登录';
					}
				}

				this.$util.Tips({
					title: errorMsg,
					icon: 'none'
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.coupon-center {
	min-height: 100vh;
	background: #f5f5f5;
	padding-top: 0;
}

/* 自定义导航栏 */
.custom-nav {
	background: #fff;
	border-bottom: 1rpx solid #e5e5e5;
}

.nav-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-left .iconfont {
	font-size: 36rpx;
	color: #333;
}

.nav-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.nav-right {
	width: 60rpx;
}

/* 优惠券列表 */
.coupon-list {
	padding: 32rpx 24rpx;
}

.coupon-item {
	position: relative;
	margin-bottom: 32rpx;
	height: 200rpx;
}

.coupon-main {
	display: flex;
	background: #fff;
	height: 100%;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
}

.coupon-left {
	flex: 1;
	display: flex;
	padding: 32rpx 28rpx;
	align-items: center;
}

.coupon-image {
	width: 110rpx;
	height: 110rpx;
	border-radius: 12rpx;
	overflow: hidden;
	margin-right: 24rpx;
	flex-shrink: 0;
	background: #f8f8f8;
	border: 1rpx solid #eeeeee;
}

.coupon-image image {
	width: 100%;
	height: 100%;
}

.coupon-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.coupon-title {
	font-size: 30rpx;
	color: #333333;
	font-weight: 600;
	line-height: 1.4;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
}

.coupon-time {
	font-size: 22rpx;
	color: #FF2D18;
	margin-bottom: 30rpx;
	font-weight: 500;
}

.coupon-desc {
	font-size: 22rpx;
	color: #888888;
	font-weight: 400;
}

.coupon-right {
	width: 220rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #FF5400;
	position: relative;
}

.coupon-right.received,
.coupon-right.sold-out {
	background: linear-gradient(135deg, #CCCCCC 0%, #B8B8B8 100%);
}

.coupon-value {
	text-align: center;
	margin-bottom: 20rpx;
}

.discount {
	font-size: 56rpx;
	font-weight: 700;
	color: #fff;
	line-height: 1;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.money {
	color: #fff;
	line-height: 1;
	display: flex;
	align-items: baseline;
	justify-content: center;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.currency {
	font-size: 28rpx;
	margin-right: 4rpx;
	font-weight: 600;
}

.money text:not(.currency) {
	font-size: 56rpx;
	font-weight: 700;
}

.condition {
	font-size: 22rpx;
	color: #fff;
	margin-top: 8rpx;
	font-weight: 400;
	opacity: 0.9;
}

.coupon-btn {
	padding: 12rpx 28rpx;
	border-radius: 28rpx;
	font-size: 26rpx;
	text-align: center;
	min-width: 140rpx;
	font-weight: 600;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.coupon-btn.available {
	background: #fff;
	color: #FF5400;
}

.coupon-btn.received {
	background: rgba(255, 255, 255, 0.3);
	color: #fff;
}

.coupon-btn.sold-out {
	background: rgba(255, 255, 255, 0.3);
	color: #fff;
}

/* 左右半圆缺口 */
.circle-left,
.circle-right {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	width: 24rpx;
	height: 24rpx;
	background: #f5f5f5;
	border-radius: 50%;
	z-index: 3;
}

.circle-left {
	left: -12rpx;
}

.circle-right {
	right: -12rpx;
}

/* 上下半圆缺口 - 位于白色和橙色交界处 */
.circle-top,
.circle-bottom {
	position: absolute;
	left: calc(100% - 220rpx - 10rpx);
	width: 24rpx;
	height: 24rpx;
	background: #f5f5f5;
	border-radius: 50%;
	z-index: 3;
}

.circle-top {
	top: -12rpx;
}

.circle-bottom {
	bottom: -12rpx;
}

/* 加载更多 */
.loading-more {
	padding: 40rpx;
	text-align: center;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

.no-more {
	padding: 40rpx;
	text-align: center;
}

.no-more-text {
	font-size: 28rpx;
	color: #999;
}
</style>
