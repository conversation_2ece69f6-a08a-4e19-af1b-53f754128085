<template>
	<view class="merchant-details-content">
		<!-- 顶部导航栏 -->
		<view class="merchant-details-top-nav-box" :style="{backgroundColor: isScroll ? '#fff' : 'transparent'}">
			<view :style=" {height:statusBarHeight+'px'}"></view>
			<view class="merchant-details-top-nav-title"
				:style="{height:titleBarHeight+'px','line-height':titleBarHeight+'px'}">
				<uni-icons color="#222222" type="back" size="30" @click="goBack"></uni-icons>
				<view class="merchant-details-top-nav-title-text">店铺详情</view>
			</view>
		</view>
		<view class="merchant-details-header-box">
			<!-- 占位 -->
			<view :style="{height:headerHeight+'px'}"></view>
			<!-- 顶部商户信息 -->
			<view class="merchant-details-header-main-box">
				<!-- <view class="merchant-details-header-img"></view> -->
				<image class="merchant-details-header-img" :src="merchantDetail.avatar" mode="aspectFit"></image>
				<view class="merchant-details-header-name">{{merchantDetail.name}}</view>
				<view v-if="merchantDetail.isCollect" class="merchant-details-header-concern"
					:style="{backgroundColor:'#ffffff' }" @click="cancelConcernFn">
					<view class="concern-title" :style="{color: '#666666'}">已关注</view>
					<view class="concern-num">{{merchantDetail.followerNum}}人关注</view>
				</view>
				<view v-else class="merchant-details-header-concern" :style="{backgroundColor:'#2b2a29'}" @click="concernFn">
					<view class="concern-title" :style="{color:'#ffffff'}">
						<text>+</text> 关注
					</view>
				</view>
			</view>
		</view>
		<view class="merchant-details-main-box">
			<view class="merchant-details-main">
				<view class="merchant-details-intro">
					<view class="merchant-details-intro-main-box">
						<view class="merchant-details-intro-item-box">
							<view class="intro-item-title">店铺评级</view>
							<uni-rate :readonly="true" :value="merchantDetail.starLevel" color="#cccccc" active-color="#ff8125" />
						</view>
						<view class="merchant-details-intro-item-box">
							<view class="intro-item-title">店铺简介</view>
							<view class="intro-item-text">{{merchantDetail.intro}}</view>
						</view>
						<view class="merchant-details-intro-item-box">
							<view class="intro-item-title">店铺地址</view>
							<view class="intro-item-text">{{merchantDetail.addressDetail}}</view>
						</view>
						<view class="merchant-details-intro-item-box">
							<view class="intro-item-title">开店时间</view>
							<view class="intro-item-text">{{merchantDetail.createTime}}</view>
						</view>
					</view>
				</view>

				<view class="merchant-details-service" @click="goToservice">
					<view class="service-title">联系客服</view>
					<uni-icons color="#999999" type="forward" size="20"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getMerchantDetailApi,
		addCollectMerchantApi,
		cancelCollectMerchantApi,
	} from '@/api/home.js'
	export default {
		data() {
			return {
				menuButtonInfo: null, // 胶囊按钮信息
				statusBarHeight: 0, // 状态栏高度
				titleBarHeight: 0, // 标题栏高度
				headerHeight: 0, // 高度占位
				merchantId: '', // 商户id
				merchantDetail: {}
			};
		},
		onLoad(options) {
			// 获取状态栏高度
			// const info = uni.getSystemInfoSync() // 获取设备信息
			const info = uni.getWindowInfo() // 获取设备信息
			// console.log('info', info);
			this.statusBarHeight = info.statusBarHeight
			// 获取胶囊按钮信息(width, height, top等)
			const menuButton = uni.getMenuButtonBoundingClientRect()
			// console.log('menuButton', menuButton);
			this.menuButtonInfo = menuButton
			// 胶囊按钮相对于导航栏的上边距
			const topDistance = this.menuButtonInfo.top - this.statusBarHeight
			// 计算导航栏高度
			this.titleBarHeight = this.menuButtonInfo.height + topDistance * 2
			this.headerHeight = this.titleBarHeight + this.statusBarHeight

			console.log('页面传递的信息', options);
			this.merchantId = JSON.parse(options.merchantId)
			this.getMerchantDetail()
		},
		methods: {
			// 点击返回上一页
			goBack() {
				console.log('返回上一页');
				uni.navigateBack()
			},
			// 获取店铺详细信息
			async getMerchantDetail() {
				const res = await getMerchantDetailApi(this.merchantId)
				if (res.code === 200) {
					console.log('获取店铺详细信息', res);
					this.merchantDetail = res.data
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击关注门店
			concernFn() {
				console.log('点击了关注');
				uni.showModal({
					title: '',
					content: '确定关注该门店?',
					success: async (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							const res = await addCollectMerchantApi(this.merchantId)
							if (res.code === 200) {
								uni.showToast({
									icon: 'none',
									title: '关注成功',
									duration: 2000
								});
								this.getMerchantDetail()
							} else {
								uni.showToast({
									icon: 'none',
									title: res.message,
									duration: 2000
								});
								this.getMerchantDetail()
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 点击取消关注
			cancelConcernFn() {
				console.log('点击取消关注');
				uni.showModal({
					title: '',
					content: '确定取消关注该门店?',
					success: async (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							const res = await cancelCollectMerchantApi(this.merchantId)
							if (res.code === 200) {
								uni.showToast({
									icon: 'none',
									title: '取消关注成功',
									duration: 2000
								});
								this.getMerchantDetail()
							} else {
								uni.showToast({
									icon: 'none',
									title: res.message,
									duration: 2000
								});
								this.getMerchantDetail()
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			goToservice() {
				console.log('点击前往客服');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.merchant-details-content {
		.merchant-details-top-nav-box {
			position: fixed;
			top: 0rpx;
			z-index: 1000;
			width: 100%;
			// background: #fff;

			.merchant-details-top-nav-title {
				display: flex;
				align-items: center;

				.merchant-details-top-nav-title-text {
					flex: 1;
					text-align: center;
					font-size: 34rpx;
					color: #222222;
				}
			}
		}

		.merchant-details-header-box {
			width: 750rpx;
			height: 486rpx;
			background: linear-gradient(180deg, #b8fe4e, #f5f5f5);

			.merchant-details-header-main-box {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-top: 90rpx;
				width: 100%;

				.merchant-details-header-img {
					margin-bottom: 30rpx;
					width: 140rpx;
					height: 140rpx;
					// background: #d8d8d8;
					border-radius: 16rpx;
				}

				.merchant-details-header-name {
					margin-bottom: 20rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #222222;
				}

				.merchant-details-header-concern {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 188rpx;
					height: 72rpx;
					// background: #ffffff;
					border-radius: 36rpx;

					.concern-title {
						font-size: 24rpx;
						// color: #666666;
					}

					.concern-num {
						font-size: 18rpx;
						color: #999999;
					}
				}
			}
		}

		.merchant-details-main-box {
			display: flex;
			justify-content: center;
			margin-top: 100rpx;
			width: 750rpx;

			.merchant-details-main {
				display: flex;
				flex-direction: column;
				width: 712rpx;

				.merchant-details-intro {
					display: flex;
					justify-content: center;
					width: 712rpx;
					height: 452rpx;
					background: #ffffff;
					border-radius: 16rpx;

					.merchant-details-intro-main-box {
						width: 688rpx;

						.merchant-details-intro-item-box {
							display: flex;
							justify-content: space-between;
							align-items: center;
							padding-left: 4rpx;
							padding-right: 12rpx;
							width: 688rpx;
							height: 100rpx;
							max-height: 140rpx;
							border-bottom: 2rpx solid #f5f5f5;

							&:last-child {
								border: none;
							}

							&:nth-child(3) {
								height: 140rpx;
							}

							.intro-item-title {
								font-size: 28rpx;
								color: #666666;
							}

							.intro-item-text {
								max-width: 472rpx;
								max-height: 80rpx;
								font-size: 28rpx;
								color: #333333;
								text-align: right;
								line-clamp: 2;
								overflow: hidden;
								text-overflow: ellipsis;
								text-overflow: -webkit-ellipsis-lastline;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								line-clamp: 2;
								-webkit-box-orient: vertical;
							}
						}
					}
				}

				.merchant-details-service {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 20rpx;
					padding-left: 20rpx;
					padding-right: 22rpx;
					width: 712rpx;
					height: 100rpx;
					background: #ffffff;
					border-radius: 16rpx;

					.service-title {
						font-size: 28rpx;
						color: #666666;
					}
				}
			}
		}
	}
</style>