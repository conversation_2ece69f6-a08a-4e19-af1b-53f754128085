<template>
	<view class='recommend'>
		<block v-if="hotGoods.length">
			<view v-if="isShowTitle" class='title acea-row dispalyss'>
				<view class="fangkuai">
					
				</view>
				<!-- <text class='iconfont icon-zhuangshixian'></text> -->
				<text class='name line2 tuijiantitle'>热门推荐</text>
				<view class="tuijian-remake">
					精选好物任您选
				</view>
				<!-- <text class='iconfont icon-zhuangshixian lefticon'></text> -->
			</view>
			<view class='recommendList' :class="isShowTitle?'':'mt30'">
				<WaterfallsFlow :wfList='hotGoods' :type="1" :isStore="1"/>
			</view>
			<view class='loadingicon acea-row row-center-wrapper footer' v-if="isShowLoadMore">
				<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
			</view>
		</block>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		mapGetters
	} from "vuex";
	import {
		getProductHot
	} from '@/api/product.js';
	import WaterfallsFlow from '@/components/WaterfallsFlow/WaterfallsFlow.vue'
	export default {
		name: 'recommend',
		computed: mapGetters(['uid', 'currentMerId']),
		components: {
			WaterfallsFlow
		},
		props: {
			categoryId: {
				type: Number,
				default: function() {
					return 0;
				}
			},
			isShowTitle: {
				type: Boolean,
				default: function() {
					return true;
				}
			},
			isShowLoadMore: {
				type: Boolean,
				default: function() {
					return false;
				}
			},
		},
		data() {
			return {
				merId: 0,
				params: {
					page: 1,
					limit: 10,
					cid: 0
				},
				loadend: false,
				loading: false,
				loadTitle: '加载更多',
				hotGoods: []
			};
		},
		watch: {
			currentMerId: function(newVal, oldVal) {
				if (newVal == oldVal) return;
				this.$set(this, "merId", newVal);
			},
			categoryId: function(newVal, oldVal) {
				if (newVal == oldVal) return;
				this.params.page = 1;
				this.loadend = false;
				this.loading = false;
				this.$set(this, "hotGoods", []);
				this.get_host_product();
			},
			merId: function(newVal, oldVal) {
				if (newVal == oldVal) return;
				this.params.page = 1;
				this.loadend = false;
				this.loading = false;
				this.$set(this, "hotGoods", []);
				this.get_host_product();
			}
		},
		mounted() {
			this.$set(this, "merId", this.currentMerId);
		},
		methods: {
			get_host_product: function() {
				if (this.loadend) return
				if (this.loading) return
				this.loading = true;
				this.loadTitle = '';
				
				if(this.merId > 0) {
					this.params.cid = this.categoryId;
					this.params.merId = this.merId;
					
					getProductHot(
						this.params
					).then((res) => {
						let list = res.data.list;
					
						this.loadend = list.length < this.params.limit;
						this.hotGoods = this.hotGoods.concat(list);
						this.$set(this, "hotGoods", this.hotGoods);
					
						this.params.page++;
						this.loading = false;
						this.loadTitle = this.loadend ? '已加载全部' : '加载更多';
						this.$emit('noCommodity', this.hotGoods.length);
					}).catch(err => {
						this.loading = false
						this.loadTitle = '加载更多';
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">

	.mores-txt {
		width: 100%;
		align-items: center;
		justify-content: center;
		height: 70rpx;
		color: #999;
		font-size: 24rpx;

		.iconfont {
			margin-top: 2rpx;
			font-size: 20rpx;
		}
	}

	.recommend {
		.title {
			height: 120rpx;
			line-height: 120rpx;
			font-size: 28rpx;
			color: #333333;

			.iconfont {
				font-size: 170rpx;
				color: #454545;	
			}
			.lefticon {
				transform: rotate(180deg);
			}
		}

		.name {
			// margin: 0 28rpx;
		}
	}
	.footer {
		padding: 25rpx 0 40rpx 0;
	}
	.fangkuai{
		width: 12rpx;
		height: 30rpx;
		background: #222222;
	}
	.tuijiantitle{
		margin-left: 10rpx;
		font-size: 36rpx;
		
		font-weight: 600;
		text-align: left;
		color: #333333;
		
	}
	.dispalyss{
		display: flex;
		align-items: center;
	}
	.tuijian-remake{
				margin-left: 10rpx;
		font-size: 26rpx;
		
		text-align: left;
		color: #b2b2b2;
	}
</style>
