<template>
	<view class="search-product-content">
		<!-- 顶部导航栏 -->
		<view class="search-product-top-nav-box" :style="{backgroundColor: isScroll ? '#f5f5f5' : 'transparent'}">
			<view :style=" {height:statusBarHeight+'px'}"></view>
			<view class="search-product-top-nav-title"
				:style="{height:titleBarHeight+'px','line-height':titleBarHeight+'px'}">
				<!-- <uni-icons color="#222222" type="back" size="30" @click="goBack"></uni-icons> -->
				<image class="search-product-top-back-btn" src="/pages/home/<USER>/search-back.png" mode="aspectFit"
					@click="goBack"></image>
				<view class="search-product-top-nav-title-text">商品搜索</view>
			</view>
		</view>
		<!-- 占位 -->
		<view :style="{height:headerHeight+'px'}"></view>
		<!-- 搜索结构 -->
		<view class="search-box"
			:style="[{top: headerHeight+'px'},{backgroundColor: isScroll ? '#f5f5f5' : 'transparent'}]">
			<view class="search-main-box">
				<image class="search-icon" src="/pages/home/<USER>/search-icon.png" mode="aspectFit"></image>
				<input type="text" class="search-input-box" placeholder="搜索商品或品牌名称" v-model="queryObj.name" />
				<view class="search-btn" @click="searchFn">搜索</view>
			</view>
		</view>
		<!-- 初始结构 -->
		<view class="search-hot-words-box" v-if="isHave === '0'">
			<view class="search-hot-words-header-box">
				<view class="search-hot-words-header-title">热门搜索</view>
				<image class="search-hot-words-header-icon" src="/pages/home/<USER>/search-del.png" mode="aspectFit"></image>
			</view>
			<view class="search-hot-words-list-box">
				<view class="search-hot-words-item-box" v-for="item in productHotList" :key="item.id"
					@click="clickSearchFn(item.word)">
					{{item.word}}
				</view>
			</view>
		</view>
		<!-- 无搜索结果 -->
		<view class="search-no-box" v-else-if="isHave === '1' && productList.length == 0">
			<view class="search-no-main-box">
				<view class="search-no-main-content">
					<image class="search-no-main-icon" src="/pages/home/<USER>/search-not.png" mode="aspectFit"></image>
					<view class="search-no-main-text">亲～暂无搜索结果</view>
				</view>
			</view>
			<view class="search-no-hot-recommend-box">
				<view class="search-no-hot-recommend-main">
					<image class="hot-recommend-header-box" src="/static/imgs/home/<USER>"
						mode="aspectFit"></image>
					<!--
							 value: 渲染的列表
							 column: 列数
							 maxColumn: 最大列数
							 columnSpace: 列之间的间距(单位是百分比)
							 imageKey: 列表中的图片字段的键名
							 hideImageKey: 隐藏图片字段的键名
							 seat: 自定义文字的位置,1-图片上方，2-图片下方
							 listStyle: 单个展示项的样式
							 @loaded: 图片加载完成事件
							 @wapperClick: 单项点击事件
							 @imageClick: 图片点击事件
							 -->
					<!-- 当时从首页进入的时候展示该列表 -->
					<custom-waterfalls-flow v-if="queryObj.merId == 1" ref="waterfallsFlowRef" :value="fitmentInfoObj.products"
						:column="column" :columnSpace="2.6" hideImageKey="image" :seat="2" @wapperClick="wapperClick"
						@imageClick="imageClick" @loaded="loaded">
						<view class="hot-recommend-item-box" v-for="(item,index) in fitmentInfoObj.products" :key="item.id"
							slot="slot{{index}}">
							<view class="hot-recommend-item-name">{{item.name}}</view>
							<view class="hot-recommend-item-intro">{{item.intro}}</view>
							<view class="hot-recommend-item-price-box">
								<view class="item-price">¥{{item.price}}</view>
								<view class="item-old-price">¥{{item.otPrice}}</view>
								<image v-if="item.type == '2'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
								<image v-else-if="item.type == '1'" class="item-icon"
									src="/static/imgs/home/<USER>" mode="aspectFit">
								</image>
								<image v-else-if="item.type == '3'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
							</view>
						</view>
					</custom-waterfalls-flow>
					<!-- 非首页进入显示该列表 -->
					<custom-waterfalls-flow v-else ref="waterfallsFlowRef" :value="merchantFitmentObj.merchantFitmentProducts"
						:column="column" :columnSpace="2.6" hideImageKey="image" :seat="2" @wapperClick="wapperClick"
						@imageClick="imageClick" @loaded="loaded">
						<view class="hot-recommend-item-box" v-for="(item,index) in merchantFitmentObj.merchantFitmentProducts"
							:key="item.id" slot="slot{{index}}">
							<view class="hot-recommend-item-name">{{item.name}}</view>
							<view class="hot-recommend-item-intro">{{item.intro}}</view>
							<view class="hot-recommend-item-price-box">
								<view class="item-price">¥{{item.price}}</view>
								<view class="item-old-price">¥{{item.otPrice}}</view>
								<image v-if="item.type == '2'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
								<image v-else-if="item.type == '1'" class="item-icon"
									src="/static/imgs/home/<USER>" mode="aspectFit">
								</image>
								<image v-else-if="item.type == '3'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
							</view>
						</view>
					</custom-waterfalls-flow>
				</view>
			</view>
		</view>
		<!-- 有搜索结果 -->
		<view class="search-have-box" v-else-if="isHave === '2' && productListTotal !== 0">
			<view class="search-have-fillter-box">
				<view class="fillter-item-box" :style="{color: fillterType === '1' ? '#333333' : '#666666'}"
					@click="changeFillterFn('1')">
					<view>默认</view>
				</view>
				<view class="fillter-item-box" :style="{color: fillterType === '2' ? '#333333' : '#666666'}"
					@click="changeFillterFn('2')">
					<view>销量</view>
					<image v-if="sales === 1" class="fillter-item-icon" src="/pages/home/<USER>/search-sort-top.png"
						mode="aspectFit"></image>
					<image v-else-if="sales === 2" class="fillter-item-icon" src="/pages/home/<USER>/search-sort-bottom.png"
						mode="aspectFit">
					</image>
				</view>
				<view class="fillter-item-box" :style="{color: fillterType === '3' ? '#333333' : '#666666'}"
					@click="changeFillterFn('3')">
					<view>价格</view>
					<image v-if="price === 1" class="fillter-item-icon" src="/pages/home/<USER>/search-sort-top.png"
						mode="aspectFit"></image>
					<image v-else-if="price === 2" class="fillter-item-icon" src="/pages/home/<USER>/search-sort-bottom.png"
						mode="aspectFit">
					</image>
				</view>
			</view>
			<view class="search-have-product-list-box">
				<view class="search-have-product-list-main">
					<view class="product-item-box" v-for="item in productList" :key="item.id" @click="goToDetails(item.id)">
						<image class="product-item-img" :src="item.image" mode="aspectFit"></image>
						<view class="product-item-info-box">
							<view class="product-item-name">{{item.name}}</view>
							<view class="product-item-price-box">
								<view class="product-item-new-price">￥{{item.price}}</view>
								<view class="product-item-old-price">￥{{item.otPrice}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getProductHotListApi,
		getProductListApi,
		getFitmentInfoApi,
		getMerchantFitmentApi
	} from '@/api/home.js'
	export default {
		data() {
			return {
				isHave: '0', // 判断当前是初始状态或商品列表为空或商品列表不为空, 0初始状态, 1商品列表为空, 2商品列表不为空
				menuButtonInfo: null, // 胶囊按钮信息
				statusBarHeight: 0, // 状态栏高度
				titleBarHeight: 0, // 标题栏高度
				headerHeight: 0, // 高度占位
				productHotList: [], // 搜索热词列表
				// 请求参数
				queryObj: {
					brandId: '', // 品牌id
					cid: '', // 分类id
					city: '', // 所属商户的所在市
					district: '', // 所属商户的所在县/区
					isSelf: '', // 是否自营店铺:0-自营，1-非自营
					keyword: '', // 搜索关键字
					limit: 10, // 每页数量
					maxPrice: '', // 最高价
					merId: '', // 商户id
					minPrice: '', // 最低价
					name: '', // (新)模糊搜索名称和品牌名称
					page: 1, // 页码
					priceOrder: '', // 价格排序 desc 降序(下) asc升序(上)
					productId: '', // 商品id
					productIds: '', // 商品id集合
					salesOrder: '', // 销量排序 desc 降序(下) asc升序(上)
				},
				productList: [], // 商品列表
				productListTotal: 0, // 商品列表总数
				isProductLoading: false, // 记录请求状态, true 加载中, false 加载完毕
				fitmentInfoObj: {}, // 首页信息
				storeId: '', // 商户id
				merchantFitmentObj: {}, // 商户首页信息
				isScroll: false, // 判断滚动
				fillterType: '1', // 筛选条件  1 默认  2 销量  3 价格
				sales: 0, // 销量排序 2 降序(下) 1升序(上)
				price: 0, // 价格排序 2 降序(下) 1升序(上)
			};
		},
		onLoad(options) {
			// 获取状态栏高度
			// const info = uni.getSystemInfoSync() // 获取设备信息
			const info = uni.getWindowInfo() // 获取设备信息
			// console.log('info', info);
			this.statusBarHeight = info.statusBarHeight
			// 获取胶囊按钮信息(width, height, top等)
			const menuButton = uni.getMenuButtonBoundingClientRect()
			// console.log('menuButton', menuButton);
			this.menuButtonInfo = menuButton
			// 胶囊按钮相对于导航栏的上边距
			const topDistance = this.menuButtonInfo.top - this.statusBarHeight
			// 计算导航栏高度
			this.titleBarHeight = this.menuButtonInfo.height + topDistance * 2
			this.headerHeight = this.titleBarHeight + this.statusBarHeight

			console.log('页面传递的信息', options);
			this.queryObj.merId = JSON.parse(options.merchantId)
			if (options.storeId) {
				this.storeId = JSON.parse(options.storeId)
			}
			console.log('this.queryObj', this.queryObj);
			this.getProductHotList()
		},
		onPageScroll(e) {
			// console.log('页面滚动了', e);
			if (e.scrollTop > 5) {
				this.isScroll = true
			} else {
				this.isScroll = false
			}
		},
		methods: {
			// 点击返回上一页
			goBack() {
				console.log('返回上一页');
				uni.navigateBack()
			},
			// 点击搜索
			searchFn() {
				if (this.queryObj.name === '') {
					uni.showToast({
						icon: 'none',
						duration: 2000,
						title: '请输入搜索内容'
					})
					this.isHave = '0'
					this.productList = []
					this.productListTotal = 0
				} else {
					console.log('搜索', this.queryObj);
					this.getProductList()
				}
			},
			// 获取搜索热词列表
			async getProductHotList() {
				const res = await getProductHotListApi({
					number: 8
				})
				if (res.code === 200) {
					console.log('获取搜索热词列表', res);
					this.productHotList = res.data
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击热搜词进行搜索
			clickSearchFn(name) {
				this.queryObj.name = name
				console.log('点击热搜词进行搜索', this.queryObj);
				this.getProductList()
			},
			// 获取搜索列表
			async getProductList() {
				this.isProductLoading = true
				const res = await getProductListApi(this.queryObj)
				if (res.code === 200) {
					console.log('获取搜索列表', res);
					this.productList = [...this.productList, ...res.data.list]
					this.productListTotal = res.data.total
					this.isProductLoading = false
					console.log('this.productList', this.productList);
					// 当搜索的商品列表为空时显示商品推荐
					if (this.productList.length == 0) {
						// 当是从首页进入的时候展示首页推荐列表
						if (this.queryObj.merId == 1) {
							this.isHave = '1'
							this.getFitmentInfo()
						} else {
							// 非首页进入显示商户推荐列表
							this.isHave = '1'
							this.getMerchantFitment()
						}
					} else {
						this.fillterType = '1'
						this.isHave = '2'
					}
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击筛选条件
			changeFillterFn(type) {
				console.log('点击了筛选', type);
				if (type === '1') {
					this.fillterType = type
					this.queryObj.page = 1
					this.queryObj.priceOrder = ''
					this.queryObj.salesOrder = ''
					this.sales = 0
					this.price = 0
					this.productList = []
					this.getProductList()
				} else if (type === '2') {
					this.fillterType = type
					this.queryObj.page = 1
					this.price = 0
					if (this.sales === 0 || this.sales === 2) {
						this.sales = 1
						this.queryObj.salesOrder = 'asc'
						this.queryObj.priceOrder = ''
						this.productList = []
						this.getProductList()
					} else if (this.sales === 1) {
						this.sales = 2
						this.queryObj.salesOrder = 'desc'
						this.queryObj.priceOrder = ''
						this.productList = []
						this.getProductList()
					}
				} else if (type === '3') {
					this.fillterType = type
					this.queryObj.page = 1
					this.sales = 0
					if (this.price === 0 || this.price === 2) {
						this.price = 1
						this.queryObj.priceOrder = 'asc'
						this.queryObj.salesOrder = ''
						this.productList = []
						this.getProductList()
					} else if (this.price === 1) {
						this.price = 2
						this.queryObj.priceOrder = 'desc'
						this.queryObj.salesOrder = ''
						this.productList = []
						this.getProductList()
					}
				}
			},
			// 触底加载下一页
			onReachBottom() {
				console.log('触底了');
				if (this.isHave === '2') {
					if (this.isProductLoading) return
					this.queryObj.page++
					this.getProductList()
					if (this.queryObj.page * this.queryObj.limit >= this.productListTotal) return uni.showToast({
						icon: 'none',
						duration: 2000,
						title: '数据加载完成'
					})
				} else {
					return
				}
			},
			// 获取首页数据
			async getFitmentInfo() {
				const res = await getFitmentInfoApi()
				if (res.code === 200) {
					this.fitmentInfoObj = res.data
					console.log('首页数据', this.fitmentInfoObj);
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 获取商户首页装修信息
			async getMerchantFitment() {
				const res = await getMerchantFitmentApi({
					id: this.queryObj.merId,
					storesId: this.storeId
				})
				// console.log('获取商户首页装修信息', res);
				if (res.code === 200) {
					this.merchantFitmentObj = res.data
					console.log('获取商户首页装修信息', this.merchantFitmentObj);
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 瀑布流加载完成
			loaded() {
				console.log('瀑布流加载完成')
			},
			// 瀑布流单项点击事件
			wapperClick(item) {
				console.log('瀑布流单项点击事件', item);
				if (this.queryObj.merId == 1) {
					uni.setStorageSync('storeId', 1)
					uni.navigateTo({
						url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=${this.queryObj.merId}`
					})
				} else {
					uni.setStorageSync('storeId', this.storeId)
					uni.navigateTo({
						url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=${this.queryObj.merId}`
					})
				}
			},
			// 瀑布流图片点击事件
			imageClick(item) {
				console.log('瀑布流图片点击事件', item);
				if (this.queryObj.merId == 1) {
					uni.setStorageSync('storeId', 1)
					uni.navigateTo({
						url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=${this.queryObj.merId}`
					})
				} else {
					uni.setStorageSync('storeId', this.storeId)
					uni.navigateTo({
						url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=${this.queryObj.merId}`
					})
				}
			},
			// 点击进入商品详情页
			goToDetails(id) {
				if (this.queryObj.merId == 1) {
					uni.setStorageSync('storeId', 1)
					uni.navigateTo({
						url: `/pages/goods/goods_details/index?id=${id}`
					})
				} else {
					uni.setStorageSync('storeId', this.storeId)
					uni.navigateTo({
						url: `/pages/goods/goods_details/index?id=${id}`
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	.search-product-content {
		display: flex;
		flex-direction: column;
		min-height: 100vh;

		.search-product-top-nav-box {
			position: fixed;
			top: 0rpx;
			z-index: 1000;
			width: 100%;
			// background: #fff;

			.search-product-top-nav-title {
				display: flex;
				align-items: center;

				.search-product-top-back-btn {
					width: 40rpx;
					height: 40rpx;
				}

				.search-product-top-nav-title-text {
					flex: 1;
					text-align: center;
					font-size: 34rpx;
					font-weight: 500;
					color: #222222;
				}
			}
		}

		.search-box {
			position: fixed;
			// top: ;
			display: flex;
			justify-content: center;
			padding-top: 30rpx;
			padding-bottom: 10rpx;
			width: 750rpx;

			.search-main-box {
				display: flex;
				align-items: center;
				padding-left: 20rpx;
				padding-right: 10rpx;
				width: 710rpx;
				height: 68rpx;
				background: #ffffff;
				border: 4rpx solid #222222;
				border-radius: 8rpx;

				.search-icon {
					margin-right: 10rpx;
					width: 30rpx;
					height: 30rpx;
				}

				.search-input-box {
					flex: 1;
					font-size: 26rpx;
					color: #222222;
				}

				.search-btn {
					margin-left: 10rpx;
					width: 96rpx;
					height: 48rpx;
					background: #bdfd5b;
					border-radius: 12rpx;
					font-size: 24rpx;
					text-align: center;
					line-height: 48rpx;
					color: #222222;
				}
			}
		}

		.search-hot-words-box {
			flex: 1;
			margin-top: 30rpx;
			padding-top: 24rpx;
			width: 750rpx;
			background: #ffffff;
			border-radius: 30rpx 30rpx 0rpx 0rpx;

			.search-hot-words-header-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 30rpx;
				padding-left: 30rpx;
				padding-right: 20rpx;
				width: 100%;

				.search-hot-words-header-title {
					font-size: 28rpx;
					color: #222222;
				}

				.search-hot-words-header-icon {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.search-hot-words-list-box {
				display: flex;
				flex-wrap: wrap;
				padding-left: 20rpx;
				padding-right: 20rpx;
				width: 100%;

				.search-hot-words-item-box {
					margin-right: 20rpx;
					margin-bottom: 30rpx;
					padding-left: 20rpx;
					padding-right: 20rpx;
					// width: 160rpx;
					height: 62rpx;
					background: #f5f5f5;
					border-radius: 8rpx;
					line-height: 62rpx;
					font-size: 24rpx;
					color: #666666;
				}
			}
		}

		.search-no-box {
			flex: 1;
			padding-top: 148rpx;
			width: 750rpx;

			.search-no-main-box {
				display: flex;
				justify-content: center;
				margin-bottom: 40rpx;
				width: 100%;

				.search-no-main-content {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 300rpx;

					.search-no-main-icon {
						margin-bottom: 24rpx;
						width: 300rpx;
						height: 300rpx;
					}

					.search-no-main-text {
						font-size: 26rpx;
						color: #999999;
					}
				}
			}

			.search-no-hot-recommend-box {
				display: flex;
				justify-content: center;
				width: 750rpx;

				.search-no-hot-recommend-main {
					width: 690rpx;

					.hot-recommend-header-box {
						margin-bottom: 30rpx;
						width: 338rpx;
						height: 48rpx;
					}

					.hot-recommend-item-box {
						width: 100%;
						padding: 26rpx 20rpx 10rpx 20rpx;

						.hot-recommend-item-name {
							margin-bottom: 10rpx;
							width: 100%;
							font-size: 28rpx;
							font-weight: 700;
							color: #333333;
							line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							text-overflow: -webkit-ellipsis-lastline;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}

						.hot-recommend-item-intro {
							width: 100%;
							font-size: 24rpx;
							color: rgba(102, 102, 102, 0.60);
							line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							text-overflow: -webkit-ellipsis-lastline;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}

						.hot-recommend-item-price-box {
							margin-top: 10rpx;
							display: flex;
							align-items: center;
							width: 100%;

							.item-price {
								margin-right: 10rpx;
								font-size: 28rpx;
								font-weight: 500;
								color: #333333;
							}

							.item-old-price {
								font-size: 28rpx;
								text-decoration: line-through;
								color: #999999;
							}

							.item-icon {
								width: 64rpx;
								height: 24rpx;
							}
						}

						.desc {
							font-size: 24rpx;
							color: #666;
						}

					}
				}
			}
		}

		.search-have-box {
			flex: 1;
			margin-top: 108rpx;
			width: 750rpx;

			.search-have-fillter-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-left: 60rpx;
				padding-right: 62rpx;
				width: 100%;
				height: 76rpx;

				.fillter-item-box {
					display: flex;
					justify-content: center;
					align-items: center;
					font-size: 26rpx;
					color: #333333;

					.fillter-item-icon {
						width: 20rpx;
						height: 18rpx;
					}
				}
			}

			.search-have-product-list-box {
				display: flex;
				justify-content: center;
				width: 100%;

				.search-have-product-list-main {
					display: flex;
					flex-wrap: wrap;
					width: 690rpx;

					.product-item-box {
						margin-right: 18rpx;
						margin-bottom: 18rpx;
						width: 336rpx;
						height: 516rpx;
						background: #ffffff;
						border-radius: 12rpx;

						&:nth-child(2n) {
							margin-right: 0;
						}

						.product-item-img {
							margin-bottom: 20rpx;
							width: 336rpx;
							height: 336rpx;
							border-top-right-radius: 12rpx;
							border-top-left-radius: 12rpx;
						}

						.product-item-info-box {
							padding-left: 20rpx;
							padding-right: 20rpx;
							width: 336rpx;

							.product-item-name {
								margin-bottom: 20rpx;
								font-size: 28rpx;
								color: #333333;
								line-clamp: 2;
								overflow: hidden;
								text-overflow: ellipsis;
								text-overflow: -webkit-ellipsis-lastline;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								line-clamp: 2;
								-webkit-box-orient: vertical;
							}

							.product-item-price-box {
								display: flex;
								width: 336rpx;

								.product-item-new-price {
									margin-right: 10rpx;
									font-size: 28rpx;
									font-weight: 700;
									color: #333333;
								}

								.product-item-old-price {
									font-size: 28rpx;
									text-decoration: line-through;
									color: #999999;
								}
							}
						}
					}
				}
			}
		}
	}
</style>