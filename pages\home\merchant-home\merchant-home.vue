<template>
	<view class="merchant-home-content">
		<!-- 顶部导航栏 -->
		<view class="merchant-home-top-nav-box" :style="{backgroundColor: isScroll ? '#fff' : 'transparent'}">
			<view :style=" {height:statusBarHeight+'px'}"></view>
			<view class="merchant-home-top-nav-title" :style="{height:titleBarHeight+'px','line-height':titleBarHeight+'px'}">
				<uni-icons color="#222222" type="back" size="30" @click="goBack"></uni-icons>
			</view>
		</view>
		<view class="merchant-home-header-box">
			<!-- 占位 -->
			<view :style="{height:headerHeight+'px'}"></view>
			<!-- 顶部商户信息 -->
			<view class="merchant-home-header-main-box">
				<view class="merchant-home-header-main">
					<view class="merchant-home-header-info-box">
						<view class="merchant-home-header-info-left-box">
							<!-- <view class="merchant-home-header-info-img"></view> -->
							<image class="merchant-home-header-info-img" :src="merchantFitmentObj.merchantDetail.avatar"
								mode="aspectFit" @click="goTodetail">
							</image>
							<view class="merchant-home-header-info-intro">
								<view class="intro-name-box">
									<view class="intro-name-text">{{merchantFitmentObj.merchantDetail.name}}</view>
									<view class="intro-name-address" @click="chooseMerchantFn">
										<view class="address-text">
											{{ merchantFitmentObj.storesInfo.name ?  merchantFitmentObj.storesInfo.name :storeName}}
										</view>
										<image class="address-choose" src="/static/imgs/home/<USER>" mode="aspectFit"></image>
									</view>
								</view>
								<view class="intro-text-box">{{merchantFitmentObj.merchantDetail.intro}}</view>
							</view>
						</view>
						<view v-if="merchantFitmentObj.merchantDetail.isCollect" class="merchant-home-header-info-right-collect-box"
							@click="cancelConcernFn">
							<view class="collect-status">已关注</view>
							<view class="collect-num">{{merchantFitmentObj.merchantDetail.followerNum}}人关注</view>
						</view>
						<view v-else class="merchant-home-header-info-right-box" @click="concernFn">+ 关注</view>
					</view>
					<view class="merchant-home-header-search-box" @click="goSearchPage">
						<image class="search-icon-box" src="/static/imgs/home/<USER>" mode="aspectFit"></image>
						<!-- <input type="text" class="search-input-box" placeholder="高档老花镜" /> -->
						<view class="search-input-box">高档老花镜</view>
						<view class="search-btn-box">搜索</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 间隔占位 -->
		<view style="width: 100%"
			:style="[{height: `${merchantFitmentObj.spacingHeight}rpx`},{backgroundColor: merchantFitmentObj.spacingColor === 1 ? '#ffffff' : '#f5f5f5'}]">
		</view>
		<view class="merchant-home-main-box">
			<view class="merchant-home-main">
				<view class="merchant-home-swiper-box">
					<!-- 轮播图模块 -->
					<!-- <image class="merchant-home-swiper-img" src="merchantFitmentObj.merchantFitmentCarousels[0].imageUrl" mode="aspectFit"></image>-->
					<!-- 轮播图只有一张 -->
					<swiper class="merchant-home-swiper-main-box"
						:style="{height: `${merchantFitmentObj.merchantFitmentCarousels[0].adsHeight}rpx`}" circular autoplay>
						<swiper-item v-for="item in merchantFitmentObj.merchantFitmentCarousels"
							:style="{height: `${item.adsHeight}rpx`}" :key="item.id">
							<image class="merchant-home-swiper-item" :style="{height: `${item.adsHeight}rpx`}" :src="item.imageUrl"
								mode="aspectFill" @click="goLink(item.linkUrl)"></image>
						</swiper-item>
					</swiper>
				</view>
				<!-- 间隔占位 -->
				<view style="width: 100%"
					:style="[{height: `${merchantFitmentObj.spacingHeight}rpx`},{backgroundColor: merchantFitmentObj.spacingColor === 1 ? '#ffffff' : '#f5f5f5'}]">
				</view>
				<view class="merchant-home-nav-box">
					<view class="merchant-home-nav-list-box">
						<view class="merchant-home-nav-item-box" v-for="item in merchantFitmentObj.oneFunctions" :key="item.id"
							@click="goToPage(item.linkUrl)">
							<image class="nav-item-icon" :src="item.iconUrl" mode="aspectFit"></image>
							<view class="nav-item-text">{{item.name}}</view>
						</view>
					</view>
					<view class="merchant-home-nav-list-box">
						<view class="merchant-home-nav-item-box" v-for="item in merchantFitmentObj. twoFunctions" :key="item.id"
							@click="goToPage(item.linkUrl)">
							<image class="nav-item-icon" :src="item.iconUrl" mode="aspectFit"></image>
							<view class="nav-item-text">{{item.name}}</view>
						</view>
					</view>
				</view>
				<!-- 间隔占位 -->
				<view style="width: 100%"
					:style="[{height: `${merchantFitmentObj.spacingHeight}rpx`},{backgroundColor: merchantFitmentObj.spacingColor === 1 ? '#ffffff' : '#f5f5f5'}]">
				</view>
				<view class="merchant-home-hot-recommend-box">
					<image class="hot-recommend-header-box" src="/static/imgs/home/<USER>"
						mode="aspectFit"></image>
					<!--
							 value: 渲染的列表
							 column: 列数
							 maxColumn: 最大列数
							 columnSpace: 列之间的间距(单位是百分比)
							 imageKey: 列表中的图片字段的键名
							 hideImageKey: 隐藏图片字段的键名
							 seat: 自定义文字的位置,1-图片上方，2-图片下方
							 listStyle: 单个展示项的样式
							 @loaded: 图片加载完成事件
							 @wapperClick: 单项点击事件
							 @imageClick: 图片点击事件
							 -->
					<custom-waterfalls-flow ref="waterfallsFlowRef" :value="merchantFitmentObj.merchantFitmentProducts"
						:column="column" :columnSpace="2.6" hideImageKey="image" :seat="2" @wapperClick="wapperClick"
						@imageClick="imageClick" @loaded="loaded">
						<view class="hot-recommend-item-box" v-for="(item,index) in merchantFitmentObj.merchantFitmentProducts"
							:key="item.id" slot="slot{{index}}">
							<view class="hot-recommend-item-name">{{item.name}}</view>
							<view class="hot-recommend-item-intro">{{item.intro}}</view>
							<view class="hot-recommend-item-price-box">
								<view class="item-price">¥{{item.price}}</view>
								<view class="item-old-price">¥{{item.otPrice}}</view>
								<image v-if="item.type == '2'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
								<image v-else-if="item.type == '1'" class="item-icon"
									src="/static/imgs/home/<USER>" mode="aspectFit">
								</image>
								<image v-else-if="item.type == '3'" class="item-icon" src="/static/imgs/home/<USER>"
									mode="aspectFit">
								</image>
							</view>
						</view>
					</custom-waterfalls-flow>
				</view>
			</view>
		</view>
		<!-- 底部导航栏占位 -->
		<view class="merchant-bottom-nav-placeholder-box"></view>
		<!-- 底部导航栏 -->
		<view class="merchant-bottom-nav-box">
			<view class="merchant-bottom-nav-item-box">
				<image class="bottom-nav-item-icon" src="/pages/home/<USER>/home-select.png" mode="aspectFit"></image>
				<view class="bottom-nav-item-title-select">首页</view>
			</view>
			<view class="merchant-bottom-nav-item-box" @click="goMerchantNav('/pages/home/<USER>/merchant-class')">
				<image class="bottom-nav-item-icon" src="/pages/home/<USER>/classify.png" mode="aspectFit"></image>
				<view class="bottom-nav-item-title">分类</view>
			</view>
			<view class="merchant-bottom-nav-item-box" @click="goToNav('/pages/order_addcart/order_addcart')">
				<image class="bottom-nav-item-icon" src="/pages/home/<USER>/cart.png" mode="aspectFit"></image>
				<view class="bottom-nav-item-title">购物车</view>
			</view>
			<view class="merchant-bottom-nav-item-box" @click="goToNav('/pages/user/index')">
				<image class="bottom-nav-item-icon" src="/pages/home/<USER>/my.png" mode="aspectFit"></image>
				<view class="bottom-nav-item-title">我的</view>
			</view>
		</view>
		<!-- 选择门店弹层 -->
		<uni-popup ref="storePopup" type="bottom" border-radius="15px 15px 0 0" :is-mask-click="false"
			background-color="#fff">
			<view class="merchant-popup-content">
				<view class="merchant-popup-header-box">
					<view class="merchant-popup-header-title">选择门店</view>
					<image class="merchant-popup-header-close" src="/static/imgs/home/<USER>"
						mode="aspectFit" @click="closeStorePopup"></image>
				</view>
				<view class="merchant-popup-search-box">
					<view class="merchant-popup-search-main">
						<image class="merchant-popup-search-icon" src="/static/imgs/home/<USER>"
							mode="aspectFit"></image>
						<input type="text" class="merchant-popup-search-input" placeholder="请输入门店名称" v-model="storeQueryObj.name" />
						<view class="merchant-popup-search-btn" @click="searchStore">搜索</view>
					</view>
				</view>
				<view class="merchant-popup-list-box">
					<view class="merchant-popup-list-main">
						<scroll-view :scroll-top="0" scroll-y="true" class="merchant-popup-list-scroll-box"
							style="overflow: hidden;" @scrolltolower="lowerStoreFn">
							<view class="merchant-popup-list">
								<radio-group @change="storeRadioChange">
									<label class="merchant-item-box" v-for="(item, index) in storeList" :key="item.id">
										<view class="merchant-item-radio">
											<radio :value="item.id" :checked="index === storeListCurrent" activeBackgroundColor="#bdfd5b"
												iconColor="#000000" />
										</view>
										<image class="merchant-item-img" :src="item.avatar" mode="aspectFit"></image>
										<view class="merchant-item-info">
											<view class="merchant-item-name">{{item.name}}</view>
											<view class="merchant-item-address">{{item.addressDetail}}</view>
											<view class="merchant-item-distance" v-if="item.distance">{{item.distance}}米以内</view>
										</view>
									</label>
								</radio-group>
							</view>
						</scroll-view>
					</view>
				</view>
				<view class="merchant-popup-btn-box">
					<view class="merchant-popup-btn" @click="confirmStoreFn">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		getMerchantFitmentApi,
		addCollectMerchantApi,
		cancelCollectMerchantApi,
		getStoresHomePageApi
	} from '@/api/home.js'
	export default {
		data() {
			return {
				merchantId: '', // 商户id
				storeId: '', // 门店id
				storeName: '', // 门店名称
				merchantFitmentObj: {}, // 商户首页装修信息
				storeQueryObj: {
					latitude: '', // 纬度-传经纬度时根据距离排序
					limit: 10, // 每页数量
					longitude: '', // 经度-传经纬度时根据距离排序
					merId: '', // 商户id
					name: '', // 门店名称模糊
					page: 1, // 页码
					typeList: '' //配送方式筛选集合，1配送2自提3快递
				},
				storeList: [], // 门店分页列表
				storeListTotal: 0, // 门店分页列表总数
				isStoreListLoading: false, // 门店信息分页列表状态
				storeListCurrent: null, // 门店信息分页列表单选配置
				isScroll: false, // 判断滚动
				menuButtonInfo: null, // 胶囊按钮信息
				statusBarHeight: 0, // 状态栏高度
				titleBarHeight: 0, // 标题栏高度
				headerHeight: 0, // 高度占位
				column: 2, // 瀑布流列数
			};
		},
		onLoad(options) {
			// 获取状态栏高度
			// const info = uni.getSystemInfoSync() // 获取设备信息
			const info = uni.getWindowInfo() // 获取设备信息
			// console.log('info', info);
			this.statusBarHeight = info.statusBarHeight
			// 获取胶囊按钮信息(width, height, top等)
			const menuButton = uni.getMenuButtonBoundingClientRect()
			// console.log('menuButton', menuButton);
			this.menuButtonInfo = menuButton
			// 胶囊按钮相对于导航栏的上边距
			const topDistance = this.menuButtonInfo.top - this.statusBarHeight
			// 计算导航栏高度
			this.titleBarHeight = this.menuButtonInfo.height + topDistance * 2
			this.headerHeight = this.titleBarHeight + this.statusBarHeight

			console.log('页面传递的信息', JSON.parse(decodeURIComponent(options.obj)));
			const obj = JSON.parse(decodeURIComponent(options.obj))
			console.log('obj', obj);
			this.merchantId = obj.merchantId
			this.storeId = obj.storeId || uni.getStorageSync('storeId')
			console.log('this.storeId', this.storeId);
			if (this.storeId == '') {
				this.storeName = '请选择门店'
			}
			console.log('this.storeName', this.storeName);
			// this.storeName = obj.storeName
			this.getMerchantFitment(this.merchantId, this.storeId)
		},
		onPageScroll(e) {
			// console.log('页面滚动了', e);
			if (e.scrollTop > 80) {
				this.isScroll = true
			} else {
				this.isScroll = false
			}
		},
		methods: {
			// 点击返回上一级
			goBack() {
				console.log('返回上一级');
				uni.navigateBack()
			},
			// 点击前往店铺详情
			goTodetail() {
				uni.navigateTo({
					url: `/pages/home/<USER>/merchant-details?merchantId=${JSON.stringify(this.merchantFitmentObj.merchantDetail.id)}`
				})
			},
			// 获取商户首页装修信息
			async getMerchantFitment(merchantId, storeId) {
				const res = await getMerchantFitmentApi({
					id: merchantId,
					storesId: storeId
				})
				// console.log('获取商户首页装修信息', res);
				if (res.code === 200) {
					this.merchantFitmentObj = res.data
					console.log('获取商户首页装修信息', this.merchantFitmentObj);
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击选择门店
			chooseMerchantFn() {
				console.log('点击选择门店');
				this.storeQueryObj.merId = this.merchantFitmentObj.merchantDetail.id
				this.storeList = []
				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
						this.storeQueryObj.latitude = res.latitude
						this.storeQueryObj.longitude = res.longitude
						this.$refs.storePopup.open()
						this.getStoresHomePage()
					},
					fail: (err) => {
						console.log(err);
						uni.showToast({
							icon: 'none',
							title: '请授权获取地理位置',
							duration: 2000
						});
					}
				});
			},
			// 点击关注门店
			concernFn() {
				console.log('点击了关注');
				uni.showModal({
					title: '',
					content: '确定关注该门店?',
					success: async (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							const res = await addCollectMerchantApi(this.merchantFitmentObj.merchantDetail.id)
							if (res.code === 200) {
								uni.showToast({
									icon: 'none',
									title: '关注成功',
									duration: 2000
								});
								this.getMerchantFitment(this.merchantId, this.storeId)
							} else {
								uni.showToast({
									icon: 'none',
									title: res.message,
									duration: 2000
								});
								this.getMerchantFitment(this.merchantId, this.storeId)
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 点击取消关注
			cancelConcernFn() {
				console.log('点击取消关注');
				uni.showModal({
					title: '',
					content: '确定取消关注该门店?',
					success: async (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							const res = await cancelCollectMerchantApi(this.merchantFitmentObj.merchantDetail.id)
							if (res.code === 200) {
								uni.showToast({
									icon: 'none',
									title: '取消关注成功',
									duration: 2000
								});
								this.getMerchantFitment(this.merchantId, this.storeId)
							} else {
								uni.showToast({
									icon: 'none',
									title: res.message,
									duration: 2000
								});
								this.getMerchantFitment(this.merchantId, this.storeId)
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 点击前往搜索页
			goSearchPage() {
				console.log('点击前往搜索页');
				uni.navigateTo({
					url: `/pages/home/<USER>/search-product?merchantId=${JSON.stringify(this.merchantId)}&storeId=${JSON.stringify(this.storeId)}`
				})
			},
			// 点击轮播图跳转链接
			goLink(link) {
				console.log('点击轮播图跳转链接', link);
				uni.navigateTo({
					url: link
				})
			},
			// 点击前往不同页面
			goToPage(link) {
				console.log('点击前往不同页面', link);
				uni.navigateTo({
					url: link
				})
			},
			// 瀑布流加载完成
			loaded() {
				console.log('加载完成')
			},
			// 瀑布流单项点击事件
			wapperClick(item) {
				console.log('瀑布流单项点击事件', item);
				uni.navigateTo({
					url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=${this.merchantFitmentObj.storesInfo.merId}`
				})
			},
			// 瀑布流图片点击事件
			imageClick(item) {
				console.log('瀑布流图片点击事件', item);
				uni.navigateTo({
					url: `/pages/goods/goods_details/index?id=${item.id}&merchantid=${this.merchantFitmentObj.storesInfo.merId}`
				})
			},
			// 获取门店信息分页列表getStoresHomePageApi
			async getStoresHomePage() {
				const res = await getStoresHomePageApi(this.storeQueryObj)
				if (res.code === 200) {
					this.isStoreListLoading = false
					console.log('门店信息分页列表', res);
					this.storeList = [...this.storeList, ...res.data.list]
					this.storeListTotal = res.data.total
				} else {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 2000
					});
				}
			},
			// 点击关闭选择门店
			closeStorePopup() {
				console.log('关闭选择门店弹层');
				this.$refs.storePopup.close()
			},
			// 点击搜索门店, 获取门店列表
			searchStore() {
				console.log('搜索门店', this.storeQueryObj);
				// if (this.queryObj.name == '') {
				// 	uni.showToast({
				// 		icon: 'none',
				// 		title: '请输入搜索内容',
				// 		duration: 2000
				// 	})
				// } else {
				this.storeList = []
				this.getStoresHomePage()
				// }
			},
			// 门店列表滚动到了底部
			lowerStoreFn(e) {
				console.log('滚动到底部', e)
				if (this.isStoreListLoading) return
				this.storeQueryObj.page++
				this.getMerchantHomePage()
				if (this.storeQueryObj.page * this.storeQueryObj.limit >= this.storeListTotal) {
					return uni.showToast({
						icon: 'none',
						title: '数据加载完毕',
						duration: 2000
					});
				}
			},
			// 点击选择门店
			storeRadioChange(evt) {
				console.log('点击选择商户evt', evt);
				this.storeId = evt.detail.value
				uni.setStorageSync('storeId', this.storeId)
				for (let i = 0; i < this.storeList.length; i++) {
					if (String(this.storeList[i].id) === evt.detail.value) {
						this.storeListCurrent = i;
						console.log('this.storeList[i]', this.storeList[i]);
						// this.storeName = this.storeList[i].name
						this.storeId = this.storeList[i].id
						break;
					}
				}
			},
			confirmStoreFn() {
				console.log('点击了确定选择门店');
				this.$refs.storePopup.close()
				this.getMerchantFitment(this.merchantId, this.storeId)
			},
			// 点击切换页面
			goToNav(url) {
				uni.switchTab({
					url: url
				})
			},
			// 点击前往商户分类页面
			goMerchantNav(url) {
				console.log('url00000');
				uni.navigateTo({
					url: `/pages/home/<USER>/merchant-class?merchantId=${this.merchantId}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.merchant-home-content {
		.merchant-home-top-nav-box {
			position: fixed;
			top: 0rpx;
			z-index: 1000;
			width: 100%;
			// background: #fff;

			.merchant-home-top-nav-title {
				display: flex;
				align-items: center;
			}
		}

		.merchant-home-header-box {
			margin-bottom: 28rpx;
			width: 750rpx;
			height: 360rpx;
			background: linear-gradient(180deg, #b8fe4e, #f2f8e0);
			border-radius: 0rpx 16rpx 16rpx 0rpx;

			.merchant-home-header-main-box {
				display: flex;
				justify-content: center;
				margin-top: 30rpx;
				width: 100%;

				.merchant-home-header-main {
					width: 690rpx;

					.merchant-home-header-info-box {
						display: flex;
						// justify-content: space-between;
						align-items: center;
						margin-bottom: 30rpx;
						width: 690rpx;

						.merchant-home-header-info-left-box {
							display: flex;
							align-items: center;
							flex: 1;

							.merchant-home-header-info-img {
								margin-right: 20rpx;
								width: 80rpx;
								height: 80rpx;
								background: #ffffff;
								border-radius: 16rpx;
							}

							.merchant-home-header-info-intro {
								flex: 1;

								.intro-name-box {
									display: flex;
									align-items: center;

									.intro-name-text {
										margin-right: 5rpx;
										font-size: 34rpx;
										font-weight: 700;
										color: #222222;
									}

									.intro-name-address {
										display: flex;
										align-items: flex-end;
										padding: 0 4rpx;
										// width: 130rpx;
										// height: 34rpx;
										border: 1rpx solid #222222;
										border-radius: 4rpx;

										.address-text {
											font-size: 24rpx;
											color: #000000;
										}

										.address-choose {
											margin-bottom: 6rpx;
											margin-left: 5rpx;
											width: 12rpx;
											height: 12rpx;
										}
									}
								}

								.intro-text-box {
									font-size: 24rpx;
									color: #000000;
								}
							}
						}

						.merchant-home-header-info-right-box {
							margin-left: 20rpx;
							width: 100rpx;
							height: 56rpx;
							background: #2b2a29;
							border-radius: 16rpx;
							text-align: center;
							line-height: 56rpx;
							font-size: 24rpx;
							color: #ffffff;
						}

						.merchant-home-header-info-right-collect-box {
							padding: 10rpx;
							margin-left: 20rpx;
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							// width: 100rpx;
							// height: 56rpx;
							background: #fff;
							border-radius: 16rpx;

							.collect-status {
								font-size: 24rpx;
								color: #666666;
							}

							.collect-num {
								font-size: 18rpx;
								color: #999999;
							}
						}
					}

					.merchant-home-header-search-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 0 20rpx;
						width: 690rpx;
						height: 76rpx;
						background: #ffffff;
						// border-radius: 20rpx;
						border-radius: 36rpx;

						.search-icon-box {
							margin-right: 10rpx;
							width: 36rpx;
							height: 36rpx;
						}

						.search-input-box {
							flex: 1;
							font-size: 28rpx;
							color: #B2B2B2;
						}

						.search-btn-box {
							width: 100rpx;
							height: 56rpx;
							background: #cefd37;
							border-radius: 16rpx;
							font-size: 26rpx;
							line-height: 56rpx;
							text-align: center;
							color: #222222;
						}
					}
				}
			}
		}

		.merchant-home-main-box {
			display: flex;
			justify-content: center;
			// padding-top: 60rpx;
			width: 750rpx;

			.merchant-home-main {
				width: 690rpx;

				.merchant-home-swiper-box {
					// margin-bottom: 28rpx;
					width: 690rpx;
					// height: 260rpx;

					.merchant-home-swiper-main-box {
						width: 690rpx;
						// height: 260rpx;

						.merchant-home-swiper-item {
							width: 690rpx;
							height: 260rpx;
							border-radius: 36rpx;
							// background-color: #b8fe4e;
							// text-align: center;
							// line-height: 260rpx;
						}
					}

					.merchant-home-swiper-img {
						width: 690rpx;
						height: 260rpx;
					}
				}

				.merchant-home-nav-box {

					padding-top: 30rpx;
					padding-bottom: 30rpx;
					// margin-bottom: 20rpx;
					width: 690rpx;
					// height: 202rpx;
					background: #ffffff;
					border-radius: 36rpx;

					.merchant-home-nav-list-box {
						display: flex;
						justify-content: space-around;
						align-items: center;
						margin-bottom: 10rpx;
						width: 100%;

						&:last-child {
							margin-bottom: 0;
						}

						.merchant-home-nav-item-box {
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;

							.nav-item-icon {
								margin-bottom: 6rpx;
								width: 88rpx;
								height: 88rpx;
							}

							.nav-item-text {
								font-size: 24rpx;
								color: #666666;
							}
						}
					}

				}

				.merchant-home-hot-recommend-box {
					width: 690rpx;

					.hot-recommend-header-box {
						margin-bottom: 30rpx;
						width: 338rpx;
						height: 48rpx;
					}

					.hot-recommend-item-box {
						width: 100%;
						padding: 26rpx 20rpx 10rpx 20rpx;

						.hot-recommend-item-name {
							margin-bottom: 10rpx;
							width: 100%;
							font-size: 28rpx;
							font-weight: 700;
							color: #333333;
							line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							text-overflow: -webkit-ellipsis-lastline;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}

						.hot-recommend-item-intro {
							width: 100%;
							font-size: 24rpx;
							color: rgba(102, 102, 102, 0.60);
							line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							text-overflow: -webkit-ellipsis-lastline;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
						}

						.hot-recommend-item-price-box {
							margin-top: 10rpx;
							display: flex;
							align-items: center;
							width: 100%;

							.item-price {
								margin-right: 10rpx;
								font-size: 28rpx;
								font-weight: 500;
								color: #333333;
							}

							.item-old-price {
								font-size: 28rpx;
								text-decoration: line-through;
								color: #999999;
							}

							.item-icon {
								width: 64rpx;
								height: 24rpx;
							}
						}

						.desc {
							font-size: 24rpx;
							color: #666;
						}

					}
				}
			}
		}

		.merchant-bottom-nav-placeholder-box {
			width: 750rpx;
			height: 168rpx;
		}

		.merchant-bottom-nav-box {
			position: fixed;
			left: 0;
			bottom: 0;
			display: flex;
			justify-content: space-between;
			padding-bottom: 68rpx;
			width: 750rpx;
			height: 168rpx;
			background-color: #f5f5f5;

			.merchant-bottom-nav-item-box {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 188rpx;
				height: 100rpx;

				.bottom-nav-item-icon {
					width: 48rpx;
					height: 48rpx;
					// width: 100rpx;
					// height: 104rpx;
				}

				.bottom-nav-item-title-select {
					font-size: 20rpx;
					color: #222222;
				}

				.bottom-nav-item-title {
					font-size: 20rpx;
					color: #999999;
				}
			}
		}

		.merchant-popup-content {
			padding-top: 30rpx;
			width: 100%;
			height: 75vh;

			.merchant-popup-header-box {
				display: flex;
				align-items: center;
				margin-bottom: 28rpx;
				padding-right: 26rpx;
				width: 100%;

				.merchant-popup-header-title {
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					color: #222222;
				}

				.merchant-popup-header-close {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.merchant-popup-search-box {
				display: flex;
				justify-content: center;
				margin-bottom: 22rpx;
				width: 100%;

				.merchant-popup-search-main {
					display: flex;
					align-items: center;
					padding-left: 20rpx;
					padding-right: 18rpx;
					width: 710rpx;
					height: 76rpx;
					background: #f5f5f5;
					border-radius: 16rpx;

					.merchant-popup-search-icon {
						margin-right: 10rpx;
						width: 36rpx;
						height: 36rpx;
					}

					.merchant-popup-search-input {
						flex: 1;
						font-size: 28rpx;
						color: #222;
					}

					.merchant-popup-search-btn {
						margin-left: 10rpx;
						width: 100rpx;
						height: 56rpx;
						background: #bdfd5b;
						border-radius: 16rpx;
						text-align: center;
						line-height: 56rpx;
						font-size: 26rpx;
						color: #222222;
					}
				}
			}

			.merchant-popup-list-box {
				display: flex;
				justify-content: center;
				margin-bottom: 64rpx;
				width: 100%;

				.merchant-popup-list-main {
					width: 710rpx;

					.merchant-popup-list-scroll-box {
						width: 100%;
						height: 750rpx;
						// background-color: #bdfd5b;
						overflow: hidden;

						.merchant-popup-list {
							width: 100%;

							.merchant-item-box {
								display: flex;
								align-items: center;
								margin-bottom: 30rpx;
								width: 100%;

								&:last-child {
									margin-bottom: 0;
								}

								.merchant-item-radio {
									margin-right: 20rpx;
								}

								.merchant-item-img {
									margin-right: 20rpx;
									width: 140rpx;
									height: 140rpx;
									// background: #d8d8d8;
									border-radius: 16rpx;
								}

								.merchant-item-info {
									flex: 1;

									.merchant-item-name {
										margin-bottom: 12rpx;
										font-weight: 500;
										font-size: 28rpx;
										color: #333333;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis;
									}

									.merchant-item-address {
										margin-bottom: 6rpx;
										font-size: 24rpx;
										color: #666666;
										line-clamp: 2;
										overflow: hidden;
										text-overflow: ellipsis;
										text-overflow: -webkit-ellipsis-lastline;
										display: -webkit-box;
										-webkit-line-clamp: 2;
										line-clamp: 2;
										-webkit-box-orient: vertical;
									}

									.merchant-item-distance {
										font-size: 24rpx;
										text-align: right;
										color: #ff630c;
									}
								}
							}
						}

						.scroll-view-item {
							height: 300rpx;
							line-height: 300rpx;
							text-align: center;
							font-size: 36rpx;
						}
					}
				}
			}

			.merchant-popup-btn-box {
				display: flex;
				justify-content: center;
				width: 100%;

				.merchant-popup-btn {
					width: 690rpx;
					height: 72rpx;
					background: #bdfd5b;
					border-radius: 16rpx;
					text-align: center;
					line-height: 72rpx;
					font-size: 30rpx;
					color: #000000;
				}
			}
		}
	}
</style>