<template>
	<view class="pay_status">
		<view class="withdraw_details">
			<view>
				<view style="display: flex;"><img style="width: 36rpx;height: 36rpx;" src="/static/images/choose_check.png" alt="" /></view>
				<view style="display: flex;justify-content: center;"><view :class="status==1?'line active':'line'"></view></view>
				<view style="display: flex;"><img style="width: 36rpx;height: 36rpx;" :src="statusSrc" alt="" /></view>
			</view>
			<view style="margin-left: 40rpx;">
				<view>
					<view style="font-size: 28rpx;">提现申请已提交，等待处理</view>
					<view style="font-size: 24rpx;color: #B2B2B2;margin-top: 10rpx;">{{createTime}}</view>
				</view>
				<view style="margin-top: 60rpx;">
					<view style="font-size: 28rpx;">{{statusTitle}}</view>
					<view v-if="status==2" style="font-size: 24rpx;color: #B2B2B2;margin-top: 10rpx;">{{refusalReason}}</view>
					<view style="font-size: 24rpx;color: #B2B2B2;margin-top: 10rpx;">{{auditTime}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				status: '',
				statusSrc: '',
				statusTitle: '',
				statusDict: ['预计到账时间','提现成功','提现失败'],
				createTime: '',
				auditTime: '',
				refusalReason: ''
			}
		},
		onLoad(options) {
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#F5F5F5',
			});
			this.status = options.auditStatus||''
			this.statusSrc = options.auditStatus==1?'/static/images/choose_check.png':'/static/images/choose_noCheck.png';
			this.statusTitle = options.auditStatus?this.statusDict[options.auditStatus]:'预计到账时间';
			this.createTime = options.createTime;
			this.refusalReason = options.refusalReason;
			this.auditTime = options.auditStatus==0||!options.auditStatus?'预计7个工作日打款':options.auditTime;
		},
		methods: {
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style scoped lang="scss">
	.pay_status{
		padding: 20rpx;
	}
	.withdraw_details{
		display: flex;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx 20rpx;
	}
	.line{
		height: 106rpx;
		width: 6rpx;
		background-color: #CCCCCC;
	}
	.active{
		background-color: #BDFD5B;
	}
</style>