<template>
	<view>
		<view class='coupon-list-window' :class='coupon.coupon==true?"on":""'>
			<!-- 弹框标题 -->
			<view class="popup-header">
				<view class="popup-title">优惠券</view>
				<view class="close-btn" @click="close">
					<text class="iconfont icon-guanbi5"></text>
				</view>
			</view>

			<view class='coupon-list'>
				<block v-if="coupon.list.length">
					<view
						v-for="(item,index) in coupon.list"
						:key='index'
						class="coupon-item"
						:class="item.isUse ? 'used' : 'available'"
						@click="getCouponUser(index,item.id)"
					>
						<!-- 优惠券主体 -->
						<view class="coupon-main">
							<!-- 左侧内容 -->
							<view class="coupon-left">
								<!-- 优惠券图片 -->
								<view class="coupon-image" v-if="item.category === 2 && item.product && item.product.image">
									<image :src="item.product.image" mode="aspectFill"></image>
								</view>
								<view class="coupon-image" v-else-if="item.image">
									<image :src="item.image" mode="aspectFill"></image>
								</view>
								<view class="coupon-image default-image" v-else>
									<text class="iconfont icon-youhuiquan"></text>
								</view>

								<!-- 优惠券信息 -->
								<view class="coupon-info">
									<view class="coupon-title">
										<span class='line-title' :class='item.isUse?"gray":"select"' v-if='item.category===1'>商家券</span>
										<span class='line-title' :class='item.isUse?"gray":"select"' v-else-if='item.category===3'>平台券</span>
										<span class='line-title' :class='item.isUse?"gray":"select"' v-else>商品券</span>
										<span>{{item.name}}</span>
									</view>
									<view class="coupon-time" v-if="item.endTime">
										有效期：{{item.endTime.slice(0,10)}}
									</view>
									<view class="coupon-time" v-else-if="item.day > 0">
										领取后{{item.day}}天内可用
									</view>
									<view class="coupon-time" v-else-if="item.useStartTimeStr && item.useEndTimeStr">
										{{item.useStartTimeStr}} - {{item.useEndTimeStr}}
									</view>
								</view>
							</view>

							<!-- 右侧优惠券面值和按钮 -->
							<view class="coupon-right" :class="item.isUse ? 'used' : 'available'">
								<view class="coupon-value">
									<view class="discount" v-if="item.couponType === 2">
										{{item.discount}}<text style="font-size: 30rpx;"> 折</text>
									</view>
									<view class="money" v-else>
										<text class="currency">¥</text>
										<text>{{item.money || 0}}</text>
									</view>
									<view class="condition" v-if="item.minPrice > 0">
										满{{item.minPrice}}元
									</view>
									<view class="condition" v-else>
										无门槛
									</view>
								</view>

								<view class="coupon-btn" :class="item.isUse ? 'used' : 'available'">
									{{item.isUse ? (item.use_title || '已领取') : (coupon.statusTile || '立即领取')}}
								</view>
							</view>
						</view>
						<!-- 上方半圆缺口 -->
						<view class="circle-top"></view>
						<!-- 下方半圆缺口 -->
						<view class="circle-bottom"></view>
					</view>
				</block>
				<!-- 无优惠券 -->
				<view class='no-coupons' v-else>
					<view class='noCommodity'>
						<view class='pictrue'>
							<image src='/pages/aastatictoT/static/images/noCoupon.png'></image>
						</view>
						<view class="text">暂无优惠券哦~</view>
					</view>
				</view>
			</view>
		</view>
		<view class='mask' catchtouchmove="true" :hidden='coupon.coupon==false' @click='close'></view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		receiveCoupon
	} from '@/api/order.js';
	export default {
		props: {
			//打开状态 0=领取优惠券,1=使用优惠券
			openType: {
				type: Number,
				default: 0,
			},
			coupon: {
				type: Object,
				default: function() {
					return {};
				}
			},
			//下单页面使用优惠券组件不展示tab切换页
			orderShow: {
				type: String,
				default: function() {
					return '';
				}
			},
			typeNum:{
				type:Number,
				default:0
			}
		},
		data() {
			return {
               type: 1,
			};
		},
		watch: {
		    'coupon.type': function (val) {//监听props中的属性
		        this.type = val;
		    }
		},
		methods: {
			close: function() {
				this.type = this.typeNum;
				this.$emit('ChangCouponsClone');
			},
			getCouponUser: function(index, id) {
				let that = this;
				let list = that.coupon.list;
				if (list[index].isUse == true && this.openType == 0) return true;
				switch (this.openType) {
					case 0:
						//领取优惠券
						receiveCoupon(id).then(res => {
							if (res.code === 200) {
								that.$emit('ChangCouponsUseState', index);
								that.$util.Tips({
									title: "领取成功"
								});
								that.$emit('ChangCoupons', list[index]);
							} else {
								that.$util.Tips({
									title: res.message || "领取失败"
								});
							}
						}).catch(err => {
							console.error('领取优惠券失败:', err);
							that.$util.Tips({
								title: "领取失败，请稍后重试"
							});
						});
						break;
					case 1:
						that.$emit('ChangCoupons', index);
						break;
				}
			},
			setType: function(type) {
				this.$emit('tabCouponType', type);
				this.type = type;
			}
		}
	}
</script>

<style scoped lang="scss">
	.coupon-list-window {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		border-radius: 16rpx 16rpx 0 0;
		z-index: 555;
		transform: translate3d(0, 100%, 0);
		transition: all .3s cubic-bezier(.25, .5, .5, .9);
		max-height: 80vh;
		display: flex;
		flex-direction: column;
	}

	.coupon-list-window.on {
		transform: translate3d(0, 0, 0);
	}

	/* 弹框头部 */
	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx 32rpx 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
		flex-shrink: 0;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}

	.close-btn {
		width: 48rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #999999;
		font-size: 28rpx;
	}

	/* 优惠券列表 */
	.coupon-list {
		flex: 1;
		overflow-y: auto;
		padding: 24rpx 24rpx;
		padding-bottom: calc(24rpx + constant(safe-area-inset-bottom) / 3);
		padding-bottom: calc(24rpx + env(safe-area-inset-bottom) / 3);
		background: #f5f5f5;
		margin-top: 0;
	}

	.coupon-item {
		position: relative;
		margin-bottom: 24rpx;
		height: 180rpx;
	}

	.coupon-main {
		display: flex;
		background: #fff;
		height: 100%;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 1rpx solid #f0f0f0;
	}

	.coupon-left {
		flex: 1;
		display: flex;
		padding: 24rpx;
		align-items: center;
	}

	.coupon-image {
		width: 88rpx;
		height: 88rpx;
		border-radius: 8rpx;
		overflow: hidden;
		margin-right: 20rpx;
		flex-shrink: 0;
		background: #f8f8f8;
		border: 1rpx solid #eeeeee;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.coupon-image image {
		width: 100%;
		height: 100%;
	}

	.default-image {
		color: #cccccc;
		font-size: 40rpx;
	}

	.coupon-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.coupon-title {
		font-size: 24rpx;
		color: #333333;
		font-weight: 500;
		line-height: 1.4;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.coupon-time {
		font-size: 20rpx;
		color: #FF2D18;
		font-weight: 400;
	}

	.coupon-right {
		width: 180rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #FF5400;
		position: relative;
	}

	.coupon-right.used {
		background: linear-gradient(135deg, #CCCCCC 0%, #B8B8B8 100%);
	}

	.coupon-value {
		text-align: center;
		margin-bottom: 16rpx;
	}

	.discount {
		font-size: 48rpx;
		font-weight: 700;
		color: #fff;
		line-height: 1;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.money {
		color: #fff;
		line-height: 1;
		display: flex;
		align-items: baseline;
		justify-content: center;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.currency {
		font-size: 24rpx;
		margin-right: 2rpx;
		font-weight: 600;
	}

	.money text:not(.currency) {
		font-size: 48rpx;
		font-weight: 700;
	}

	.condition {
		font-size: 20rpx;
		color: #fff;
		margin-top: 6rpx;
		font-weight: 400;
		opacity: 0.9;
	}

	.coupon-btn {
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		text-align: center;
		min-width: 100rpx;
		font-weight: 500;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	}

	.coupon-btn.available {
		background: #fff;
		color: #FF5400;
	}

	.coupon-btn.used {
		background: rgba(255, 255, 255, 0.3);
		color: #fff;
	}

	/* 上下半圆缺口 - 位于白色和橙色交界处 */
	.circle-top,
	.circle-bottom {
		position: absolute;
		left: calc(100% - 180rpx - 8rpx);
		width: 20rpx;
		height: 20rpx;
		background: #f5f5f5;
		border-radius: 50%;
		z-index: 3;
	}

	.circle-top {
		top: -10rpx;
	}

	.circle-bottom {
		bottom: -10rpx;
	}

	/* 标签样式 */
	.line-title {
		display: inline-block;
		padding: 2rpx 8rpx;
		background: #fff;
		border: 1rpx solid #FF5400;
		border-radius: 12rpx;
		font-size: 18rpx;
		color: #FF5400;
		margin-right: 8rpx;
		line-height: 1.2;
		vertical-align: middle;
	}

	.line-title.gray {
		border-color: #BBB;
		color: #bbb;
		background-color: #F5F5F5;
	}

	.line-title.select {
		border-color: #FF5400;
		color: #FF5400;
	}

	/* 没有优惠券样式 */
	.no-coupons {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 400rpx;

		.noCommodity {
			text-align: center;

			.pictrue {
				width: 200rpx;
				height: 200rpx;
				margin: 0 auto 20rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.text {
				font-size: 28rpx;
				color: #999999;
			}
		}
	}

	/* 遮罩层 */
	.mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		z-index: 554;
	}
</style>
