<template>
	<!-- v-if="reply.length>0" -->
	<view class="evaluateWtapper" v-if="reply.length>0">
		<view class="evaluateItem" v-for="(item, indexw) in reply" :key="indexw">
			<!-- 头像和用户信息行 -->
			<view class="user-header-row">
				<view class="user-avatar">
					<image :src="item.avatar"></image>
				</view>
				<view class="user-info-right">
					<!-- 上部分：昵称和规格 -->
					<view class="user-first-line">
						<view class="username">{{ item.nickname }}<text v-if="item.isLogoff === true">（已注销）</text></view>
						<view class="user-specs">规格：{{ item.sku?item.sku:'无' }}</view>
					</view>
					<!-- 下部分：时间 -->
					<view class="comment-date">{{ item.createTime }}</view>
				</view>
			</view>

			<!-- 星标评分 -->
			<view class="star-rating">
				<view class="start" :class="'star' + item.star"></view>
			</view>

			<!-- 评论内容 -->
			<view class="evaluate-infor">{{ item.comment }}</view>

			<!-- 评论图片 -->
			<view class="imgList acea-row" v-if="item.pics && item.pics.length && item.pics[0]">
				<view class="pictrue" v-for="(itemn, indexn) in item.pics" :key="indexn">
					<image :src="itemn" class="image" @click='getpreviewImage(indexw, indexn)'></image>
				</view>
			</view>

			<!-- 商家回复 -->
			<view class="reply" v-if="item.merchantReplyContent">
				<text class="reply-label">商家回复：</text><text class="reply-content">{{ item.merchantReplyContent }}</text>
			</view>
		</view>
	</view>
</template>
<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	export default {
		props: {
			reply: {
				type: Array,
				default: () => []
			}
		},
		data: function() {
			return {};
		},
		methods: {
			getpreviewImage: function(indexw, indexn) {
				uni.previewImage({
					urls: this.reply[indexw].pics,
					current: this.reply[indexw].pics[indexn]
				});
			}
		}
	}
</script>
<style scoped lang='scss'>
	.evaluateWtapper .evaluateItem {
		background-color: #fff;
		padding: 24rpx;
		border-radius: 20rpx;
    	margin-bottom: 25rpx;
	}

	.evaluateWtapper .evaluateItem~.evaluateItem {
		border-top: 1rpx solid #f5f5f5;
	}

	/* 头像和用户信息行 */
	.user-header-row {
		display: flex;
		margin-bottom: 20rpx;
	}

	.user-avatar {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
		flex-shrink: 0;

		image {
			width: 100%;
			height: 100%;
			border-radius: 50%;
		}
	}

	.user-info-right {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 80rpx;
	}

	.user-first-line {
		display: flex;
		align-items: center;

		.username {
			font-size: 28rpx;
			color: #333;
			margin-right: 20rpx;
		}

		.user-specs {
			font-size: 24rpx;
			color: #999;
		}
	}

	.comment-date {
		font-size: 24rpx;
		color: #999;
	}

	/* 星标评分 */
	.star-rating {
		margin-bottom: 20rpx;
	}

	/* 评论内容 */
	.evaluate-infor {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 20rpx;
		line-height: 1.5;
	}

	/* 评论图片 */
	.imgList {
		margin-bottom: 20rpx;

		.pictrue {
			width: 102rpx;
			height: 102rpx;
			margin-right: 14rpx;
			border-radius: 14rpx;
			margin-bottom: 16rpx;

			image {
				width: 100%;
				height: 100%;
				background-color: #f7f7f7;
				border-radius: 14rpx;
			}
		}
	}

	/* 商家回复 */
	.reply {
		font-size: 26rpx;
		background-color: #f5f5f5;
		border-radius: 14rpx;
		padding: 20rpx;
		position: relative;
		margin-top: 20rpx;

		.reply-label {
			color: #333333;
		}

		.reply-content {
			color: #999999;
		}

		&::before {
			content: "";
			width: 0;
			height: 0;
			border-left: 20rpx solid transparent;
			border-right: 20rpx solid transparent;
			border-bottom: 30rpx solid #f5f5f5;
			position: absolute;
			top: -14rpx;
			left: 40rpx;
		}
	}

	.font_color{
		@include main_color(theme);
	}
</style>
