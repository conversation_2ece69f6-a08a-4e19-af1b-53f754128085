<template>
	<view class="refund-apply-page">
		<!-- 订单信息 -->
		<view class="order-info-container">
			<view class="section-title">订单信息</view>
			
			<view class="info-item">
				<text class="info-label">订单时间</text>
				<text class="info-value">{{ orderInfo.orderTime }}</text>
			</view>
			
			<view class="info-item">
				<text class="info-label">退款金额</text>
				<text class="info-value refund-amount">可退金额 ¥{{ orderInfo.refundAmount }}</text>
			</view>
		</view>
		
		<!-- 上传描述和凭证 -->
		<view class="upload-container">
			<view class="section-title">上传描述和凭证</view>
			
			<!-- 文本输入框 -->
			<view class="text-input-wrapper">
				<textarea 
					v-model="description" 
					placeholder="请输入文字" 
					class="description-input"
					:maxlength="120"
				></textarea>
				<view class="char-count">{{ description.length }}/120</view>
			</view>
			
			<!-- 添加图片 -->
			<view class="image-upload-section">
				<view class="upload-images">
					<!-- 已上传的图片 -->
					<view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
						<image :src="image" mode="aspectFill" class="uploaded-image" @click="previewImage(image)"></image>
						<view class="delete-btn" @click="deleteImage(index)">
							<text class="iconfont icon-close"></text>
						</view>
					</view>
					
					<!-- 添加图片按钮 -->
					<view class="add-image-btn" @click="chooseImage" v-if="uploadedImages.length < 9">
						<view class="add-icon">
							<text class="iconfont icon-tupian"></text>
						</view>
						<text class="add-text">添加图片</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部提交按钮 -->
		<view class="bottom-btn">
			<view class="submit-btn" @click="submitApplication">提交申请</view>
		</view>
	</view>
</template>

<script>
	import { orderRefundVerify } from '@/api/order.js';

	export default {
		data() {
			return {
				description: '',
				uploadedImages: [], // 本地图片路径数组
				uploadedImageUrls: [], // 服务器图片URL数组
				orderInfo: {
					orderTime: '',
					refundAmount: '0.00'
				},
				// 从选择商品页面传递过来的数据
				orderNo: '',
				selectedProducts: [],
				aftersaleType: '',
				reason: ''
			};
		},
		onLoad(options) {
			console.log('申请售后页面参数:', options);
			// 接收从选择商品页面传递的参数
			if (options.orderNo) {
				this.orderNo = options.orderNo;
			}
			if (options.products) {
				this.selectedProducts = JSON.parse(decodeURIComponent(options.products));
			}
			if (options.type) {
				this.aftersaleType = options.type;
			}
			if (options.reason) {
				this.reason = options.reason;
			}

			// 计算订单时间和退款金额
			this.calculateOrderInfo();
		},
		methods: {
			// 计算订单信息
			calculateOrderInfo() {
				// 计算退款金额（选中商品的总价）
				let totalRefundAmount = 0;
				this.selectedProducts.forEach(product => {
					totalRefundAmount += parseFloat(product.price) * parseInt(product.quantity);
				});
				this.orderInfo.refundAmount = totalRefundAmount.toFixed(2);

				// 设置订单时间（使用订单号作为订单时间显示）
				this.orderInfo.orderTime = this.orderNo;
			},

			// 选择图片
			chooseImage() {
				const remainingCount = 9 - this.uploadedImages.length;
				uni.chooseImage({
					count: remainingCount,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						console.log('选择图片成功:', res);
						// 添加本地图片路径用于预览
						this.uploadedImages = this.uploadedImages.concat(res.tempFilePaths);

						// 逐个上传图片到服务器
						res.tempFilePaths.forEach((filePath) => {
							this.uploadSingleImage(filePath);
						});
					},
					fail: (err) => {
						console.log('选择图片失败:', err);
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},

			// 上传单张图片到服务器
			uploadSingleImage(filePath) {
				const that = this;
				this.$util.uploadImgs(filePath, {
					url: 'upload/image',
					name: 'multipart',
					model: "product",
					pid: 1
				}, (res) => {
					// 上传成功，保存服务器URL
					that.uploadedImageUrls.push(res.data.url);
					console.log('图片上传成功:', res.data.url);
				}, (err) => {
					console.log('图片上传失败:', err);
					uni.showToast({
						title: '图片上传失败',
						icon: 'none'
					});
				});
			},
			
			// 删除图片
			deleteImage(index) {
				this.uploadedImages.splice(index, 1);
				// 同时删除对应的服务器URL（如果存在）
				if (this.uploadedImageUrls[index]) {
					this.uploadedImageUrls.splice(index, 1);
				}
			},
			
			// 预览图片
			previewImage(current) {
				uni.previewImage({
					current: current,
					urls: this.uploadedImages
				});
			},
			
			// 整理提交参数
			prepareSubmitParams() {
				// 获取选中商品的ID列表
				const orderDetailIdList = this.selectedProducts.map(product => {
					// 使用原始数据中的ID，如果没有则使用productId
					return product.originalData?.id || product.originalData?.productId || product.id;
				});

				// 确定退款类型：1仅退款，2退款退货
				let refundType = 1; // 默认仅退款
				if (this.aftersaleType === 'return') {
					refundType = 2; // 退货退款
				}

				// 整理图片URL（多个图片用英文逗号分隔）
				const reasonImage = this.uploadedImageUrls.join(',');

				return {
					explain: this.description, // 退款描述
					orderDetailIdList: orderDetailIdList, // 退货商品的id数组
					orderNo: this.orderNo, // 交易订单号
					reasonImage: reasonImage, // 退款凭证图片
					refundType: refundType, // 退款类型
					text: this.reason // 退款原因
				};
			},

			// 提交申请
			submitApplication() {
				if (!this.description.trim()) {
					uni.showToast({
						title: '请输入问题描述',
						icon: 'none'
					});
					return;
				}

				// 整理提交参数
				const submitParams = this.prepareSubmitParams();

				console.log('提交售后申请参数:', submitParams);

				// 调用提交售后申请的接口
				uni.showLoading({
					title: '提交中...'
				});

				orderRefundVerify(submitParams).then(res => {
					uni.hideLoading();
					console.log('售后申请提交成功:', res);

					// 检查响应是否成功
					if (res.code === 200 && res.message) {
						// 从响应的 message 字段获取 refundOrderNo
						const refundOrderNo = res.message;

						uni.showToast({
							title: '申请提交成功',
							icon: 'success'
						});

						// 延迟跳转到售后详情页面，并关闭相关页面
						setTimeout(() => {
							// 回退2个页面，关闭当前页面(refund_apply)和选择商品页面(select_refund_goods)
							// 回到订单详情页面(order_details/detail)
							uni.navigateBack({
								delta: 2,
								success: () => {
									// 回退成功后，再从订单详情页面跳转到售后详情页面
									setTimeout(() => {
										uni.navigateTo({
											url: `/pages/goods/aftersale_details/index?orderNo=${this.orderNo}&refundOrderNo=${refundOrderNo}`
										});
									}, 100);
								},
								fail: () => {
									// 如果回退失败，直接跳转到售后详情页面
									uni.redirectTo({
										url: `/pages/goods/aftersale_details/index?orderNo=${this.orderNo}&refundOrderNo=${refundOrderNo}`
									});
								}
							});
						}, 1500);
					} else {
						// 如果响应格式不正确，显示错误信息
						uni.showToast({
							title: res.message || '申请提交失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('售后申请提交失败:', err);

					uni.showToast({
						title: err.message || '申请提交失败',
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.refund-apply-page {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 0 30rpx 120rpx;
	}
	
	.order-info-container,
	.upload-container {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-top: 20rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 30rpx;
	}
	
	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.info-label {
			font-size: 28rpx;
			color: #333;
		}
		
		.info-value {
			font-size: 28rpx;
			color: #999;
			
			&.refund-amount {
				color: #333;
			}
		}
	}
	
	.text-input-wrapper {
		position: relative;
		
		.description-input {
			width: 100%;
			min-height: 200rpx;
			background-color: #f8f9fa;
			border-radius: 12rpx;
			padding: 20rpx;
			font-size: 28rpx;
			color: #333;
			line-height: 1.5;
			box-sizing: border-box;
			border: none;
			outline: none;
		}
		
		.char-count {
			position: absolute;
			bottom: 20rpx;
			right: 20rpx;
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.image-upload-section {
		margin-top: 40rpx;
		
		.upload-images {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
		}
		
		.image-item {
			position: relative;
			width: 160rpx;
			height: 160rpx;
			
			.uploaded-image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}
			
			.delete-btn {
				position: absolute;
				top: -10rpx;
				right: -10rpx;
				width: 40rpx;
				height: 40rpx;
				background-color: #ff4d4f;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.iconfont {
					color: #fff;
					font-size: 20rpx;
				}
			}
		}
		
		.add-image-btn {
			width: 160rpx;
			height: 160rpx;
			border: 2rpx dashed #ddd;
			border-radius: 12rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: #fafafa;
			
			.add-icon {
				width: 50rpx;
				height: 50rpx;
				border-radius: 12rpx;
				background-color: #f0f0f0;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 10rpx;
				
				.iconfont {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.add-text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
	
	.bottom-btn {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		padding: 30rpx;
		border-top: 1rpx solid #f5f5f5;
		
		.submit-btn {
			width: 100%;
			height: 80rpx;
			background-color: #BDFD5B;
			color: #333;
			font-size: 32rpx;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 20rpx;
		}
	}
</style> 