<template>
	<view :data-theme="theme">
		<view class="withdraw_cash">
			<view class="withdraw_log">
				<view class="withdraw_cash_list" v-for="item in list" :key="item.id" @click="goStatus(item)">
					<view class="withdraw_cash_left">
						<img :src="`/static/images/icon_${item.closingType}_kind.png`" style="width: 60rpx;height: 60rpx;margin-right: 10rpx;" alt="" />
						<view style="margin-left: 10rpx;">
							<view style="font-size: 28rpx;display: flex;align-items: center;">
								<text>{{onClosingType(item.closingType)}}</text>
								<view class="withdraw_status" style="display: flex;">
									<img v-if="item.auditStatus!==1" mode="heightFix" style="height: 26rpx;margin-left: 4rpx;" :src="`/static/images/withdraw_status${item.auditStatus}.png`" alt="" />
								</view>
							</view>
							<view style="margin-top: 10rpx;color: #999999;font-size: 24rpx;">{{item.updateTime}}</view>
						</view>
					</view>
					<view style="display: flex;align-items: center;">
						<view style="color: #222;font-size: 32rpx;font-weight: 600;">¥{{item.closingPrice}}</view>
						<view class="right-icon"></view>
					</view>
				</view>
			</view>
		</view>
		<view class='loadingicon acea-row row-center-wrapper'>
			<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{userBillList.length > 0?loadTitle:''}}
		</view>
	</view>
</template>

<script>
	import {
		getProductHot
	} from '@/api/product.js';
	import {
		userActivity,
		getMyAccountApi,
		getBillList,
		getRechargeApi,
		rechargeCreateApi,
		withdrawLog
	} from '@/api/user.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		alipayQueryPayResult
	} from '@/api/order.js';
	import recommend from "@/components/base/recommend.vue";
	import {
		Debounce
	} from '@/utils/validate.js'
	let app = getApp();
	export default {
		components: {
			recommend
		},
		data() {
			return {
				loading: true,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 20,
				list: [],
				userBillList: ['佣金提现至银行卡','佣金提现至余额','佣金提现至微信','佣金提现至支付宝'],
			};
		},
		computed: mapGetters(['isLogin', 'userInfo']),
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.userDalance();
					}
				},
				deep: true
			}
		},
		onLoad() {
			let that = this;
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#F5F5F5',
			});
		},
		onShow() {
			if (this.isLogin) {
				this.getWithdrawLog();
			} else {
				toLogin();
			}
		},
		async onReachBottom() {
		// 触发加载更多逻辑
			await this.getWithdrawLog()
		},
		methods: {
			async getWithdrawLog() {
				let that = this;
				if (that.loadend) return;
				that.loading = true;
				that.loadTitle = "";
				let params = {
					page: that.page,
					limit: that.limit
				}
				await withdrawLog(params).then(function(res) {
					that.list = that.list.concat(res.data.list),
					that.loadend = res.data.pages <= that.page;
					that.page += 1;
					that.loading = false;
					that.loadTitle = that.loadend ? "我也是有底线的~" : "加载更多";
				}, function(res) {
					that.loading = false;
					that.loadTitle = '加载更多';
				});
			},
			onClosingType(type) {
				if(type=='yue') {
					return '佣金提现至余额'
				}else if(type=='weixin') {
					return '佣金提现至微信'
				}else if(type=='alipay') {
					return '佣金提现至支付宝'
				}else if(type=='bank') {
					return '佣金提现至银行卡'
				}
			},
			goStatus(item) {
				console.log(item)
				uni.navigateTo({
					url: `/pages/users/withdraw_details/index?auditStatus=${item.auditStatus}&createTime=${item.createTime}&auditTime=${item.auditTime}&refusalReason=${item.refusalReason}`
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.withdraw_cash{
		padding: 0 20rpx;
		border-radius: 40rpx;
		.withdraw_log{
			padding-left: 20rpx;
			background-color: #fff;
			border-radius: 20rpx;
			.withdraw_cash_list{
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx 30rpx 30rpx 0;
				border-bottom: 1px solid #F5F5F5;
				.withdraw_cash_left{
					display: flex;
					align-items: center;
				}
				.right-icon{
					margin-left: 10rpx;
					width: 14rpx;
					height: 14rpx;
					border-top: 1px solid #666666;
					border-right: 1px solid #666666;
					transform: rotate(45deg);
				}
			}
		}
	}
</style>
