<template>
	<view class="aftersale-detail-page" :data-theme="theme">
		<!-- 顶部导航栏（适配微信小程序胶囊） -->
		<view class="nav-header" :style="'height:' + (navHeight + statusBarHeight) + 'px;padding-top:' + statusBarHeight + 'px;'">
			<view class="nav-content" :style="'height:' + navHeight + 'px;'">
				<view class="nav-left" @click="goBack">
					<text class="iconfont icon-fanhui"></text>
				</view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-area" :style="'margin-top:' + (navHeight + statusBarHeight) + 'px;'">
			<!-- 售后状态提示文字 -->
			<view class="aftersale-status-text">
				<image src="/static/images/wait_indent2.png" mode="aspectFill"></image>
				<text>{{ getStatusText() }}</text>
			</view>
			
			<!-- 退款信息 -->
			<view class="refund-info">
				<view class="refund-title">退款信息</view>
				<view class="refund-item">
					<text>退款金额</text>
					<text style="color: #FF2222;">¥{{ aftersaleInfo.refundAmount }}</text>
				</view>
				<view class="refund-item">
					<text>退款状态</text>
					<text>{{ aftersaleInfo.refundStatus }}</text>
				</view>
				<view class="refund-item">
					<text>申请原因</text>
					<text>{{ aftersaleInfo.reason }}</text>
				</view>
				<view v-if="aftersaleInfo.reasonExplain" class="refund-item">
					<text>用户说明</text>
					<text>{{ aftersaleInfo.reasonExplain }}</text>
				</view>
				<view v-if="aftersaleInfo.reasonImages && aftersaleInfo.reasonImages.length > 0" class="refund-item">
					<text>退款图片</text>
					<view class="reason-images">
						<image
							v-for="(img, index) in aftersaleInfo.reasonImages"
							:key="index"
							:src="img"
							mode="aspectFill"
							@click="previewImage(img, aftersaleInfo.reasonImages)"
						></image>
					</view>
				</view>
				<view v-if="aftersaleInfo.refundReason" class="refund-item">
					<text>拒绝说明</text>
					<text style="color: #FF2222;">{{ aftersaleInfo.refundReason }}</text>
				</view>
			</view>
			
			<!-- 退货地址 -->
			<view class="return-address">
				<view class="address-title">
					<text>退货地址</text>
				</view>
				<view class="address-item">
					<text>收货人</text>
					<text>{{ aftersaleInfo.returnAddress.name }} {{ aftersaleInfo.returnAddress.phone }}</text>
				</view>
				<view class="address-item">
					<text>收货地址</text>
					<text>{{ aftersaleInfo.returnAddress.address }}</text>
				</view>
			</view>
			
			<!-- 退货物流 -->
			<view v-if="aftersaleInfo.refundStatusCode === 2" class="return-logistics">
				<view class="logistics-title">退货物流</view>
				<view v-if="aftersaleInfo.trackingNumber && aftersaleInfo.expressName" class="logistics-item">
					<text>{{ aftersaleInfo.expressName }}</text>
					<text>{{ aftersaleInfo.trackingNumber }}</text>
				</view>
				<view v-else class="logistics-item" @click="selectLogistics">
					<text>请选择物流信息</text>
					<text class="iconfont icon-xiangyou"></text>
				</view>
			</view>
			
			<!-- 商店信息 + 商品信息 -->
			<view class="merchant-product-card">
				<!-- 商店信息部分 -->
				<view class="merchant-section">
					<view class="merchant-header">
						<text :class="'delivery-tag ' + (aftersaleInfo.deliveryClass || 'express')">{{ aftersaleInfo.deliveryType }}</text>
						<text class="merchant-name">{{ aftersaleInfo.merchantName }}</text>
					</view>
					<view v-if="aftersaleInfo.deliveryTime && aftersaleInfo.deliveryType !== '快递'" class="delivery-time">
						{{ getDeliveryTimeText() }}：{{ aftersaleInfo.deliveryTime }}
					</view>
				</view>
				
				<!-- 商品信息部分 -->
				<view class="products-section">
					<view v-for="(product, index) in aftersaleInfo.products" :key="index" class="product-item">
						<!-- 商品上部分：图片、标题、规格、金额、数量 -->
						<view class="product-main">
							<view class="product-image">
								<image :src="product.image" mode="aspectFill"></image>
							</view>
							<view class="product-info">
								<view class="product-name">{{ product.name }}</view>
								<view class="product-spec">{{ product.spec }}</view>
								<view class="product-price">¥{{ product.price }}</view>
							</view>
							<view class="product-quantity">x{{ product.quantity }}</view>
						</view>
						
						<!-- 商品下部分：描述 -->
						<view v-if="product.description" class="product-description">
							{{ product.description }}
						</view>

						<!-- 眼镜定制信息（包含验光单信息） -->
						<glassesProductCustomInfo v-if="product.customData && (product.customData.lens || product.customData.optometry)" :customData="product.customData"></glassesProductCustomInfo>
					</view>
					
					<!-- 商品合计信息 -->
					<view class="product-summary">
						<view class="summary-right">
							<text class="summary-price">已选{{ aftersaleInfo.totalQuantity }}件</text>
							<text class="summary-price">合计：<text class="price-highlight">¥{{ aftersaleInfo.totalPrice }}</text></text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 退款信息详细 -->
			<view class="refund-detail-info">
				<view class="detail-title">退款详情</view>
				<view class="detail-item">
					<text>退款单号</text>
					<text>{{ aftersaleInfo.refundOrderNo }}</text>
				</view>
				<view class="detail-item">
					<text>申请时间</text>
					<text>{{ aftersaleInfo.applyTime }}</text>
				</view>
				<view v-if="aftersaleInfo.agreeTime" class="detail-item">
					<text>同意时间</text>
					<text>{{ aftersaleInfo.agreeTime }}</text>
				</view>
				<view v-if="aftersaleInfo.refundTime" class="detail-item">
					<text>退款时间</text>
					<text>{{ aftersaleInfo.refundTime }}</text>
				</view>
				<view class="detail-item">
					<text>退款类型</text>
					<text>{{ aftersaleInfo.refundType }}</text>
				</view>
				<view class="detail-item">
					<text>退款状态</text>
					<text>{{ aftersaleInfo.refundStatus }}</text>
				</view>
				<view class="detail-item">
					<text>关联订单</text>
					<text>{{ aftersaleInfo.relatedOrderNo }}</text>
				</view>
				<view v-if="aftersaleInfo.voucherImages && aftersaleInfo.voucherImages.length > 0" class="detail-item">
					<text>关联凭证</text>
					<view class="voucher-images">
						<image
							v-for="(img, index) in aftersaleInfo.voucherImages"
							:key="index"
							:src="img"
							mode="aspectFill"
							@click="previewImage(img, aftersaleInfo.voucherImages)"
						></image>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view v-if="aftersaleInfo.refundStatusCode === 0" class="action-btn secondary" @click="cancelAftersale">取消申请</view>
			<button class="action-btn primary" open-type="contact">联系客服</button>
		</view>
	</view>
</template>

<script>
	import { getAftersaleDetail, cancelRefundOrder } from '@/api/order.js';
	import glassesProductCustomInfo from '@/components/glassesProductCustomInfo';

	export default {
		components: {
			glassesProductCustomInfo
		},
		data() {
			return {
				theme: 'default',
				orderNo: '',
				refundOrderNo: '', // 新增：退款订单号
				aftersaleType: '',
				isRefund: false, // 新增：是否为退款订单
				statusBarHeight: 0,
				navHeight: 44,
				// 模拟售后数据
				aftersaleInfo: {
					refundAmount: '192.11',
					reason: '订单信息有误（规格/颜色等）',
					returnAddress: {
						name: '张晓明',
						phone: '15288889999',
						address: '山西省太原市小店区龙城北街小店区龙城北街313号三嘉1层1-3'
					},
					merchantName: '乐腾生态（龙城北街店）',
					deliveryTime: '05-23 12:00-08:00',
					totalQuantity: 1,
					products: [
						{
							name: '乘号大框显脸小 可配镜眼镜百搭大框镜',
							spec: '黑金色',
							price: '288.00',
							quantity: 1,
							image: '/static/images/glasses.png',
							description: '儿童多步高清防近视跟随镜片学生青少年控制眼镜度数男女1.56收费（400度以内)',
							hasOptometry: true
						}
					],
					totalPrice: '288.00',
					refundOrderNo: '20250523786501233',
					applyTime: '2025-05-23 18:12:11',
					refundType: '退货退款',
					relatedOrderNo: '20250523786501233',
					voucherImages: [
						'/static/images/voucher1.png',
						'/static/images/voucher2.png'
					]
				}
			};
		},
		onLoad(options) {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;

			// 获取胶囊按钮信息以适配导航栏
			// #ifdef MP-WEIXIN
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.navHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;
			// #endif

			// #ifndef MP-WEIXIN
			this.navHeight = 44;
			// #endif

			if (options.orderNo) {
				this.orderNo = options.orderNo;
			}
			if (options.refundOrderNo) {
				this.refundOrderNo = options.refundOrderNo;
			}
			if (options.aftersaleType) {
				this.aftersaleType = options.aftersaleType;
			}
			if (options.isRefund) {
				this.isRefund = options.isRefund === 'true';
			}

			// 调用售后详情接口
			this.loadAftersaleDetail();
		},
		methods: {
			// 加载售后详情数据
			loadAftersaleDetail() {
				// 使用传入的退款订单号，如果没有则使用原订单号
				const refundOrderNo = this.refundOrderNo || this.orderNo;

				if (!refundOrderNo) {
					uni.showToast({
						title: '缺少订单参数',
						icon: 'none'
					});
					return;
				}

				console.log('调用售后详情接口，退款订单号：', refundOrderNo);

				uni.showLoading({
					title: '加载中...'
				});

				getAftersaleDetail(refundOrderNo).then(res => {
					uni.hideLoading();
					console.log('售后详情接口返回数据：', res);

					if (res.code === 200 && res.data) {
						console.log('售后详情数据：', JSON.stringify(res.data, null, 2));

						// 映射API数据到页面数据
						this.mapAftersaleData(res.data);
					} else {
						uni.showToast({
							title: res.message || '获取售后详情失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('售后详情接口调用失败：', err);
					uni.showToast({
						title: '网络异常，请重试',
						icon: 'none'
					});
				});
			},

			// 映射API数据到页面数据
			mapAftersaleData(apiData) {
				// 处理退款状态
				const refundStatusMap = {
					0: '待审核',
					1: '审核未通过',
					2: '退款中',
					3: '已退款'
				};

				// 处理退款类型
				const refundTypeMap = {
					1: '仅退款',
					2: '退款退货'
				};

				// 处理配送类型
				const shippingTypeMap = {
					1: '快递',
					2: '自提',
					3: '配送'
				};

				// 处理配送类型样式类
				const shippingClassMap = {
					1: 'express',
					2: 'pickup',
					3: 'delivery'
				};

				// 处理商品列表
				const products = (apiData.responseList || []).map(item => {
					// 解析定制数据
					let customData = {};
					try {
						customData = JSON.parse(item.customData || '{}');
					} catch (e) {
						console.warn('解析定制数据失败:', e);
					}

					return {
						name: item.productName,
						image: item.image,
						spec: item.sku,
						price: item.refundPrice,
						quantity: item.applyRefundNum,
						description: item.intro,
						hasOptometry: !!customData.optometry,
						customData: customData
					};
				});

				// 计算总数量
				const totalQuantity = products.reduce((sum, product) => sum + product.quantity, 0);

				// 构建完整地址
				const fullAddress = `${apiData.merProvince || ''}${apiData.merCity || ''}${apiData.merDistrict || ''}${apiData.addressDetail || ''}`;

				// 映射数据
				this.aftersaleInfo = {
					refundAmount: apiData.refundPrice ,
					reason: apiData.refundReasonWap ,
					reasonExplain: apiData.refundReasonWapExplain,
					reasonImages: apiData.refundReasonWapImg ? apiData.refundReasonWapImg.split(',').filter(img => img.trim()) : [],
					returnAddress: {
						name: apiData.merName ,
						phone: '', // API中没有电话字段，可能需要从其他接口获取
						address: fullAddress
					},
					merchantName: apiData.merName ,
					deliveryTime: this.formatDeliveryTime(apiData.shippingDate, apiData.shippingTimeStr),
					deliveryType: shippingTypeMap[apiData.shippingType] || '快递',
					deliveryClass: shippingClassMap[apiData.shippingType] || 'express',
					totalQuantity: totalQuantity,
					products: products,
					totalPrice: apiData.refundPrice ,
					refundOrderNo: apiData.refundOrderNo ,
					applyTime: this.formatTime(apiData.createTime),
					agreeTime: this.formatTime(apiData.agreeTime),
					refundTime: this.formatTime(apiData.refundTime),
					refundType: refundTypeMap[apiData.refundType],
					refundStatus: refundStatusMap[apiData.refundStatus],
					refundStatusCode: apiData.refundStatus, // 保存原始状态码用于条件判断
					relatedOrderNo: apiData.orderNo ,
					voucherImages: apiData.refundReasonWapImg ? apiData.refundReasonWapImg.split(',').filter(img => img.trim()) : [],
					trackingNumber: apiData.trackingNumber ,
					expressName: apiData.expressName ,
					refundReason: apiData.refundReason 
				};
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');

				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			},

			// 格式化配送时间
			formatDeliveryTime(date, timeStr) {
				if (!date && !timeStr) return '';

				let result = '';
				if (date) {
					const deliveryDate = new Date(date);
					const month = String(deliveryDate.getMonth() + 1).padStart(2, '0');
					const day = String(deliveryDate.getDate()).padStart(2, '0');
					result = `${month}-${day}`;
				}

				if (timeStr) {
					result += ` ${timeStr}`;
				}

				return result || '待确定';
			},

			// 获取状态提示文字
			getStatusText() {
				const statusTextMap = {
					'待审核': '等待商家同意',
					'审核未通过': '商家拒绝了退款申请',
					'退款中': '商家同意等待买家退回商品',
					'已退款': '退款已完成'
				};

				return statusTextMap[this.aftersaleInfo.refundStatus] || '处理中';
			},

			// 获取配送时间文字
			getDeliveryTimeText() {
				const deliveryTimeTextMap = {
					'快递': '快递配送',
					'自提': '门店自提',
					'配送': '门店配送'
				};

				return deliveryTimeTextMap[this.aftersaleInfo.deliveryType] || '门店配送';
			},

			goBack() {
				uni.navigateBack();
			},

			async cancelAftersale() {
				// 只有在待审核状态下才能取消申请
				if (this.aftersaleInfo.refundStatusCode !== 0) {
					uni.showToast({
						title: '当前状态不能取消申请',
						icon: 'none'
					});
					return;
				}

				uni.showModal({
					title: '取消申请',
					content: '确定要取消退款申请吗？取消后需要重新申请。',
					success: async (res) => {
						if (res.confirm) {
							try {
								uni.showLoading({
									title: '取消中...'
								});

								await cancelRefundOrder(this.aftersaleInfo.refundOrderNo);

								uni.hideLoading();
								uni.showToast({
									title: '取消申请成功',
									icon: 'success'
								});

								// 延迟返回上一页
								setTimeout(() => {
									uni.navigateBack();
								}, 1500);

							} catch (error) {
								uni.hideLoading();
								console.error('取消退款申请失败:', error);
								uni.showToast({
									title: error.message || '取消申请失败',
									icon: 'none'
								});
							}
						}
					}
				});
			},

			selectLogistics() {
				console.log('选择物流信息');

				if (!this.aftersaleInfo.refundOrderNo) {
					uni.showToast({
						title: '退款订单号不存在',
						icon: 'none'
					});
					return;
				}

				// 跳转到编辑退货物流页面，传递退款订单号
				uni.navigateTo({
					url: `/pages/goods/return_logistics_edit/index?refundOrderNo=${this.aftersaleInfo.refundOrderNo}`
				});
			},
			previewImage(img, urls) {
				uni.previewImage({
					current: img,
					urls: urls || this.aftersaleInfo.voucherImages
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.aftersale-detail-page {
		background: linear-gradient(180deg, #b8fe4e 0%, #f5f5f5 16%);
		min-height: 100vh;
		padding-bottom: 120rpx;
	}
	
	.nav-header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #b8fe4e;
		
		.nav-content {
			display: flex;
			align-items: center;
			padding: 0 30rpx;
			
			.nav-left {
				.iconfont {
					font-size: 40rpx;
					color: #333;
				}
			}
		}
	}
	
	.content-area {
		.aftersale-status-text {
			display: flex;
			align-items: center;
			color: #333333;
			font-size: 32rpx;
			font-weight: 600;
			margin: 40rpx 30rpx 20rpx;
			padding-top: 30rpx;
			
			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 10rpx;
			}
		}
	}
	
	.refund-info,
	.return-address,
	.return-logistics,
	.merchant-product-card,
	.refund-detail-info {
		background-color: #fff;
		margin: 0 30rpx 20rpx;
		border-radius: 20rpx;
		padding: 20rpx 30rpx;
	}
	
	.refund-info,
	.return-address,
	.return-logistics,
	.refund-detail-info {
		.refund-title,
		.address-title,
		.logistics-title,
		.detail-title {
			font-size: 30rpx;
			color: #222222;
			font-weight: 600;
			margin-bottom: 30rpx;
		}


		
		.refund-item,
		.address-item,
		.logistics-item,
		.detail-item {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 20rpx;
			font-size: 26rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			text:first-child {
				color: #999999;
				flex: 1;
			}
			
			text:last-child {
				color: #333333;
				text-align: right;
				flex: 2;
			}
		}
		
		.logistics-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			text:first-child {
				color: #333333;
			}
			
			.iconfont {
				color: #999999;
				font-size: 24rpx;
			}
		}
		
		.voucher-images,
		.reason-images {
			display: flex;
			gap: 10rpx;
			flex: 2;
			justify-content: flex-end;
			flex-wrap: wrap;

			image {
				width: 60rpx;
				height: 60rpx;
				border-radius: 8rpx;
			}
		}

		.reason-images {
			justify-content: flex-start;

			image {
				width: 80rpx;
				height: 80rpx;
			}
		}

		.delivery-tag {
			font-size: 22rpx;
			color: #fff;
			padding: 6rpx 12rpx;
			border-radius: 6rpx;

			&.delivery {
				background-color: #FF7F00;
			}

			&.pickup {
				background-color: #FF3127;
			}

			&.express {
				background-color: #409EFF;
			}
		}
	}
	
	.merchant-product-card {
		.merchant-section {
			margin-bottom: 30rpx;
			
			.merchant-header {
				display: flex;
				align-items: center;
				margin-bottom: 15rpx;
				
				.delivery-tag {
					color: #fff;
					font-size: 22rpx;
					padding: 6rpx 12rpx;
					border-radius: 6rpx;
					margin-right: 15rpx;

					&.delivery {
						background-color: #FF7F00;
					}

					&.pickup {
						background-color: #FF3127;
					}

					&.express {
						background-color: #409EFF;
					}
				}
				
				.merchant-name {
					font-size: 30rpx;
					color: #333;
					font-weight: 600;
				}
			}
			
			.delivery-time {
				color: #FF3127;
				font-size: 26rpx;
			}
		}
		
		.products-section {
			.product-item {
				&:not(:last-child) {
					margin-bottom: 30rpx;
				}
				
				.product-main {
					display: flex;
					align-items: flex-start;
					margin-bottom: 20rpx;
					
					.product-image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 12rpx;
						overflow: hidden;
						margin-right: 20rpx;
						flex-shrink: 0;
						
						image {
							width: 100%;
							height: 100%;
						}
					}
					
					.product-info {
						flex: 1;
						height: 160rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						
						.product-name {
							font-size: 28rpx;
							color: #333;
							font-weight: 600;
							line-height: 1.4;
						}
						
						.product-spec {
							font-size: 26rpx;
							color: #666;
						}
						
						.product-price {
							font-size: 32rpx;
							color: #FF2222;
							font-weight: 600;
						}
					}
					
					.product-quantity {
						align-self: center;
						font-size: 26rpx;
						color: #222222;
						margin-left: 20rpx;
					}
				}
				
				.product-description {
					background-color: #f5f5f5;
					border-radius: 12rpx;
					padding: 20rpx;
					font-size: 24rpx;
					color: #999999;
					line-height: 1.5;
					margin-bottom: 20rpx;
				}
				

			}
			
			.product-summary {
				padding-top: 20rpx;
				display: flex;
				justify-content: flex-end;
				
				.summary-right {
					text-align: right;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.summary-price :first-child {
						margin-right: 10rpx;
					}
					
					.summary-price {
						display: block;
						font-size: 28rpx;
						color: #222222;
						
						.price-highlight {
							color: #FF2222;
							font-weight: 600;
						}
					}
				}
			}
		}
	}
	
	.bottom-actions {
		display: flex;
		gap: 20rpx;
		width: 50%;
		float: right;
		margin-right: 30rpx;
		
		.action-btn {
			flex: 1;
			text-align: center;
			padding: 15rpx;
			border-radius: 15rpx;
			font-size: 28rpx;
			background: none;
			outline: none;

			&.secondary {
				border: 1rpx solid #999999;
				color: #999999;
			}

			&.primary {
				border: 1rpx solid #999999;
				color: #999999;
			}
		}
	}

	/* 售后详情页面中 glassesProductCustomInfo 组件样式定制 */
	/deep/ .glasses {
		width: 100% !important;
		margin-top: 20rpx;
	}

	/deep/ .glasses .lens {
		background-color: #f5f5f5;
		color: #999999 !important;
		width: 100%;
		border-radius: 12rpx !important;
		padding: 20rpx !important;
		text-align: left;
		font-size: 24rpx !important;
		line-height: 1.5;
		margin-bottom: 0 !important;
	}

	/deep/ .glasses .optometry {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
		font-size: 26rpx;
		color: #222222;
		margin-top: 20rpx;
	}

	/deep/ .glasses .optometry .iconfont {
		color: #999999;
		font-size: 24rpx !important;
	}
</style>