// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
	userDrawer: {
		data: [{
				name: 'Home'
			},
			{
				name: 'Search'
			},
			{
				name: 'Category'
			},
			{
				name: 'Cart'
			},
			{
				name: 'My Favourites'
			},
			{
				name: 'Mine'
			},
			{
				name: 'Language'
			}
		],
		language: [{
				name: 'Chinese'
			},
			{
				name: 'English'
			},
			{
				name: 'French'
			},
			{
				name: 'Thai'
			},
			{
				name: 'Lao'
			},
		]
	},
	page: {
		index: {
			mainNav: [{
					name: 'categories'
				},
				{
					name: 'Favorite'
				},
				{
					name: 'Order'
				},
				{
					name: 'address'
				},
			],
			coupons: 'coupons',
			titProduct: 'Featured products',
			titNow: 'Trending now',
			titCate: 'Featured categories',
			titBtn: 'View All',
			titAlso: 'You May Also Like',
			couponDesc: 'Order over US ',
			received: 'Received',
			orMore: 'or more',
			viewDetails: 'Details',
			recommended: 'Recommended',
			promotions: 'Promotions',
			onView: 'View item',
			collectSite: 'Collect this site',
			applyFor: 'Apply for',
			mobileMall: 'Mobile Mall',
			shopStreet: 'SHOP STREET',
			customerService: 'service',
			top: 'Top',
			topOne: 'TOP ONE',
			footer1: 'Complete variety',
			footer2: 'Fast delivery',
			footer3: 'Genuine product',
			footer4: 'low price every day',
			address: "Room 1101-04, Block A, Qihang Times Square, Xixian Avenue, Weiyang District, Xi'an City, Shaanxi Province",
			icp: "Xi'an Zhongbang Network Technology Co., Ltd. Shaanxi ICP No. 14011498-3"
		},
		goodsDetail: {
			search: 'Search for goods',
			sales: 'Sales',
			oldPrice: 'Old price',
			inventory: 'Inventory',
			coupons: 'coupons',
			choose: 'choose',
			choose1: 'choice',
			Reviews: 'Reviews',
			good: 'Good',
			details: 'Details',
			recommend: 'Recommend',
			home: 'Home',
			like: 'Like',
			cart: 'Cart',
			addCart: 'Add To Cart',
			buyNow: 'Buy Now',
			soldOut: 'Sold out',
			sold: 'Sold',
			type: 'types in total',
			navList: [{
					name: 'product'
				},
				{
					name: 'review'
				},
				{
					name: 'recommend'
				},
				{
					name: 'detail'
				}
			],
			select: 'The selected',
			clear: 'Del all',
			settle: 'Settlement',
			num: 'Number',
			confirm: 'Confirm',
			cancel: 'Cancel',
			assure: "Assure"
		},
		goodsList: {
			navTitle: 'Product List',
			search: 'Search for goods',
			default: 'Default',
			price: 'Price',
			sales: 'Sales',
			new: 'New',
			empty: 'Go and see something else',
			no: 'No More',
			more: 'Load More',
			nono: 'I also have a bottom line',
			nomer: 'No shop'
		},
		user: {
			navTitle: 'Mine',
			like: 'Wish list',
			integral: 'integral',
			coupons: 'Coupons',
			orderCenter: 'Order Center',
			myOrder: 'My Order',
			placeLogin: 'Please click login',
			orderStatus: [{
					name: 'To Pay'
				},
				{
					name: 'Waiting'
				},
				{
					name: 'Received'
				},
				{
					name: 'Evaluation'
				},
				{
					name: 'Return'
				},
				{
					name: 'Completed'
				},
				{
					name: 'Received goods'
				}
			],
			services: 'Other Services',
			address: 'Address Management',
			contact: 'Customer service',
			applicationRecord: 'Merchant Collection',
			goodsCollection: 'Commodity collection',
			record: 'Application record',
			mineNav: [{
					name: 'My Order'
				},
				{
					name: 'Refund list'
				},
				{
					name: 'Evaluation list'
				}
			],
			qunTitle: 'Business license information',
			qunDesc: 'According to the requirements of the State Administration for Industry and Commerce "Online Transaction Management Measures", the online store business license information is publicized as follows:',
			qunCode: 'Please enter the image verification code',
			submit: 'submit',
		},
		goodsSearch: {
			navTitle: 'Godds Search',
			place: 'Click to search for goods',
			search: 'Search',
			hotSearch: 'Hot Search',
			recommend: 'recommend',
			emptySearch: 'There is nothing',
			placeSearch: 'Please enter the item to search',
			onSearch: 'Searching',
			goods: 'goods',
			mer: 'Merchant',
			merchantType: 'Merchant type',
			merchantClassify: 'Merchant classification',
			sure: 'determine',
			reset: 'Reset',
			open: 'expand all',
			up: 'Put away',
			sort: 'Sort',
			Default: 'default',
			Seller: 'Seller',
			Price: 'Price',
			moreMer: 'more stores',
			searchMer: 'Sorry, the store you searched for was not found~',
			searchGoods: 'Sorry, nothing related to your search was found~',
		},
		goodsAddcart: {
			navTitle: 'Shopping Cart',
			labelNav: '100% product quality assurance and after-sales service',
			buyNum: 'Buy quantity',
			management: 'management',
			cancel: 'cancel',
			attribute: 'attribute',
			failureGoods: 'The failure of goods',
			empty: 'Delete all', //
			failure: 'failure',
			fallDesc: 'The goods are no longer valid',
			selectAll: 'All',
			buyNow: 'Buy Now', //
			like: 'Like',
			delete: 'Delete',
			emptyCart: 'There is no goods',
			again: 'Please reselect the specification',
			reselect: 'reselect'
		},
		orderPayStatus: {
			success: 'payment successful',
			fail: 'Payment failed',
			orderId: 'Order Id',
			orderTime: 'Time',
			payType: 'Payment Method',
			payPrice: 'Total Payment',
			failReason: 'The reason for failure',
			viewOrder: 'To view the order',
			backHome: 'Back Home'
		},
		orderDetails: {
			navTitle: 'Order Details',
			item: 'item(s)', // item(s)
			Customer: 'Customer service',
			orderId: 'order Id',
			copy: 'copy',
			orderTime: 'Time',
			refundTime: 'Refund time',
			payStatus: 'Pay Status',
			payTrue: 'Success pay',
			payFalse: 'Did not pay',
			payType: 'Method',
			message: 'message',
			name: 'Consignee',
			phone: 'Phone',
			address: 'Address',
			returnReason: 'Reason',
			returnMsg: 'Desc',
			returnImg: 'Voucher',
			sendMethod: 'Method',
			sendOne: 'Send', //send
			sendTwo: 'The delivery', //The delivery
			CourierCompany: 'ExpName', //Courier company
			CourierNo: 'ExpNo', //Courier number
			deliveryName: 'Courier',
			deliveryPhone: 'Phone',
			Virtual: 'Virtual', //Virtual delivery
			deliveryMsg: 'Has been sent',
			allPrice: 'All Price',
			freight: 'freight',
			coupon: 'Coupon deduction', //Coupon deduction
			point: 'Points deduction', //Points deduction
			actualPayment: 'Actual payment', //actual payment
			cancelOrder: 'Cancel Order',
			nowPay: 'Now Pay',
			refund: 'Refund',
			logistics: 'Logistics',
			confirm: 'Confirm',
			delete: 'Delete',
			again: 'Again',
			confirmInfo: 'To protect your rights and interests, please confirm the receipt after receiving the goods and confirming that they are correct',
			confirmDel: 'Are you sure to delete this order',
			confirmCancel: 'Confirm to cancel the order',
		},
		users: {
			userInfo: {
				navTitle: 'Settings',
				avatar: 'Avatar',
				name: 'Name',
				email: 'Email',
				password: 'Password',
				save: 'Save',
				logOut: 'Log Out',
				phone: 'Phone',
				msg: 'User name cannot be empty',
				personalCenter: 'personal center',
			},
			orderConfirm: {
				setAddress: 'Set Address',
				navTitle: 'Place order',
				items: 'item(s)',
				coupons: 'coupons',
				integral: 'integral',
				freight: 'freight',
				note: 'describe',
				payPal: 'Payment',
				allPrice: 'Total amount',
				remaining: ' remaining',
				newIntegral: 'now',
				free: 'free',
				placeNote: 'Please add remarks(Less than 150)',
				CouponDeduction: 'Coupon deduction',
				total: 'Total'
			},
			orderList: {
				navTitle: 'My order',
				orderNum: 'Cumulative order',
				total: 'Total consumption',
				orderStatus: [{
						name: 'To Pay'
					},
					{
						name: 'Waiting'
					},
					{
						name: 'Received'
					},
					{
						name: 'Evaluation'
					},
					{
						name: 'Return'
					},
				],
				item: 'item(s)', // item(s)
				totalPay: 'Total Payment', //total payment
				cancelOrder: 'Cancel',
				viewDetails: 'View Details',
				evaluation: 'Evaluation', //evaluation
				evaluated: 'Evaluationd', //evaluation
				delete: 'Delete',
				empty: 'No order information',
				pay: 'Payment',
				complete: 'Complete'
			},
			userCoupon: {
				navTitle: 'My Coupons',
				available: 'Available',
				expired: 'Expired/Used',
				min: 'Least',
				can: 'Use Now',
				used: 'Expired',
				general: 'General',
				goods: 'Goods',
				cate: 'Category',
				receive: 'Get',
				received: 'Received',
				receivedInfo: 'Available within',
				receivedInfo1: 'day after receiving',
				navList: [{
						name: 'business'
					},
					{
						name: 'Goods'
					},
					{
						name: 'Category'
					},
				],
				empty: 'No coupons',
				valid: 'Valid for',
				day: 'day',
				minus: 'minus',
				getUsers: 'to use'
			},
			userGoodsCollection: {
				navTitle: 'My Wish List',
				item: 'item(s)',
				management: 'management',
				cancel: 'cancel',
				empty: 'There is no collection'
			},
			userAddressList: {
				navTitle: 'Address',
				default: 'Default address',
				add: 'Add a New Address',
				emptyAddress: 'No address infomation',
				edit: 'revise',
				del: 'delete',
				sureDel: 'Are you sure to delete this address'
			},
			userAddress: {
				navTitle: 'Address',
				name: 'Name',
				Email: 'Email',
				country: 'Country',
				address: 'Address',
				postCode: 'Post Code',
				phone: 'Phone',
				default: 'Default address',
				save: 'Save',
				place: 'Please enter the '
			},
			userReturnList: {
				navTitle: 'Return list',
				orderId: 'Refund Order No',
				item: 'tem(s)',
				total: 'Refund Payment ',
				refunding: 'Refunding',
				refunded: 'Refunded',
				examine: 'Business Review in progress',
				examineInfo: 'You have successfully initiated the refund application, please wait patiently for the merchant to process; Please negotiate with the merchant before refunding, which will help to better deal with after-sales problems',
				examine1: 'The merchant has refunded',
				refuse: 'Refuse refund',
				refuseReason: 'Reason for rejection：',
				refuseReason2: 'he refund has been accepted successfully. If the merchant has sent goods, please return them as soon as possible; Thank you for your support',
			},
			goodsCommentList: {
				navTitle: 'Product review',
				score: 'Score',
				good: 'Good',
				all: 'All',
				goodScore: 'Good',
				general: 'General',
				bad: 'Bad',
				attribute: 'Attribute',
				seller: 'seller'
			},
			goodsCommentCon: {
				navTitle: 'Product review',
				score: [{
						name: 'Product quality'
					},
					{
						name: 'Service attitude'
					}
				],
				place: 'Does the product meet your expectations? Share your ideas with those who want to buy',
				upload: 'Upload',
				submit: 'Submit',
				tips: 'Thank you for your comments',
				tipsWrite: 'Please fill in your experience of baby！',
				SU: 'Successful evaluation',
				tipsSU: 'We will continue to provide you with better goods and services'
			},
			goodsReturn: {
				navTitle: 'Application for return', //
				number: 'Number',
				price: 'Money',
				reason: 'Reason',
				info: 'remark',
				voucher: 'Upload certificate',
				place: 'additional notes for seller（max 100 character）',
				submit: 'Request a refund', //
				upload: 'Upload',
				detal: 'Refund order details',
				information: 'Refund information',
				refundList: 'Refund list'
			},
			login: {
				placeEmail: 'Please enter the email',
				placePasd: 'Please enter the password',
				sign: 'SIGN IN', //
				create: 'Create account', //
				forget: 'Forgot password?',
				quick: 'Quick login',
				pasdLogin: 'Password login',
				with: 'Sign In With',
				Pasdagain: 'password again',
				submit: 'Submit',
				next: 'Next',
				reset: 'RESET PASSWORD',
				resetDesc: " Please enter your email to receive a password reset link. ",
				emailVer: "Email verification",
				verDesc: 'For the safety of your account, please verify your email：',
				tourists: 'Anonymous purchase',
				continue: 'Continue',
				phoneVer: 'Phone verification',
				remember: 'Remember your password?'
			},
			register: {
				tabNav: [{
						name: 'Email'
					},
					{
						name: 'Phone'
					}
				],
				placeEmail: 'Please enter the email',
				placeCode: 'Please enter the captcha',
				placePhone: 'Phone number',
				submit: 'Submit',
				have: 'Have an account?',
				sign: 'Sign in'
			},
			logistics: {
				navTitle: 'logistics',
				expName: 'expName',
				expNo: 'expNo',
			},
			userIntegral: {
				now: 'Have Integral',
				totalInt: 'Total Integral',
				totalPay: 'Consumption',
				freeze: 'Freeze Integral',
				tabNav: [{
						name: "The Detail"
					},
					{
						name: "Ascension"
					},
				],
				shopAdd: 'You can earn points for your purchases',
				make: 'Add',
				empty: 'There is no record'
			},
			replyList: {
				navTitle: 'Evaluation list'
			}
		},
		store: {
			search: 'shop search',
			followed: 'Followed',
			follow: 'follow',
			storeRating: 'Rating',
			storeQualification: 'Qualification',
			storeIntroduction: 'Introduction',
			storeAddress: 'Address',
			storePhone: 'Phone',
			storeService: 'Contact Seller',
			storeTime: 'Join',
			storeName: 'Merchant name',
			userName: 'User name',
			phone: 'Phone',
			emil: 'e-mail',
			code: 'Code',
			getCode: 'Get code',
			class: 'Merchant classification',
			type: 'Store type',
			place1: 'Please upload the pictures of business license and industry related qualification certificate',
			place2: 'Up to 5 pictures can be uploaded, and the format supports JPG, PNG and JPEG',
			agree: 'Read and agree',
			agreement: 'Settlement agreement',
			submit: 'Submit application',
			agreement2: 'Merchant entry agreement',
			Tips1: 'Congratulations, your information has been submitted successfully!',
			Tips2: 'It is expected that the audit will be completed within 15 working days, and the platform customer service will contact you in time!',
			Tips3: 'Return to home page',
			title1: 'Merchant settlement',
			title2: 'Come and join us',
			street: 'Shop Street',
			keyword: 'keyword',
			merInfo: 'Shop details',
			index: 'home page',
			classify: 'classification',
			collar: 'Collect coupons',
			call: 'Service',
			people: ' Followers',
			delFollow: 'unfollow',
			selfSupport: 'self-support',
			homeNav: [{
					name: 'home page'
				},
				{
					name: 'classification'
				},
				{
					name: 'Collect coupons'
				}
			]
		}
	},
	message: {
		login: {
			loginLoading: 'logging in',
			getting: 'loading',
			emailEmpty: 'Please fill in your email address',
			correctEmail: 'Please fill in the correct email address', //Please fill in the correct email address
			emptyPassword: 'Please fill in your password',
			againPassword: 'Please confirm password.',
			diffPassword: 'The passwords are inconsistent',
			emptyPhone: 'Please fill in your phone',
			emptyCaptche: 'Please fill in your captche',
			correctCaptche: 'Please fill in the correct captche',
			loginSuccess: 'login success',
			resetSuccess: 'password reset success',
			name: 'Please fill in your name',
			detail: 'Please fill in your detailed address',
			postCode: 'Please fill in your post code',
			save: 'Is to save',
			updateSU: 'Modify the success',
			saveSU: 'Save success',
			prompt: 'prompt',
			confirmDel: 'Are you sure?',
			logout: 'Confirm logout?',
			notExist: 'Address does not exist',
			setSu: 'Set success',
			delSU: 'Delete success',
			calSU: 'Cancellation succeeded',
			send: 'send',
			resend: 'resend',
			remaining: 'cd',
			operationSU: 'Operation successful',
			loginSure: 'Do you want to use this password to keep login after modification？',
			loginFacebook: 'The current users is not logged in to Facebook or your web page',
			registerSU: 'login was successful',
			agreement: 'Please check the user privacy agreement',
			agree: 'I have read and agree',
			agreementName: 'User agreement'
		},
		pay: {
			orderFail: 'Pay for failure',
			error: 'Missing parameters cannot view order payment status',
			errorOrder: 'The order number is missing. The order details cannot be viewed'
		},
		orderConfirm: {
			payType: 'Please select payment method',
			emptyAddress: 'Please select the shipping address',
			payLoading: 'To pay'
		},
		tips: {
			time: 'Submission time',
			adopt: 'Approved',
			noadopt: 'Audit failed',
			reason: 'reason',
			stay: 'To be reviewed',
			edit: 'edit',
			see: 'see',
			again: 'Resubmit',
			shoppingSU: 'Shopping cart added successfully',
			loding: 'Loading...',
			selectAddress: 'Select address',
			selectOther: 'Choose another address',
			surePay: 'Are you sure to pay the order',
			sureDel: 'Are you sure to delete this order',
			upPic: 'Up to 5 copies can be uploaded',
			edidM: 'Click modify password',
			sold: 'Sold',
			changeGoods: 'Please select an item',
			picTips: 'Upload format is JPG, png',
			noEvaluation: 'no comments~',
			more: 'display more',
			hide: 'hide more',
			noDetal: 'No product details yet',
			getSU: 'Received successfully',
			browser: 'Due to the limited functionality of the 360 browser, please press Ctrl+D to manually collect!',
			nubrowser: 'Your browser does not support it, please press Ctrl+D to manually add!',
			error1: 'Sorry, there was an error on the page you visited',
			error2: 'Failed to load',
			error3: 'bad Request',
			error4: 'Please check your config file',
			placeLogin: 'please sign in'
		},
		settled: {
			emptyName: "Please enter the company name",
			emptyRealName: "Please enter your real name",
			emptyCaptcha: "Please enter the verification code",
			emptyCategory: "Please select merchant category",
			emptyIsAgree: "Please check and agree to the settlement agreement",
			emptyIsPicture: "Please upload the qualification picture",
			appleTips: 'There is no application record yet. Go and apply for admission'
		}
	}
}
