<template>
	<view class="" :data-theme="theme">
		<view class="pagebox">
			<view class="feetitle">
				建议或意见
			</view>
			<view class="textareacontainer">
				<textarea v-model="textareaValue" maxlength="200" placeholder="请输入您的建议或意见"
					@input="updateFontNum"></textarea>
				<view class="char-count">{{ fontNum }}/200</view>
			</view>
		</view>
		<view class="freebutton" @click="tijiaoyijian">
			<button class="freebutton-btn">
				提交
			</button>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		agreementInfo
	} from '@/api/api.js'
	import {
		userOut,
	} from '@/api/user.js'
	import {
		mapGetters
	} from "vuex";
	let app = getApp();
	export default {
		name: 'user_about',
		data() {
			return {
				textareaValue: '',
				fontNum: 0
			}
		},
		onLoad: function(options) {
			this.type = options.from;
			this.setTitle(this.type);
			this.getCacheinfo();
		},
		methods: {
			tijiaoyijian(){
				if(!this.textareaValue){
					uni.showToast({
						title:'请先输入您的建议或意见',
						icon:'none'
					})
					return
				}
				uni.showLoading({
					title:'正在提交'
				})
				setTimeout(()=>{
					uni.hideLoading()
					uni.showToast({
						title:'提交成功',
						icon:'none'
					})
					this.textareaValue = ""
				},2000)
			},
			updateFontNum(e) {
				this.fontNum = e.detail.value.length;
			},
			getCacheinfo() {
				this.loaded = false;
				agreementInfo(this.type).then(res => {
					this.agreementData = JSON.parse(res.data).agreement
					this.loaded = true;
				})
			},

		}
	}
</script>

<style lang="scss">
	.pagebox {
		padding: 20rpx;
	}

	.feetitle {
		font-size: 32rpx;
		font-weight: 600;
		text-align: left;
		color: #222222;
		line-height: 44rpx;
	}

	.textareacontainer {
		position: relative;
		margin-top: 20rpx;
		background-color: #fff;
		padding: 20rpx;

		textarea {
			width: 100%;
		}
	}

	.char-count {
		position: absolute;
		right: 4%;
		bottom: 7%;
		color: #999999;
		font-size: 24rpx;
	}

	.freebutton {
		width: 100%;
		// background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		padding: 17rpx 0;
		padding-bottom: 15rpx;
		padding-bottom: calc(15rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);
	}

	.freebutton-btn {
		margin-left: 20rpx;
		margin-right: 20rpx;
		// width: 690rpx;
		// height: 86rpx;
		// margin: 0 auto;
		line-height: 88rpx;
		color: #FFFFFF;
		border-radius: 43rpx;
		// @include main_bg_color(theme);
		font-size: 32rpx;
		// width: 710rpx;
		height: 88rpx;
		background: #bdfd5b !important;
		border-radius: 16rpx;
		font-size: 32rpx;
		font-weight: 600;
		text-align: center;
		color: #222222;
	}
</style>