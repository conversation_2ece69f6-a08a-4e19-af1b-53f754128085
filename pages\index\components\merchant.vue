<template>
	<view :data-theme="theme">
		<view class='indexList tui-skeleton'>
			<view class='title acea-row row-between-wrapper'>
				<view class='text line1 tui-skeleton-rect'>
					<image src="../../../static/images/haodian.png"></image>
					<text class='label'>正品大牌低价购</text>
				</view>
				<view class='more tui-skeleton-rect' hover-class="none" @click="more()">
					MORE
					<text class="iconfont icon-jiantou"></text>
				</view>
			</view>
			<view class='tips'>Local store</view>
			<view class='merList'>
				<view class='item' v-for="(item,index) in merList" :key='index'>
					<navigator :url="`/pages/merchant/home/<USER>" hover-class="none">
						<view class='pic tui-skeleton-rect'>
							<image :src='item.coverImage' class="picImg"></image>
						</view>
						<image class="lines left" src="../../../static/images/lianjie.png"></image>
						<view class='logo tui-skeleton-rect'>
							<image :src='item.rectangleLogo'></image>
						</view>
						<image class="lines right" src="../../../static/images/lianjie.png"></image>
						<view class='merName tui-skeleton-rect'>
							<view class='neme'>{{item.name}}</view>
							<view><text class='label'>{{item.typeId | merchantTypeFilter}}</text></view>
						</view>
					</navigator>
				</view>
			</view>
			<view class="no-mer" v-if="merList.length == 0">本地暂时没有可提供服务的门店</view>
		</view>
	</view>		
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		indexMerListApi
	} from '@/api/merchant.js';
	let app = getApp()
	export default {
		props: {
			userAddress: {
				type: Object,
				default: function() {
					return null;
				}
			},
		},
		data() {
			return {
				theme:app.globalData.theme,
				merList: [{},{},{},{},{},{}],
			}
		},
		mounted() {
			this.$store.dispatch('MerCategoryList');
			this.$store.dispatch('MerTypeList');
			this.getMerList();
		},
		methods: {
			more(i){
				uni.navigateTo({
					url:'/pages/merchant/street/index'
				})
			},
			getMerList(){
				let params = {}; 
				if (this.userAddress && this.userAddress.city && this.userAddress.district) {
					params.city = this.userAddress.city;
					params.district = this.userAddress.district;
				}
				indexMerListApi(params).then((res) => {
					this.merList = res.data;
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.indexList{
	  background-color: initial;
	  padding: 0;
	}
	.merList{
		.item:nth-child(3n) {
			margin-right: 0;
		}
		.item{
			width: 33.33%;
			position: relative;
			display: inline-block;
			padding: 0 8rpx;
			margin-top: 20rpx;
			.lines{
				width: 8rpx;
				height: 34rpx;
				position: absolute;
				z-index: 1;
			}
			.left{
				left: 14rpx;
				top: 204rpx;
			}
			.right{
				right: 14rpx;
				top: 204rpx;
			}
			.pic{
				height: 220rpx;
				border-radius: 14rpx;
				overflow: hidden;
				/deep/easy-loadimage,/deep/.easy-loadimage ,uni-image, .easy-loadimage , image{
					width: 100% !important;
					height: 100% !important;
					border-radius: 14rpx;
				}
			}
			
			.logo{
				width: 150rpx;
				height: 44rpx;
				background: #FFFFFF;
				box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(0,0,0,0.1000);
				border-radius: 33rpx;
				opacity: 1;
				margin-left: -75rpx;
				position: absolute;
				z-index: 1;
				left: 50%;
				top: 59%;
				image{
					margin: auto;
					width: 130rpx;
					height: 44rpx;
					display: block;
				}
			}
			.merName{
				height: 110rpx;
				background: #FFFFFF;
				border-radius: 14rpx;
				opacity: 1;
				padding: 30rpx 0 14rpx 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				.neme{
					font-weight: bold;
					color: #333333;
					font-size: 22rpx;
					text-align: center;
					margin-bottom: 4rpx;
				}
				.label{
					height: 28rpx;
					line-height: 28rpx;
					@include main_bg_color(theme);
					border-radius: 14rpx;
					opacity: 1;
					text-align: center;
					font-size:18rpx;
					color: #fff;
					padding: 0 12rpx;
				}
			}
		}
	}
	.no-mer {
		font-size: 26rpx;
		color: #999;
		text-align: center;
		padding: 20rpx 0;
	}
</style>