<template>
	<view class="order-detail-page" :data-theme="theme">
		<!-- 顶部导航栏（适配微信小程序胶囊） -->
		<view class="nav-header" :style="'height:' + (navHeight + statusBarHeight) + 'px;padding-top:' + statusBarHeight + 'px;'">
			<view class="nav-content" :style="'height:' + navHeight + 'px;'">
				<view class="nav-left" @click="goBack">
					<text class="iconfont icon-fanhui"></text>
				</view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-area" :style="'margin-top:' + (navHeight + statusBarHeight) + 'px;'">
					<!-- 支付倒计时提示文字（在第一个白色卡片上方） -->
		<!-- 优先检查退款状态 -->
		<view class="payment-countdown-text refunded" v-if="orderInfo.refundStatus === 3">
			<image src="/static/images/wait_indent2.png" mode="aspectFill"></image>
			<text>已退款</text>
		</view>
		<view class="payment-countdown-text" v-else-if="orderInfo.status === 0 && paymentCountdown > 0">
			请在{{ formatCountdown(paymentCountdown) }}前完成支付
		</view>
		<view class="payment-countdown-text cancelled" v-else-if="orderInfo.status === 9">
			<image src="/static/images/icon_indent.png" mode="aspectFill"></image>
			<text>已取消</text>
		</view>
		<view class="payment-countdown-text cancelled" v-else-if="orderInfo.status === 1">
			<image src="/static/images/wait_indent2.png" mode="aspectFill"></image>
			<text>{{ getShippingTypeText() }}</text>
		</view>
		<view class="payment-countdown-text cancelled" v-else-if="orderInfo.status === 3 || orderInfo.status === 4">
			<image src="/static/images/wait_indent2.png" mode="aspectFill"></image>
			<text>{{ getShippingTypeText() }}</text>
		</view>
		<view class="payment-countdown-text cancelled" v-else-if="orderInfo.status === 5">
			<image src="/static/images/wait_indent2.png" mode="aspectFill"></image>
			<text>{{ allProductsEvaluated ? '已评价' : '待评价' }}</text>
		</view>
		<view class="payment-countdown-text cancelled" v-else-if="orderInfo.status === 6">
			<image src="/static/images/wait_indent2.png" mode="aspectFill"></image>
			<text>已完成</text>
		</view>
			
					<!-- 第一个白色卡片：支付信息 -->
		<view class="payment-card">
			<!-- 优先检查退款状态 -->
			<view v-if="orderInfo.refundStatus === 3" class="payment-content">
				<view class="payment-info">
					<view class="payment-amount">
						<text class="amount-label">已退款</text>
					</view>
					<view class="payment-tip">退款已完成，感谢您的理解~</view>
				</view>
				<view class="pay-btn" @click="buyAgain">再次购买</view>
			</view>
			<view v-else-if="orderInfo.status === 0 && paymentCountdown > 0" class="payment-content">
				<view class="payment-info">
					<view class="payment-amount">
						<text class="amount-label">{{ isIntegralOrder() ? '支付积分' : '支付金额' }}</text>
						<text class="amount-value">{{ formatPayPrice() }}</text>
					</view>
					<view class="payment-tip">为了避免订单取消请尽快支付哦~</view>
				</view>
				<view class="pay-btn" @click="goPay">立即支付</view>
			</view>
			<view v-else-if="orderInfo.status === 0 && paymentCountdown <= 0" class="payment-content">
				<view class="payment-info">
					<view class="payment-tip" style="color: #333333;font-weight: bold;margin-bottom: 10rpx;">支付时间已过期</view>
					<view class="payment-tip">订单将自动取消，您可以重新下单</view>
				</view>
				<view class="pay-btn disabled" style="background-color: #ccc; color: #999;">支付已过期</view>
			</view>
			<view v-else-if="orderInfo.status === 9" class="payment-content">
				<view class="payment-info">
					<view class="payment-tip" style="color: #333333;font-weight: bold;margin-bottom: 10rpx;">超时未支付已取消</view>
					<view class="payment-tip">为了避免订单取消请尽快支付哦~</view>
				</view>
				<view class="pay-btn" @click="buyAgain">再次购买</view>
			</view>
			<view v-else-if="orderInfo.status === 1" class="payment-content">
				<view class="payment-info">
					<view class="payment-amount">
						<text class="amount-label">{{ getStatusText() }}</text>
					</view>
					<view class="payment-tip">{{ getStatusTip() }}</view>
				</view>
				<view class="pay-btn" @click="cancelOrder">取消订单</view>
			</view>
			<view v-else-if="orderInfo.status === 3 || orderInfo.status === 4" class="payment-content">
				<view class="payment-info">
					<view class="payment-amount">
						<text class="amount-label">{{ getStatusText() }}</text>
					</view>
					<view class="payment-tip">{{ getStatusTip() }}</view>
				</view>
				<view class="pay-btn" @click="handleReceiveAction">
					{{ merchantOrder && merchantOrder.shippingType === 2 ? '查看提货码' : '确认收货' }}
				</view>
			</view>
			<view v-else-if="orderInfo.status === 5" class="payment-content">
				<view class="payment-info">
					<view class="payment-amount">
						<text class="amount-label">{{ allProductsEvaluated ? '已评价' : '待评价' }}</text>
					</view>
					<view class="payment-tip">{{ allProductsEvaluated ? '感谢您的评价~' : '请对我们的服务做出评价吧~' }}</view>
				</view>
			</view>
			<view v-else-if="orderInfo.status === 6" class="payment-content">
				<view class="payment-info">
					<view class="payment-amount">
						<text class="amount-label">订单已完成</text>
					</view>
					<view class="payment-tip">感谢您的信任与支持~</view>
				</view>
				<view class="pay-btn" @click="buyAgain">再次购买</view>
			</view>
		</view>
			
			<!-- 第二个白色卡片：商店信息 + 商品信息 -->
			<view class="merchant-product-card">
							<!-- 商店信息部分 -->
			<view class="merchant-section" v-if="merchantOrder">
				<view class="merchant-header">
					<text class="delivery-tag">{{ getDeliveryTag() }}</text>
					<text class="merchant-name">{{ merchantOrder.merName || merchantOrder.storesName }}</text>
				</view>
				<view class="delivery-time" v-if="merchantOrder.shippingTimeJoint">
					{{ getDeliveryTypeText() }}：{{ merchantOrder.shippingTimeJoint }}
				</view>
			</view>
			
			<!-- 商品信息部分 -->
			<view class="products-section">
				<view v-for="(product, index) in merchantOrder.orderInfoList" :key="index" class="product-item">
											<!-- 商品上部分：图片、标题、规格、金额、数量 -->
					<view class="product-main">
						<view class="product-image">
							<image :src="product.image" mode="aspectFill"></image>
						</view>
						<view class="product-info">
							<view class="product-name">{{ product.productName }}</view>
							<view class="product-spec">{{ product.sku }}</view>
							<view class="product-price">{{ formatPrice(product.price) }}</view>
						</view>
						<view class="product-quantity">x{{ product.payNum }}</view>
					</view>

					<!-- 立即评价按钮 - 仅在待评价状态且商品未评价时显示 -->
					<view v-if="orderInfo.status === 5 && product.isReply === false" class="product-evaluate-btn">
						<view class="evaluate-btn" @click="goToEvaluate(product)">立即评价</view>
					</view>

					<!-- 眼镜定制信息 -->
					<glassesProductCustomInfo v-if="product.customData && (product.customData.lens || product.customData.optometry)" :customData="product.customData"></glassesProductCustomInfo>
					</view>
					
									<!-- 商品合计信息 -->
				<view class="product-summary">
					<view class="summary-right">
						<text class="summary-price">已选{{ orderInfo.totalNum }}件</text>
						<text class="summary-price">合计：<text class="price-highlight">{{ formatPrice(orderInfo.totalPrice) }}</text></text>
					</view>
				</view>
				</view>
			</view>
			
					<!-- 价格明细 -->
		<view class="price-detail">
			<view class="price-title">{{ isIntegralOrder() ? '积分明细' : '价格明细' }}</view>
			
			<!-- 积分订单明细 -->
			<template v-if="isIntegralOrder()">
				<view class="price-item">
					<text>消耗积分</text>
					<text style="color: #FF2222;">{{ merchantOrder && merchantOrder.useIntegral ? merchantOrder.useIntegral : orderInfo.payPrice }}积分</text>
				</view>
				<view class="price-item total">
					<text>合计</text>
					<text style="color: #FF2222;">{{ formatPayPrice() }}</text>
				</view>
			</template>
			
			<!-- 普通订单价格明细 -->
			<template v-else>
				<view class="price-item">
					<text>商品金额</text>
					<text style="color: #FF2222;">¥{{ orderInfo.proTotalPrice }}</text>
				</view>
				<view class="price-item" v-if="orderInfo.payPostage > 0">
					<text>配送费</text>
					<text class="discount">¥{{ orderInfo.payPostage }}</text>
				</view>
				<view class="price-item" v-if="orderInfo.couponPrice > 0">
					<text>优惠券</text>
					<text class="discount">-¥{{ orderInfo.couponPrice }}</text>
				</view>
				<view class="price-item" v-if="orderInfo.integralPrice > 0">
					<text>积分抵扣</text>
					<text class="discount">-¥{{ orderInfo.integralPrice }}</text>
				</view>
				<view class="price-item total">
					<text>合计</text>
					<text style="color: #FF2222;">¥{{ orderInfo.payPrice }}</text>
				</view>
			</template>
		</view>
			
					<!-- 配送信息 -->
		<view class="delivery-info">
			<!-- 快递信息 (shippingType === 1) -->
			<template v-if="merchantOrder && merchantOrder.shippingType === 1">
				<view class="delivery-title">快递信息</view>
				<view class="delivery-item">
					<text>配送方式</text>
					<text>{{ getDeliveryMethodText() }}</text>
				</view>
				<view class="delivery-item" v-if="orderInfo.status >= 1" @click="viewLogistics">
					<text>物流信息</text>
					<view class="express-info">
						<text>{{ orderInfo.deliveryName || '快递' }} {{ orderInfo.deliveryId || '' }}</text>
						<text class="iconfont icon-xiangyou"></text>
					</view>
				</view>
				<view class="delivery-item">
					<text>收货地址</text>
					<text>{{ merchantOrder.userAddress }}</text>
				</view>
			</template>
			
			<!-- 自提人信息 (shippingType === 2) -->
			<template v-else-if="merchantOrder && merchantOrder.shippingType === 2">
				<view class="delivery-title">自提人信息</view>
				<view class="delivery-item">
					<text>配送方式</text>
					<text>{{ getDeliveryMethodText() }}</text>
				</view>
				<view class="delivery-item" v-if="merchantOrder.shippingTimeJoint">
					<text>自提时间</text>
					<text>{{ merchantOrder.shippingTimeJoint }}</text>
				</view>
				<view class="delivery-item">
					<text>自提地址</text>
					<text>{{ merchantOrder.merAddressDetail }}</text>
				</view>
				<view class="delivery-item" v-if="merchantOrder.linkman">
					<text>联系人</text>
					<text>{{ merchantOrder.linkman }}</text>
				</view>
				<view class="delivery-item" v-if="merchantOrder.phone">
					<text>联系电话</text>
					<text>{{ merchantOrder.phone }}</text>
				</view>
			</template>
			
			<!-- 配送信息 (shippingType === 3) -->
			<template v-else-if="merchantOrder && merchantOrder.shippingType === 3">
				<view class="delivery-title">配送信息</view>
				<view class="delivery-item">
					<text>配送方式</text>
					<text>{{ getDeliveryMethodText() }}</text>
				</view>
				<view class="delivery-item" v-if="merchantOrder.shippingTimeJoint">
					<text>送达时间</text>
					<text>{{ merchantOrder.shippingTimeJoint }}</text>
				</view>
				<view class="delivery-item">
					<text>配送地址</text>
					<text>{{ merchantOrder.userAddress }}</text>
				</view>
				<view class="delivery-item" v-if="merchantOrder.linkman">
					<text>配送员</text>
					<text>{{ merchantOrder.linkman }}</text>
				</view>
				<view class="delivery-item" v-if="merchantOrder.phone">
					<text>联系电话</text>
					<text>{{ merchantOrder.phone }}</text>
				</view>
			</template>
			
			<!-- 默认配送信息 -->
			<template v-else>
				<view class="delivery-title">配送信息</view>
				<view class="delivery-item">
					<text>配送方式</text>
					<text>{{ getDeliveryMethodText() }}</text>
				</view>
			</template>
		</view>
			
					<!-- 订单信息 -->
		<view class="order-info">
			<view class="order-title">订单信息</view>
			<view class="order-item">
				<text>订单编号</text>
				<text>{{ orderInfo.orderNo }}</text>
			</view>
			<view class="order-item">
				<text>下单时间</text>
				<text>{{ orderInfo.createTime }}</text>
			</view>
			<view class="order-item" v-if="orderInfo.paid">
				<text>支付方式</text>
				<text>{{ getPayTypeText() }}</text>
			</view>
			<view class="order-item" v-if="orderInfo.payTime">
				<text>支付时间</text>
				<text>{{ orderInfo.payTime }}</text>
			</view>
			<view class="order-item">
				<text>订单备注</text>
				<text>{{ merchantOrder && merchantOrder.userRemark || '无' }}</text>
			</view>
		</view>
		</view>
		
			<!-- 底部操作栏 -->
	<view class="bottom-actions" >
		<view class="action-btn secondary" v-if="orderInfo.status === 0" @click="cancelOrder">取消订单</view>
		<view class="action-btn secondary" v-if="orderInfo.status === 1 && canApplyAftersale" @click="applyAftersale">申请售后</view>
		<view class="action-btn secondary" v-if="orderInfo.status === 5 && canApplyAftersale" @click="applyAftersale">申请售后</view>
		<view class="action-btn secondary" v-if="orderInfo.status === 6 && canApplyAftersale" @click="applyAftersale">申请售后</view>
		<button class="action-btn primary" open-type="contact">联系客服</button>
	</view>
		
		<!-- 售后申请弹窗 -->
		<view class="aftersale-modal" v-if="showAftersaleModal" @click="closeAftersaleModal">
			<view class="modal-content" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<text class="modal-title">申请售后</text>
					<view class="close-btn" @click="closeAftersaleModal">
						<text class="iconfont icon-guanbi"></text>
					</view>
				</view>
				
				<!-- 售后类型选择 -->
				<view class="aftersale-types">
					<view
						class="type-btn"
						:class="{ 'active': aftersaleType === 'refund' }"
						@click="selectAftersaleType('refund')"
					>
						仅退款
					</view>
					<!-- 待发货状态只显示仅退款，已完成订单不显示换货选项 -->
					<view
						class="type-btn"
						:class="{ 'active': aftersaleType === 'exchange' }"
						@click="selectAftersaleType('exchange')"
						v-if="orderInfo.status !== 6 && orderInfo.status !== 1"
					>
						换货
					</view>
					<view
						class="type-btn"
						:class="{ 'active': aftersaleType === 'return' }"
						@click="selectAftersaleType('return')"
						v-if="orderInfo.status !== 1"
					>
						退货退款
					</view>
				</view>
				
				<!-- 售后原因选择 -->
				<view class="reason-list">
					<view 
						class="reason-item" 
						v-for="(reason, index) in aftersaleReasons" 
						:key="index"
						@click="selectReason(index)"
					>
						<text class="reason-text">{{ reason.text }}</text>
						<view class="radio-btn" :class="{ 'selected': selectedReason === index }">
							<view class="radio-inner" v-if="selectedReason === index"></view>
						</view>
					</view>
				</view>
				
				<!-- 下一步按钮 -->
				<view class="next-btn" @click="nextStep">下一步</view>
			</view>
		</view>
		
		<!-- 提货码弹窗 -->
		<view class="pickup-code-modal" v-if="showPickupCodeModal" @click="closePickupCodeModal">
			<view class="modal-content" @click.stop>
				<!-- 弹窗头部 -->
				<view class="modal-header">
					<view class="close-btn" @click="closePickupCodeModal">
						<text class="iconfont icon-guanbi"></text>
					</view>
				</view>
				
				<!-- 提货码内容 -->
				<view class="pickup-code-content">
					<view class="pickup-code-number">{{ merchantOrder && merchantOrder.verifyCode || '暂无提货码' }}</view>
					<view class="pickup-code-tip">向商家出示提货码或订单号</view>
				</view>
				
				<!-- 确认按钮 -->
				<view class="confirm-btn" @click="closePickupCodeModal">确认</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getOrderDetail,
		orderTake,
		orderCancel,
	} from '@/api/order.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		Debounce
	} from '@/utils/validate.js';
	import glassesProductCustomInfo from '@/components/glassesProductCustomInfo';
	
	export default {
		components: {
			glassesProductCustomInfo
		},
		computed: {
			...mapGetters(['isLogin']),
			// 判断是否可以申请售后
			canApplyAftersale() {
				// refundStatus 为 undefined、null、0（待审核）或 2（退款中）时可以申请售后
				const refundStatus = this.orderInfo.refundStatus;
				return refundStatus === undefined || refundStatus === null || refundStatus === 0 || refundStatus === 2;
			},
			// 判断所有商品是否都已评价
			allProductsEvaluated() {
				if (!this.merchantOrder || !this.merchantOrder.orderInfoList || this.merchantOrder.orderInfoList.length === 0) {
					return false;
				}
				return this.merchantOrder.orderInfoList.every(product => product.isReply === true);
			}
		},
		data() {
			return {
				theme: 'default',
				orderNo: '',
				statusBarHeight: 0,
				navHeight: 44,
				paymentCountdown: 0, // 倒计时秒数
				countdownTimer: null,
				paymentTimeLimit: 30 * 60, // 支付时限30分钟(秒)
				// 售后弹窗相关数据
				showAftersaleModal: false,
				aftersaleType: 'return', // 默认选择退货退款
				selectedReason: 0, // 默认选择第一个原因
				aftersaleReasons: [
					{ text: '不想要了' },
					{ text: '订单信息有误（规格/颜色等）' },
					{ text: '地址/电话信息填写错误' },
					{ text: '没用/少用优惠券' },
					{ text: '商品缺货商家联系我取消' }
				],
				// 提货码弹窗相关数据
				showPickupCodeModal: false,
				// 订单数据
				orderInfo: {},
				merchantOrder: null
			};
		},
		onLoad(options) {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
			
			// 获取胶囊按钮信息以适配导航栏
			// #ifdef MP-WEIXIN
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			// 导航栏高度 = 胶囊高度 + 胶囊到顶部的距离 + 胶囊到底部的间距
			this.navHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;
			// #endif
			
			// #ifndef MP-WEIXIN
			this.navHeight = 44;
			// #endif
			
			if (options.orderNo) {
				this.orderNo = options.orderNo;
			}
		},
		onShow() {
			if (this.isLogin) {
				this.getOrderInfo();
			} else {
				toLogin();
			}
		},
		onHide() {
			this.stopCountdown();
		},
		onUnload() {
			this.stopCountdown();
		},
		methods: {
			/**
			 * 获取订单详细信息
			 */
			getOrderInfo() {
				let that = this;
				uni.showLoading({
					title: "正在加载中"
				});
				getOrderDetail(that.orderNo).then(res => {
					uni.hideLoading();
					let data = res.data;
					
					// 处理商户订单列表中的customData
					if (data.merchantOrderList && data.merchantOrderList.length > 0) {
						data.merchantOrderList.forEach((merchantOrder) => {
							if (merchantOrder.orderInfoList && merchantOrder.orderInfoList.length > 0) {
								merchantOrder.orderInfoList.forEach((product) => {
									let customData = product.customData;
									if (customData) {
										try {
											product.customData = JSON.parse(customData);
										} catch (e) {
											product.customData = customData;
										}
									}
								});
							}
						});
						that.$set(that, 'merchantOrder', data.merchantOrderList[0]);
					}
					
					that.$set(that, 'orderInfo', data);
					
					// 计算支付倒计时并启动倒计时
					that.calculatePaymentCountdown();
					that.startCountdown();
				}).catch(err => {
					uni.hideLoading();
					that.$util.Tips({
						title: err
					}, {
						tab: 4,
						url: '/pages/user/index'
					});
				});
			},
			
			/**
			 * 获取配送方式标签文本
			 */
			getDeliveryTag() {
				if (!this.merchantOrder) return '配送';
				switch (this.merchantOrder.shippingType) {
					case 1:
						return '快递';
					case 2:
						return '自提';
					case 3:
						return '配送';
					default:
						return '配送';
				}
			},
			
			/**
			 * 获取配送方式文本
			 */
			getDeliveryTypeText() {
				if (!this.merchantOrder) return '';
				switch (this.merchantOrder.shippingType) {
					case 1:
						return '快递配送';
					case 2:
						return '门店自提';
					case 3:
						return '门店配送';
					default:
						return '配送';
				}
			},
			
			/**
			 * 获取配送方式提示文本
			 */
			getShippingTypeText() {
				if (!this.merchantOrder) return '待发货';
				
				// 根据订单状态和配送方式显示不同文本
				if (this.orderInfo.status === 4 || this.orderInfo.status === 3) { // 待收货/待核销状态
					switch (this.merchantOrder.shippingType) {
						case 1: // 快递
						case 3: // 配送
							return '门店配送-待收货';
						case 2: // 自提
							return '门店自提-待自提';
						default:
							return '待收货';
					}
				} else {
					// 其他状态保持原逻辑
					switch (this.merchantOrder.shippingType) {
						case 1:
							return '快递配送-待发货';
						case 2:
							return '门店自提-待核销';
						case 3:
							return '门店配送-待发货';
						default:
							return '待发货';
					}
				}
			},
			
			/**
			 * 获取状态文本
			 */
			getStatusText() {
				if (!this.merchantOrder) return '';
				switch (this.orderInfo.status) {
					case 1:
						if (this.merchantOrder.shippingType === 2) {
							return '等待您到店核销';
						} else {
							return '商家正在备货中';
						}
					case 3:
					case 4:
						// 待收货/待核销状态根据配送方式显示不同文本
						if (this.merchantOrder.shippingType === 2) {
							return '商家备货已完成等待自提';
						} else {
							return '商家正在配送中';
						}
					default:
						return '';
				}
			},
			
			/**
			 * 获取状态提示
			 */
			getStatusTip() {
				if (!this.merchantOrder) return '';
				switch (this.orderInfo.status) {
					case 1:
						if (this.merchantOrder.shippingType === 2) {
							return '请到店出示核销码完成核销~';
						} else {
							return '备货完后尽快给您配送哦~';
						}
					case 3:
					case 4:
						// 待收货/待核销状态根据配送方式显示不同提示
						if (this.merchantOrder.shippingType === 2) {
							return '备货完成后请前往门店取货~';
						} else {
							return '备货完后尽快给您配送哦~';
						}
					default:
						return '';
				}
			},
			
			/**
			 * 获取配送方式文本
			 */
			getDeliveryMethodText() {
				if (!this.merchantOrder) return '';
				switch (this.merchantOrder.shippingType) {
					case 1:
						return '快递配送';
					case 2:
						return '门店自提';
					case 3:
						return '门店配送';
					default:
						return '';
				}
			},
			
			/**
			 * 获取支付方式文本
			 */
			getPayTypeText() {
				switch (this.orderInfo.payType) {
					case 'weixin':
						return '微信支付';
					case 'alipay':
						return '支付宝';
					case 'yue':
						return '余额支付';
					case 'jf':
						return '积分支付';
					default:
						return this.orderInfo.payType || '';
				}
			},
			
			/**
			 * 判断是否为积分订单
			 */
			isIntegralOrder() {
				return this.orderInfo.payType === 'jf';
			},
			
			/**
			 * 格式化价格显示（积分订单显示积分，普通订单显示金额）
			 */
			formatPrice(price) {
				if (this.isIntegralOrder()) {
					// 积分订单显示积分数
					const integral = this.merchantOrder && this.merchantOrder.useIntegral ? this.merchantOrder.useIntegral : price;
					return `${integral}积分`;
				} else {
					// 普通订单显示金额
					return `¥${price}`;
				}
			},
			
			/**
			 * 格式化支付金额显示
			 */
			formatPayPrice() {
				if (this.isIntegralOrder()) {
					// 积分订单显示积分数
					const integral = this.merchantOrder && this.merchantOrder.useIntegral ? this.merchantOrder.useIntegral : this.orderInfo.payPrice;
					return `${integral}积分`;
				} else {
					// 普通订单显示金额
					return `¥${this.orderInfo.payPrice || 0}`;
				}
			},
			
			/**
			 * 计算支付倒计时
			 */
			calculatePaymentCountdown() {
				// 只有待付款状态才需要倒计时
				if (this.orderInfo.status !== 0 || !this.orderInfo.createTime) {
					this.paymentCountdown = 0;
					return;
				}
				
				try {
					// 解析订单创建时间
					let createTime = new Date(this.orderInfo.createTime.replace(/-/g, '/'));
					// 计算支付截止时间（创建时间 + 30分钟）
					let expireTime = new Date(createTime.getTime() + this.paymentTimeLimit * 1000);
					// 当前时间
					let nowTime = new Date();
					
					// 计算剩余秒数
					let remainingSeconds = Math.floor((expireTime.getTime() - nowTime.getTime()) / 1000);
					
					if (remainingSeconds <= 0) {
						// 已过期，设置为已取消状态
						this.paymentCountdown = 0;
						// 这里可以选择是否自动更新订单状态为已取消
						// this.$set(this.orderInfo, 'status', 9);
					} else {
						this.paymentCountdown = remainingSeconds;
					}
				} catch (e) {
					console.error('计算支付倒计时失败:', e);
					this.paymentCountdown = 0;
				}
			},

			goBack() {
				uni.navigateBack();
			},
			formatCountdown(seconds) {
				if (seconds <= 0) return '00:00';
				const m = Math.floor(seconds / 60);
				const s = seconds % 60;
				return `${this.padZero(m)}:${this.padZero(s)}`;
			},
			padZero(num) {
				return num < 10 ? '0' + num : num;
			},
			startCountdown() {
				if (this.orderInfo.status !== 0) return;
				
				// 首先计算倒计时
				this.calculatePaymentCountdown();
				
				// 如果没有倒计时，直接返回
				if (this.paymentCountdown <= 0) return;
				
				this.stopCountdown();
				this.countdownTimer = setInterval(() => {
					if (this.paymentCountdown > 0) {
						this.paymentCountdown--;
					} else {
						// 倒计时结束，重新计算以确保准确性
						this.calculatePaymentCountdown();
						if (this.paymentCountdown <= 0) {
							this.stopCountdown();
						}
					}
				}, 1000);
			},
			stopCountdown() {
				if (this.countdownTimer) {
					clearInterval(this.countdownTimer);
					this.countdownTimer = null;
				}
			},
			goPay() {
				console.log('去支付');

				// 检查倒计时是否结束
				if (this.paymentCountdown <= 0) {
					uni.showToast({
						title: '支付时间已过期',
						icon: 'none'
					});
					return;
				}

				uni.navigateTo({
					url: `/pages/goods/order_payment/index?orderNo=${this.orderInfo.orderNo}&payPrice=${this.orderInfo.payPrice}`
				});
			},
			buyAgain: Debounce(function() {
				// 其他状态执行再次购买逻辑 - 跳转到商品详情页面
				if (this.merchantOrder && this.merchantOrder.orderInfoList && this.merchantOrder.orderInfoList.length > 0) {
					const firstProduct = this.merchantOrder.orderInfoList[0];
					const productId = firstProduct.productId;

					if (productId) {
						uni.navigateTo({
							url: `/pages/goods/goods_details/index?id=${productId}`
						});
					} else {
						this.$util.Tips({
							title: '无法获取商品信息'
						});
					}
				} else {
					this.$util.Tips({
						title: '无法获取商品信息'
					});
				}
			}),

			// 跳转到单个商品的评价页面
			goToEvaluate(product) {
				const evaluateId = product.id || product.productId || 0;
				uni.navigateTo({
					url: `/pages/goods/goods_comment_con/index?orderNo=${this.orderInfo.orderNo}&id=${evaluateId}`
				});
			},

			cancelOrder: Debounce(function() {
				let self = this
				uni.showModal({
					title: '提示',
					content: '确认取消该订单?',
					success: function(res) {
						if (res.confirm) {
							orderCancel(self.orderNo)
								.then((data) => {
									self.$util.Tips({
										title: '取消成功'
									}, function() {
										self.getOrderInfo();
									})
								}).catch((err) => {
									self.$util.Tips({
										title: err
									})
								});
						}
					}
				});
			}),

			viewLogistics() {
				console.log('查看物流信息');
				uni.navigateTo({
					url: '/pages/goods/order_logistics/index?orderNo=' + this.orderNo
				});
			},
			confirmReceive: Debounce(function() {
				let that = this;
				uni.showModal({
					title: '确认收货',
					content: '为保障权益，请收到货确认无误后，再确认收货',
					success: function(res) {
						if (res.confirm) {
							orderTake(that.orderNo).then(res => {
								return that.$util.Tips({
									title: '操作成功',
									icon: 'success'
								}, function() {
									that.getOrderInfo();
								});
							}).catch(err => {
								return that.$util.Tips({
									title: err
								});
							})
						}
					}
				})
			}),
			applyAftersale() {
				console.log('申请售后');

				// 根据订单状态设置默认售后类型
				if (this.orderInfo.status === 1) {
					// 待发货状态默认选择仅退款
					this.aftersaleType = 'refund';
				} else {
					// 其他状态默认选择退货退款
					this.aftersaleType = 'return';
				}

				this.showAftersaleModal = true;
			},
			closeAftersaleModal() {
				this.showAftersaleModal = false;
			},
			handleReceiveAction() {
				// 根据配送方式决定执行哪个操作
				if (this.merchantOrder && this.merchantOrder.shippingType === 2) {
					// 自提方式：显示提货码
					this.showPickupCode();
				} else {
					// 快递/配送方式：确认收货
					this.confirmReceive();
				}
			},
			showPickupCode() {
				console.log('查看提货码');
				this.showPickupCodeModal = true;
			},
			closePickupCodeModal() {
				this.showPickupCodeModal = false;
			},
			selectAftersaleType(type) {
				this.aftersaleType = type;
			},
			selectReason(index) {
				this.selectedReason = index;
			},
			nextStep() {
				console.log('选择的售后类型:', this.aftersaleType);
				console.log('选择的售后原因:', this.aftersaleReasons[this.selectedReason].text);

				this.closeAftersaleModal();
				console.log("this.merchantOrder.orderInfoList",this.merchantOrder.orderInfoList);
				
				// 准备传递的商品数据，只传递 refundNum 等于0的商品
				const filteredOrderInfoList = this.merchantOrder.orderInfoList.filter(product => {
					console.log("product.refundNum",product.refundNum);
					if( product.refundNum == 0){
						console.log("product.refundNum",product.refundNum);
						return true;
					}
					return false;
				});

				// 检查是否有符合条件的商品
				if (filteredOrderInfoList.length === 0) {
					uni.showToast({
						title: '没有可退货的商品',
						icon: 'none'
					});
					return;
				}

				const merchantData = {
					merName: this.merchantOrder.merName || this.merchantOrder.storesName,
					shippingType: this.merchantOrder.shippingType,
					orderInfoList: filteredOrderInfoList
				};

				// 跳转到选择退货商品页面，传递订单数据
				uni.navigateTo({
					url: `/pages/goods/select_refund_goods/index?orderNo=${this.orderNo}&aftersaleType=${this.aftersaleType}&reason=${encodeURIComponent(this.aftersaleReasons[this.selectedReason].text)}&merchantData=${encodeURIComponent(JSON.stringify(merchantData))}`
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.order-detail-page {
		background: linear-gradient(180deg, #b8fe4e 0%, #f5f5f5 16%);
		min-height: 100vh;
		padding-bottom: 120rpx;
	}
	
	.nav-header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #b8fe4e;
		
		.nav-content {
			display: flex;
			align-items: center;
			padding: 0 30rpx;
			
			.nav-left {
				.iconfont {
					font-size: 40rpx;
					color: #333;
				}
			}
		}
	}
	
	.content-area {
		.payment-countdown-text {
			text-align: left;
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			margin: 40rpx 30rpx 20rpx;
			padding-top: 30rpx;
			
			&.cancelled {
				display: flex;
				align-items: center;
				color: #333333;
				image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 10rpx;
				}
			}

			&.refunded {
				display: flex;
				align-items: center;
				color: #333;
				image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 10rpx;
				}
			}
		}
	}
	
	.payment-card,
	.merchant-product-card,
	.price-detail,
	.delivery-info,
	.order-info {
		background-color: #fff;
		margin: 0 30rpx 20rpx;
		border-radius: 20rpx;
		padding: 20rpx 30rpx;
	}
	
	.payment-card {
		.payment-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			.payment-info {
				flex: 1;
				
				.payment-amount {
					margin-bottom: 10rpx;
					
					.amount-label {
						font-size: 28rpx;
						color: #333;
						margin-right: 20rpx;
					}
					
					.amount-value {
						font-size: 36rpx;
						color: #333;
						font-weight: 600;
					}
				}
				
				.payment-tip {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.pay-btn {
				background-color: #BDFD5B;
				color: #222222;
				padding: 20rpx 40rpx;
				border-radius: 15rpx;
				font-size: 28rpx;
				font-weight: 600;
			}
		}
	}
	
	.merchant-product-card {
		.merchant-section {
			margin-bottom: 30rpx;
			
			.merchant-header {
				display: flex;
				align-items: center;
				margin-bottom: 15rpx;
				
				.delivery-tag {
					background-color: #FF7F00;
					color: #fff;
					font-size: 22rpx;
					padding: 6rpx 12rpx;
					border-radius: 6rpx;
					margin-right: 15rpx;
				}
				
				.merchant-name {
					font-size: 30rpx;
					color: #333;
					font-weight: 600;
				}
			}
			
			.delivery-time {
				color: #FF3127;
				font-size: 26rpx;
			}
		}
		
		.products-section {
			.product-item {
				&:not(:last-child) {
					margin-bottom: 30rpx;
				}
				
				.product-main {
					display: flex;
					align-items: flex-start;
					margin-bottom: 20rpx;

					.product-image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 12rpx;
						overflow: hidden;
						margin-right: 20rpx;
						flex-shrink: 0;

						image {
							width: 100%;
							height: 100%;
						}
					}

					.product-info {
						flex: 1;
						height: 160rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						width: 200rpx;

						.product-name {
							font-size: 28rpx;
							color: #333;
							font-weight: 600;
							line-height: 1.4;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							overflow: hidden;
							word-break: break-all;
							/* 确保在不支持 -webkit-line-clamp 的浏览器中也能正常显示 */
							max-height: calc(1.4em * 2); /* line-height * 行数 */
						}

						.product-spec {
							font-size: 26rpx;
							color: #666;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.product-price {
							font-size: 32rpx;
							color: #FF2222;
							font-weight: 600;
						}
					}

					.product-quantity {
						align-self: center;
						font-size: 26rpx;
						color: #222222;
						margin-left: 20rpx;
					}
				}

				.product-evaluate-btn {
					display: flex;
					justify-content: flex-end;
					margin-bottom: 20rpx;

					.evaluate-btn {
						background-color: #BDFD5B;
						color: #222222;
						padding: 12rpx 30rpx;
						border-radius: 15rpx;
						font-size: 26rpx;
						font-weight: 600;
					}
				}
				
				.product-description {
					background-color: #f5f5f5;
					border-radius: 12rpx;
					padding: 20rpx;
					font-size: 24rpx;
					color: #999999;
					line-height: 1.5;
					margin-bottom: 20rpx;
				}
				
				.optometry-section {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 0 20rpx;
					font-size: 26rpx;
					color: #222222;
					margin-bottom: 20rpx;
					
					.iconfont {
						color: #999999;
						font-size: 24rpx!important;
					}
				}
			}
			
			.product-summary {
				padding-top: 20rpx;
				display: flex;
				justify-content: flex-end;
				
				.summary-right {
					text-align: right;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.summary-price :first-child {
						margin-right: 10rpx;
					}
					
					.summary-price {
						display: block;
						font-size: 28rpx;
						color: #222222;
						
						.price-highlight {
							color: #FF2222;
							font-weight: 600;
						}
					}
				}
			}
		}
	}
	
	.price-detail,
	.delivery-info,
	.order-info {
		.price-title,
		.delivery-title,
		.order-title {
			font-size: 30rpx;
			color: #222222;
			font-weight: 600;
			margin-bottom: 30rpx;
		}

		.price-item {
			color: #999999;
		}
		
		.delivery-item>text:first-child {
			color: #999999;
			flex: 1;
		}
		.delivery-item>text:last-child {
			color: #333333;
			text-align: right;
			flex: 2;
		}
		
		.express-info {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			flex: 2;
			
			text:first-child {
				color: #333333;
				margin-right: 10rpx;
			}
			
			.iconfont {
				color: #999999;
				font-size: 24rpx;
			}
		}

		.order-item>text:first-child {
			color: #999999;
			flex: 1;
		}
		.order-item>text:last-child {
			color: #333333;
		}

		.price-item,
		.delivery-item,
		.order-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			font-size: 26rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			&.total {
				font-size: 28rpx;
				font-weight: 600;
				color: #E93323;
				padding-top: 15rpx;
				margin-top: 15rpx;
			}
		}
		
		.discount {
			color: #222222;
		}
	}
	
	.bottom-actions {
		display: flex;
		gap: 20rpx;
		width: 50%;
		float: right;
		margin-right: 30rpx;
		
		.action-btn {
			flex: 1;
			text-align: center;
			padding: 15rpx;
			border-radius: 15rpx;
			font-size: 28rpx;
			background: none;
			outline: none;

			&.secondary {
				border: 1rpx solid #999999;
				color: #999999;
			}

			&.primary {
				border: 1rpx solid #999999;
				color: #999999;
			}
		}
	}
	
	/* 售后申请弹窗样式 */
	.aftersale-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9999;
		display: flex;
		align-items: flex-end;
		
		.modal-content {
			width: 100%;
			background-color: #fff;
			border-radius: 30rpx 30rpx 0 0;
			padding: 0 30rpx 30rpx;
			animation: slideUp 0.3s ease-out;
		}
	}
	
	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}
	
	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
		margin-bottom: 30rpx;
		
		.modal-title {
			font-size: 36rpx;
			color: #333;
			font-weight: 600;
		}
		
		.close-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.iconfont {
				font-size: 32rpx;
				color: #999;
			}
		}
	}
	
	.aftersale-types {
		display: flex;
		margin-bottom: 40rpx;
		
		.type-btn {
			flex: 1;
			height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #666;
			background-color: #f5f5f5;
			margin-right: 20rpx;
			border-radius: 35rpx;
			
			&:last-child {
				margin-right: 0;
			}
			
			&.active {
				background-color: #BDFD5B;
				color: #333;
				font-weight: 600;
			}
		}
	}
	
	.reason-list {
		margin-bottom: 40rpx;
		
		.reason-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 0;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.reason-text {
				font-size: 30rpx;
				color: #333;
				flex: 1;
			}
			
			.radio-btn {
				width: 40rpx;
				height: 40rpx;
				border: 2rpx solid #ddd;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				
				&.selected {
					border-color: #52c41a;
					
					.radio-inner {
						width: 20rpx;
						height: 20rpx;
						background-color: #52c41a;
						border-radius: 50%;
					}
				}
			}
		}
	}
	
	.next-btn {
		width: 100%;
		height: 80rpx;
		background-color: #BDFD5B;
		color: #333;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 20rpx;
		margin-top: 20rpx;
	}

	/* 提货码弹窗样式 */
	.pickup-code-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9999;
		display: flex;
		align-items: flex-end;
		
		.modal-content {
			width: 100%;
			background-color: #fff;
			border-radius: 30rpx 30rpx 0 0;
			padding: 0 30rpx 30rpx;
			animation: slideUp 0.3s ease-out;
		}
	}
	
	.pickup-code-content {
		text-align: center;
		padding: 60rpx 0;
		
		.pickup-code-number {
			font-size: 48rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 20rpx;
			letter-spacing: 4rpx;
		}
		
		.pickup-code-tip {
			font-size: 28rpx;
			color: #999;
		}
	}
	
	.confirm-btn {
		width: 100%;
		height: 80rpx;
		background-color: #BDFD5B;
		color: #333;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 20rpx;
		margin-top: 20rpx;
	}

	/* 订单详情页面中 glassesProductCustomInfo 组件样式定制 */
	/deep/ .glasses {
		width: 100% !important;
		margin-top: 20rpx;
	}
	
	/deep/ .glasses .lens {
		background-color: #f5f5f5;
		color: #999999 !important;
		width: 100%;
		border-radius: 12rpx !important;
		padding: 20rpx !important;
		text-align: left;
		font-size: 24rpx !important;
		line-height: 1.5;
		margin-bottom: 0 !important;
	}
	
	/deep/ .glasses .optometry {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
		font-size: 26rpx;
		color: #222222;
		margin-top: 20rpx;
	}
	
	/deep/ .glasses .optometry .iconfont {
		color: #999999;
		font-size: 24rpx !important;
	}
</style> 