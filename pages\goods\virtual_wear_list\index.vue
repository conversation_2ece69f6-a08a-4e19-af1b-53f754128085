<template>
	<view :data-theme="theme">
		<view class="virtual-wear-list">
			<view class="left">
				<view v-for="(item, index) in leftVirtualWearList" class="item">
					<image :class="item.loaded ? 'loaded' : 'loading'" @load="wearImgLoad(0, item.id)" @longpress="checkPhoto(0, item.id)" @click="chooseWear(0, item.id)" mode="aspectFit" :key="item.id" :src="item.image" :style="{height: item.scaleHeight + 'px'}"></image>
					<view class="image-mask" v-if="item.checked" @click="checkPhoto(0, item.id)">
						<view class="xuanzhong-bg"></view>
						<view class="iconfont icon-xuanzhong1 image-xuanzhong"></view>
					</view>
				</view>
			</view>
			<view class="right">
				<view v-for="(item, index) in rightVirtualWearList" class="item" >
					<image :class="item.loaded ? 'loaded' : 'loading'" @load="wearImgLoad(1, item.id)" @longpress="checkPhoto(1, item.id)" @click="chooseWear(1, item.id)" mode="aspectFit" :key="item.id" :src="item.image" :style="{height: item.scaleHeight + 'px'}"></image>
					<view class="image-mask" v-if="item.checked" @click="checkPhoto(1, item.id)">
						<view class="xuanzhong-bg"></view>
						<view class="iconfont icon-xuanzhong1 image-xuanzhong"></view>
					</view>
				</view>
			</view>
		</view>
		<view class='loadingicon acea-row row-center-wrapper' v-if="leftVirtualWearList.length > 0 || rightVirtualWearList.length > 0">
			<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
		</view>
		<view class="no-wear" v-if="(leftVirtualWearList.length == 0 && rightVirtualWearList.length == 0) && !loading">
			<emptyPage src="/pages/aastatictoT/static/images/noCollection.png" title="暂无试戴记录哦~"></emptyPage>
		</view>
		<view class="footer" :class="isDel ? 'footer-is-del' : ''"></view>
		<view class="del-btn-list" v-if="isDel">
			<view class="all-checked" @click="allCheckPhoto">
				<view class="iconfont" :class="delCheckedAll ? 'all-checked-icon' : 'all-unchecked-icon'"></view>
				<view>全选</view>
			</view>
			<view class="button del" @click="delVirtualWear">删除</view>
			<view class="button cancel" @click="uncheckPhoto">取消</view>
		</view>
	</view>
</template>

<script>
	import emptyPage from '@/components/emptyPage.vue'
	import {
		mapGetters
	} from "vuex";
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		getVirtualWearList,
		delVirtualWear,
	} from '@/api/user.js';
	let app = getApp();
	export default {
		components: {
			emptyPage,
		},
		computed: mapGetters(['isLogin', 'uid']),
		data() {
			return {
				theme: app.globalData.theme,
				windowWidth: 0,
				leftVirtualWearList: [],
				rightVirtualWearList: [],
				limit: 10,
				page: 1,
				loadend: false,
				loading: false,
				loadTitle: '加载更多',
				isDel: false,
				delCheckedCount: 0,
				delCheckedAll: false
			}
		},
		onLoad(options) {
			let winfo = uni.getWindowInfo();
			this.pixelRatio = winfo.pixelRatio;
			this.windowWidth = winfo.windowWidth;
		},
		
		onShow() {
			if (this.isLogin === false) {
				toLogin();
			} else {
				if (this.leftVirtualWearList.length == 0 && this.rightVirtualWearList.length == 0) {
					this.getVirtualWearList();
				}
			}
		},
		
		onReachBottom() {
			if (this.isLogin === false) {
				toLogin();
			} else {
				this.getVirtualWearList();
			}
		},

		methods: {
			getVirtualWearList() {
				if (this.loading || this.loadend) return;

				this.loading = true;
				getVirtualWearList({limit: this.limit, page: this.page}).then(res => {
					if (res && res.code == 200 && res.data) {
						let list = res.data.list;

						this.loadend = list.length < this.limit;
						this.loadTitle = this.loadend ? '已全部加载' : '加载更多';
						this.loading = false;
			
						this.$set(this, 'page', this.page + 1);
						
						let leftList = [];
						let rightList = [];
						for (let i in list) {
							list[i].data = JSON.parse(list[i].data);
							let imageWidth = list[i].data.imageWidth;
							let imageHeight = list[i].data.imageHeight;
							let scaleWidth = imageWidth;
							let scaleHeight = ((this.windowWidth - 10) / 2) * imageHeight / imageWidth;
							list[i].scaleWidth = scaleWidth;
							list[i].scaleHeight = scaleHeight;
							if (this.delCheckedAll) {
								list[i].checked = true;
							} else {
								list[i].checked = false;
							}
							list[i].loaded = false;
							if (i % 2) {
								rightList.push(list[i]);
							} else {
								leftList.push(list[i]);
							}
						}
						
						this.leftVirtualWearList = this.$util.SplitArray(leftList, this.leftVirtualWearList);
						this.rightVirtualWearList = this.$util.SplitArray(rightList, this.rightVirtualWearList);
						this.$set(this, 'leftVirtualWearList', this.leftVirtualWearList);
						this.$set(this, 'rightVirtualWearList', this.rightVirtualWearList);
					} else {
						this.loading = false;
						this.loadTitle = '加载更多';
					}
				}).catch(() => {
					this.loading = false;
					this.loadTitle = '加载更多';
				});
			},
			
			wearImgLoad(flag, id) {
				if (flag == 0) {
					for (let index in this.leftVirtualWearList) {
						if (this.leftVirtualWearList[index].id == id) {
							this.leftVirtualWearList[index].loaded = true;
							this.$set(this.leftVirtualWearList, index, this.leftVirtualWearList[index]);
							break;
						}
					}
				} else {
					for (let index in this.rightVirtualWearList) {
						if (this.rightVirtualWearList[index].id == id) {
							this.rightVirtualWearList[index].loaded = true;
							this.$set(this.rightVirtualWearList, index, this.rightVirtualWearList[index]);
							break;
						}
					}
				}
			},

			delVirtualWear() {
				let ids = [];
				for (let index in this.leftVirtualWearList) {
					if (this.leftVirtualWearList[index].checked)
						ids.push(this.leftVirtualWearList[index].id)
				}
				for (let index in this.rightVirtualWearList) {
					if (this.rightVirtualWearList[index].checked)
						ids.push(this.rightVirtualWearList[index].id)
				}
				
				if (ids.length > 0) {
					uni.showLoading({
						title: '正在删除记录',
						mask: true
					});
					let idsStr = ids.join(',');
					delVirtualWear({ids: idsStr}).then(res => {
						if (res && res.code == 200) {
							uni.hideLoading();

							this.delCheckedCount = 0;
							this.$set(this, 'delCheckedAll', false);
							this.$set(this, 'isDel', false);
							
							this.page = 1;
							this.loadend = false;
							this.loading = false;
							this.loadTitle = '加载更多';
							this.$set(this, 'leftVirtualWearList', []);
							this.$set(this, 'rightVirtualWearList', []);
							this.getVirtualWearList();
						} else {
							uni.hideLoading();
							this.$util.Tips({
								title: '网络加载失败'
							});
						}
					}).catch(() => {
						uni.hideLoading();
						this.$util.Tips({
							title: '网络加载失败'
						});
					});
				}
			},
			
			chooseWear(flag, id) {
				if (this.isDel) {
					this.checkPhoto(flag, id);
					return;
				}
				
				uni.navigateBack({
					delta: 1
				});
				if (flag == 0) {
					for (let index in this.leftVirtualWearList) {
						if (this.leftVirtualWearList[index].id == id) {
							uni.$emit('virtualWearFromHistory', this.leftVirtualWearList[index]);
							break;
						}
					}
				} else {
					for (let index in this.rightVirtualWearList) {
						if (this.rightVirtualWearList[index].id == id) {
							uni.$emit('virtualWearFromHistory', this.rightVirtualWearList[index]);
							break;
						}
					}
				}
			},
			
			checkPhoto(flag, id) {
				if (flag == 0) {
					for (let index in this.leftVirtualWearList) {
						if (this.leftVirtualWearList[index].id == id) {
							if (this.leftVirtualWearList[index].checked) {
								this.leftVirtualWearList[index].checked = false;
								this.$set(this.leftVirtualWearList, index, this.leftVirtualWearList[index]);
								this.delCheckedCount -= 1;
							} else {
								this.leftVirtualWearList[index].checked = true;
								this.$set(this.leftVirtualWearList, index, this.leftVirtualWearList[index]);
								this.delCheckedCount += 1;
							}	
							break;
						}
					}
				} else {
					for (let index in this.rightVirtualWearList) {
						if (this.rightVirtualWearList[index].id == id) {
							if (this.rightVirtualWearList[index].checked) {
								this.rightVirtualWearList[index].checked = false;
								this.$set(this.rightVirtualWearList, index, this.rightVirtualWearList[index]);
								this.delCheckedCount -= 1;
							} else {
								this.rightVirtualWearList[index].checked = true;
								this.$set(this.rightVirtualWearList, index, this.rightVirtualWearList[index]);
								this.delCheckedCount += 1;
							}	
							break;
						}
					}
				}
				if (this.delCheckedCount > 0) {
					this.$set(this, 'isDel', true);
				}
			},
			
			uncheckPhoto() {
				for (let index in this.leftVirtualWearList) {
					if (this.leftVirtualWearList[index].checked) {
						this.leftVirtualWearList[index].checked = false;
						this.$set(this.leftVirtualWearList, index, this.leftVirtualWearList[index]);
					}
						
				}
				for (let index in this.rightVirtualWearList) {
					if (this.rightVirtualWearList[index].checked) {
						this.rightVirtualWearList[index].checked = false;
						this.$set(this.rightVirtualWearList, index, this.rightVirtualWearList[index]);
					}
				}
				this.delCheckedCount = 0;
				this.$set(this, 'delCheckedAll', false);
				this.$set(this, 'isDel', false);
			},
			
			allCheckPhoto() {
				if (this.delCheckedAll) {
					for (let index in this.leftVirtualWearList) {
						this.leftVirtualWearList[index].checked = false;
						this.$set(this.leftVirtualWearList, index, this.leftVirtualWearList[index]);
					}
					for (let index in this.rightVirtualWearList) {
						this.rightVirtualWearList[index].checked = false;
						this.$set(this.rightVirtualWearList, index, this.rightVirtualWearList[index]);
					}
					this.$set(this, 'delCheckedAll', false);
					this.delCheckedCount = 0;
				} else {
					for (let index in this.leftVirtualWearList) {
						this.leftVirtualWearList[index].checked = true;
						this.$set(this.leftVirtualWearList, index, this.leftVirtualWearList[index]);
					}
					for (let index in this.rightVirtualWearList) {
						this.rightVirtualWearList[index].checked = true;
						this.$set(this.rightVirtualWearList, index, this.rightVirtualWearList[index]);
					}
					this.$set(this, 'delCheckedAll', true);
					this.delCheckedCount = this.leftVirtualWearList.length + this.rightVirtualWearList.length;
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f5f5f5 !important;
	}
	
	.virtual-wear-list {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: space-around;
		margin-top: 20rpx;
		
		.left, .right {
			width: 45%;
			height: 100%;
			display: inline-block;
		}
		.left .item {
			width: calc(100% - 10px);
			margin: 0 3px 0 6px;
			position: relative;
			border-radius: 20rpx;
			height: 700rpx;
		}
		.right .item {
			width: calc(100% - 10px);
			margin: 0 6px 0 3px;
			position: relative;
			border-radius: 20rpx;
			height: 700rpx;
		}
		
		image {
			width: 100%;
			height: 100%!important;
			display: block;
			border-radius: 20rpx;
		}
		
		.loading {
			background: url('@/static/easy-loadimage/loading.gif') no-repeat center;
			background-size: 100rpx;
		}
		.loaded {
			background: none;
		}
		
		.image-mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: 100;
			background-color: rgba(0, 0, 0, 0.3);
		}
		.image-xuanzhong {
			position: absolute;
			bottom: 20rpx;
			right: 20rpx;
			z-index: 200;
			font-size: 40rpx;
			@include main_color(theme);
		}
		.xuanzhong-bg {
			position: absolute;
			bottom: 30rpx;
			right: 30rpx;
			z-index: 150;
			background-color: #fff;
			width: 25rpx;
			height: 25rpx;
			border-radius: 15rpx;
		}
	}
	.footer {
		height: 10rpx;
		height: calc(10rpx + constant(safe-area-inset-bottom) / 2);
		height: calc(10rpx + env(safe-area-inset-bottom) / 2);
	}
	.footer-is-del {
		height: 105rpx;
		height: calc(105rpx + constant(safe-area-inset-bottom) / 2);
		height: calc(105rpx + env(safe-area-inset-bottom) / 2);
	}
	.del-btn-list {
		background-color: #fff;
		display: flex;
		justify-content: flex-end;
		position: fixed;
		bottom: 0;
		z-index: 100;
		width: 100%;
		padding-bottom: 10rpx;
		padding-bottom: calc(10rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(10rpx + env(safe-area-inset-bottom) / 2);
		border-top: 1rpx solid #eee;
		
		.all-checked {
			position: absolute;
			left: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 60rpx;
			margin: 15rpx 0 15rpx 15rpx;
			font-size: 28rpx;
			
			.all-checked-icon:before {
				content: "\e6ef";
				font-size: 40rpx;
				padding-right: 5rpx;
				@include main_color(theme);
			}

			.all-unchecked-icon:before {
				content: "\e6a6";
				font-size: 40rpx;
				padding-right: 5rpx;
				color: #999;
			}
		}
		
		.button {
			width: 150rpx;
			height: 60rpx;
			line-height: 58rpx;
			text-align: center;
			font-size: 28rpx;
			color: #999;
			display: inline-block;
			border-radius: 30rpx;
			border: 1rpx solid #999;
			margin: 15rpx 15rpx 15rpx 0;
		}

		.del {
			@include main_color(theme);
		}
	}
	.no-wear {
		width: 100%;
		height: 100%;
		position: fixed;
		display: flex;
		align-items: center;
		justify-content: center;
		top: -100rpx;
		
		/deep/.empty-box {
			margin: 0 !important;
			padding: 0 !important;
		}
	}
</style>
