<template>
	<view class="invite-friends-container">
		<!-- 固定返回按钮 -->
		<view class="fixed-back-button" :style="{ top: buttonTop + 'px' }" @click="goBack">
			<text class="iconfont icon-fanhui"></text>
		</view>

		<view class="header-section">
			<image class="header-bg" src="https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/655635129c3143278020c47892dd3dd1rb6xuf05mo.png" mode="widthFix"></image>
		</view>

		<view class="content-section">
			<view class="qr-code-wrapper">
				<view class="card-content">
					<view class="qr-code-box">
						<canvas canvas-id="qrcode" style="width: 280rpx; height: 280rpx;" />
					</view>
					<view class="share-text">分享扫描二维码立享好物</view>
				</view>
			</view>

			<view class="friends-list-wrapper">
				<view class="card-content">
					<view class="tabs">
						<view class="tab-item" :class="{ 'active': activeTab === 0 }" @click="switchTab(0)">邀请好友</view>
						<view class="tab-item" :class="{ 'active': activeTab === 1 }" @click="switchTab(1)">下单好友</view>
					</view>

					<view class="list-container">
						<view v-if="activeTab === 0" class="list-header">
							<text class="left">邀请用户: {{ totalCount }}人</text>
							<text class="right">邀请时间</text>
						</view>
						<view v-if="activeTab === 1" class="list-header ordered-header">
							<text class="user-col">下单用户</text>
                            <text class="amount-col">下单金额</text>
							<text class="time-col">下单时间</text>
						</view>

						<scroll-view scroll-y="true" class="list-scroll">
							<template v-if="activeTab === 0">
								<view class="list-item" v-for="(item, index) in invitedFriends" :key="index">
									<view class="user-info">
										<text>{{ item.nickname || '未填写' }} {{ item.account }}</text>
									</view>
									<text class="invite-time">{{ item.createTime }}</text>
								</view>
								<view v-if="invitedFriends.length === 0" class="empty-list">
									暂无数据
								</view>
							</template>
							<template v-if="activeTab === 1">
								<view class="list-item ordered-item" v-for="(item, index) in orderedFriends" :key="index">
									<text class="user-col">{{ item.nickname || '未填写' }} {{ item.account }}</text>
                                    <text class="amount-col">¥{{ item.payPrice }}</text>
									<text class="time-col">{{ item.payTime }}</text>
								</view>
								<view v-if="orderedFriends.length === 0" class="empty-list">
									暂无数据
								</view>
							</template>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>

		<view class="bottom-bar">
			<button class="invite-btn" hover-class="none">立即邀请</button>
		</view>
	</view>
</template>

<script>
	import uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js';
	import { getInviteFriendsList, getPlaceAnOrderList } from '@/api/api.js';

	export default {
		data() {
			return {
				activeTab: 0,
				invitedFriends: [],
				orderedFriends: [],
				totalCount: 0,
				orderedTotalCount: 0,
				page: 1,
				limit: 20,
				loading: false,
				orderedFriendsLoaded: false, // 标识下单好友数据是否已加载
				// 状态栏高度和按钮位置
				statusBarHeight: 0,
				buttonTop: 0
			};
		},
		onReady() {
			// this.generateQRCode();
			// 获取状态栏高度
			this.initStatusBar();
			// 默认加载邀请好友列表
			this.loadInviteFriendsList();
		},
		methods: {
			// 获取状态栏高度和胶囊位置
			initStatusBar() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 44;

				// #ifdef MP-WEIXIN
				// 获取胶囊按钮信息，用于精确对齐
				const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
				// 使用胶囊的top值来定位返回按钮
				this.buttonTop = menuButtonInfo.top;
				// #endif

				// #ifndef MP-WEIXIN
				// 非微信小程序环境，使用状态栏高度计算
				this.buttonTop = this.statusBarHeight + 10;
				// #endif
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			switchTab(tabIndex) {
				this.activeTab = tabIndex;

				// 根据选择的标签页加载对应数据
				if (tabIndex === 0) {
					// 邀请好友标签页
					if (this.invitedFriends.length === 0) {
						this.loadInviteFriendsList();
					}
				} else if (tabIndex === 1) {
					// 下单好友标签页
					console.log('切换到下单好友标签页，开始加载数据...');
					if (!this.orderedFriendsLoaded) {
						this.loadPlaceAnOrderList();
					}
				}
			},
			generateQRCode() {
				const qr = new uQRCode();
				qr.data = 'https://ooseek.com/invite?code=123456';
				qr.size = uni.upx2px(280);
				qr.make();
				const canvasContext = uni.createCanvasContext('qrcode', this);
				qr.canvasContext = canvasContext;
				qr.drawCanvas();
			},
			async loadInviteFriendsList() {
				if (this.loading) return;
				this.loading = true;

				try {
					const params = {
						page: this.page,
						limit: this.limit
					};

					const response = await getInviteFriendsList(params);
					console.log('邀请好友列表接口响应:', response);

					// 打印完整的响应内容
					console.log('完整响应数据:', JSON.stringify(response, null, 2));

					// 根据实际接口响应渲染数据
					if (response && response.code === 200 && response.data) {
						this.totalCount = response.data.total || 0;
						this.invitedFriends = response.data.list || [];

						console.log('渲染的邀请好友数据:', {
							总数: this.totalCount,
							列表: this.invitedFriends
						});
					}

				} catch (error) {
					console.error('获取邀请好友列表失败:', error);
					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			async loadPlaceAnOrderList() {
				if (this.loading) return;
				this.loading = true;

				try {
					const params = {
						page: this.page,
						limit: this.limit
					};

					const response = await getPlaceAnOrderList(params);
					console.log('好友下单列表接口响应:', response);

					// 打印完整的响应内容
					console.log('好友下单完整响应数据:', JSON.stringify(response, null, 2));

					// 根据实际接口响应渲染数据
					if (response && response.code === 200 && response.data) {
						this.orderedTotalCount = response.data.total || 0;
						this.orderedFriends = response.data.list || [];

						console.log('渲染的下单好友数据:', {
							总数: this.orderedTotalCount,
							列表: this.orderedFriends
						});
					}

					// 标记数据已加载
					this.orderedFriendsLoaded = true;

				} catch (error) {
					console.error('获取好友下单列表失败:', error);
					uni.showToast({
						title: '获取下单数据失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	page {
		background-color: #f5fef3;
	}

	.invite-friends-container {
		padding-bottom: 160rpx;

		// 固定返回按钮样式
		.fixed-back-button {
			position: fixed;
			left: 30rpx;
			z-index: 999;
			width: 70rpx;
			height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 40rpx;
			backdrop-filter: blur(10rpx);
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

			.iconfont {
				font-size: 36rpx;
				color: #333;
			}

			// 点击效果
			&:active {
				transform: scale(0.95);
				background: rgba(255, 255, 255, 0.8);
			}
		}

		.header-section {
			width: 100%;
			height: 400rpx;

			.header-bg {
				width: 100%;
				height: 100%;
			}
		}

		.content-section {
			padding: 0 10rpx;
			margin-top: 95rpx;
			position: relative;
			z-index: 10;
		}

		.qr-code-wrapper,
		.friends-list-wrapper {
			position: relative;
			width: 100%;
			margin-bottom: 30rpx;
		}

		.qr-code-wrapper {

			background-image: url('https://pa.letengshengtai.com/ooseekimage/public/product/2025/07/23/f907e7512da64784b9bc35a7da09a1d3uxye24ninq.png');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			
			.card-content {
				position: relative;
				z-index: 2;
				padding: 30rpx 25rpx;
			}
		}

		.friends-list-wrapper {
			background-color: #fff;
			border: 18rpx solid #ffe993;
			border-radius: 20rpx;
			
			.card-content {
				position: relative;
				z-index: 2;
				padding: 30rpx 25rpx;
			}
		}

		.qr-code-wrapper {
			.card-content {
				padding-top: 150rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				.qr-code-box {
					width: 320rpx;
					height: 320rpx;
					background-color: #fff;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 8rpx;
				}

				.share-text {
					font-size: 28rpx;
					color: #666;
					margin-top: 30rpx;
					padding-bottom: 40rpx;
				}
			}
		}

		.friends-list-wrapper {
			.card-content {
				padding-top: 50rpx;
			}

			.tabs {
				display: flex;
				justify-content: center;
				gap: 120rpx;
				margin-bottom: 30rpx;

				.tab-item {
					font-size: 30rpx;
					color: #333;
					padding-bottom: 10rpx;
					position: relative;

					&.active {
						font-weight: bold;
						color: #333;

						&::after {
							content: '';
							position: absolute;
							bottom: 0;
							left: 50%;
							transform: translateX(-50%);
							width: 100%;
							height: 6rpx;
							background-color: #f7d658;
							border-radius: 3rpx;
						}
					}
				}
			}

			.list-container {
				.list-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					background-color: #fff;
					padding: 15rpx 25rpx;
					border-radius: 16rpx;
					color: #999;
					font-size: 24rpx;
					margin-bottom: 10rpx;

					&.ordered-header {
						.user-col {
							width: 35%;
						}
						.time-col {
							width: 35%;
							text-align: center;
						}
						.amount-col {
							width: 16%;
							text-align: right;
						}
					}
				}

				.list-scroll {
					height: 400rpx;

					.list-item {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 25rpx 20rpx;
						color: #333;
						font-size: 26rpx;

						.invite-time {
							font-size: 24rpx;
							color: #999;
						}
						
						&.ordered-item {
							.user-col {
								width: 35%;
								font-size: 26rpx;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}
							.time-col {
								width: 37%;
								font-size: 24rpx;
								color: #999;
								text-align: center;
							}
							.amount-col {
								width: 16%;
								font-size: 26rpx;
								text-align: right;
							}
						}
					}

					.empty-list {
						text-align: center;
						color: #999;
						font-size: 28rpx;
						padding-top: 100rpx;
					}
				}
			}
		}
	}

	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		padding: 20rpx 30rpx;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 100;
		display: flex;
		justify-content: center;

		.invite-btn {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			background-color: #282828;
			color: #BDFD5B;
			font-size: 30rpx;
			border-radius: 20rpx;
			margin: 0;
		}
	}
</style> 