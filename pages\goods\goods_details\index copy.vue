<template>
	<view :data-theme="theme">
		<tui-skeleton v-if="showSkeleton"></tui-skeleton>
		<view class="product-con tui-skeleton" :style="{visibility: showSkeleton ? 'hidden' : 'visible'}">
			<view class='navbar' :class="opacity>0.6?'bgwhite':''">
				<view class='navbarH' :style='"height:"+navH+"rpx;"'>
					<view class='navbarCon acea-row' :style="{ paddingRight: navbarRight + 'px' }">
						<!-- #ifdef MP -->
						<view class="select_nav flex justify-center align-center" id="home"
							:style="{ top: homeTop + 'rpx' }">
							<text v-if="returnShow" class="iconfont icon-fanhui2 px-20" @tap="returns"></text>
							<text v-if="returnShow" class="nav_line"></text>
							<text class="iconfont icon-gengduo5 px-20" @tap="showNav"></text>
						</view>
						<!-- #endif -->
						<!-- #ifdef H5 -->
						<view id="home" class="home acea-row row-center-wrapper iconfont icon-xiangzuo h5_back"
							:class="opacity>0.5?'on':''" :style="{ top: homeTop + 'rpx' }" 
							@tap="returns">
						</view>
						<!-- #endif -->
						<view class="tab_nav" v-show="opacity > 0.6" :style="{ top: homeTop + 'rpx' }">
							<view class="header flex justify-between align-center">
								<view class="item" :class="navActive === index ? 'on' : ''" v-for="(item,index) in navList"
									:key='index' @tap="tap(index)">
									{{ item }}
								</view>
							</view>
						</view>
						<!-- #ifdef H5 || APP-PLUS -->
						<view class="right_select" :style="{ top: homeTop + 'rpx' }" @tap="showNav">
							<text class="iconfont icon-gengduo2"></text>
						</view>
						<!-- #endif -->
					</view>
				</view>
			</view>
			<!-- 弹窗导航列表 -->
			<view class="dialog_nav" v-show="currentPage" :style="{ top: navH + 'rpx' }">
				<view class="dialog_nav_item" :class="item.after" v-for="(item,index) in selectNavList" :key="index"
					@click="linkPage(item.url)">
					<text class="iconfont" :class="item.icon"></text>
					<text class="pl-20">{{item.name}}</text>
				</view>
			</view>
			<!-- 商品信息 -->
			<view class="detail_container">
				<scroll-view :scroll-top="scrollTop" scroll-y='true' scroll-with-animation="true"
					:style='"height:"+height+"px;overflow-x:hidden;"' @scroll="scroll">
					<view id="past0">
						<view v-if="isGlassesProduct" class="glasses-try-on" :style="{ top: (690) + 'rpx' }" @click="virtualWear">
							<view class="iconfont icon-yonghu"></view>
							<view class="txt">虚拟试戴</view>
						</view>
						<productConSwiper class="tui-skeleton-rect" :imgUrls="sliderImage" :videoline="videoLink"
							@videoPause="videoPause"></productConSwiper>
						<view class="borderPad">
							<view class='wrapper mb20 borRadius14'>
								<view class='share acea-row row-between row-bottom'>
									<view class='x-money flex align-baseline'>￥
										<text class='num font-44 tui-skeleton-rect'>{{attr.productSelect.price}}</text>
										<view class="flex pl-2 tui-skeleton-rect"
											v-if="attr.productSelect.vipPrice && attr.productSelect.vipPrice > 0">
											<image src="@/static/images/vip_badge.png" class="vip_icon tui-skeleton-rect"></image>
											<text
												class='vip_money tui-skeleton-rect'>￥{{attr.productSelect.vipPrice}}</text>
										</view>
									</view>
									<view class="share-btn" @click="listenerActionSheet">
										<view class="share-txt">分享赚佣金</view>
										<view class='iconfont icon-fenxiang share-icon'></view>
									</view>
								</view>
								<view class='introduce tui-skeleton-rect'>{{productInfo.name}}</view>
								<view class='label acea-row row-between-wrapper'>
									<view class="tui-skeleton-rect">原价:￥{{attr.productSelect.otPrice || 0}}</view>
									<view class="tui-skeleton-rect">
										库存:{{attr.productSelect.stock || 0}}{{productInfo.unitName || ''}}</view>
									<view class="tui-skeleton-rect">
										销量:{{Math.floor(productInfo.sales) + Math.floor(productInfo.ficti) || 0}}{{productInfo.unitName || ''}}
									</view>
								</view>
								<view v-if="defaultCoupon.length>0 && type=='normal'"
									class='coupon acea-row row-between-wrapper' @click='couponTap'>
									<view class='hide line1 acea-row'>优惠券：
										<view class='activity'>满{{defaultCoupon[0].minPrice}}减{{defaultCoupon[0].money}}
										</view>
									</view>
									<view class='iconfont icon-jiantou'></view>
								</view>
								<view class="coupon acea-row row-between-wrapper" v-if="activityH5.length">
									<view class="line1 acea-row">
										<text class="activityName tui-skeleton-rect">活&nbsp;&nbsp;&nbsp;动：</text>
										<view v-for='(item,index) in activityH5' :key='index' @click="goActivity(item)"
											class="activityBox">
											<view v-if="item.type === '1'" class="tui-skeleton-rect"
												:class="index==0?'activity_pin':'' || index==1?'activity_miao':'' || index==2?'activity_kan':''">
												<text class="iconfonts iconfont icon-miaosha1"></text>
												<text class="activity_title"> 参与秒杀</text>
											</view>
											<view class="tui-skeleton-rect"
												:class="index==0?'activity_pin':'' || index==1?'activity_miao':'' || index==2?'activity_kan':''"
												v-if="item.type === '2'">
												<text class="iconfonts iconfont icon-kanjia"></text>
												<text class="activity_title"> 参与砍价</text>
											</view>
											<view class="skeleton-rect"
												:class="index==0?'activity_pin':'' || index==1?'activity_miao':'' || index==2?'activity_kan':''"
												v-if="item.type === '3'">
												<text class="iconfonts iconfont icon-pintuan"></text>
												<text class="activity_title"> 参与拼团</text>
											</view>
										</view>
									</view>
								</view>
							</view>
							<!-- 规格、保障服务 -->
							<view class='attribute mb20 borRadius14 tui-skeleton-rect'>
								<view class="acea-row row-between-wrapper" @click="selecAttr">
									<view class="line1 text-666">{{attrTxt}}：
										<text class='atterTxt text-333'>{{attrValue}}</text>
									</view>
									<view class='iconfont icon-jiantou'></view>
								</view>
								<view class="acea-row row-between-wrapper"
									style="margin-top:7px;padding-left:55px;"
									v-if="skuArr.length > 1">
									<view class="flex">
										<image :src="item.image" v-for="(item,index) in skuArr.slice(0,4)" :key="index"
											class="attrImg"></image>
									</view>
									<view class="switchTxt">共{{skuArr.length}}种规格可选</view>
								</view>
								<view v-if="guaranteeList.length>0" class="acea-row row-between-wrapper"
									@click="assureDrawer = true" style="margin-top: 45rpx;">
									<view class="line1 tui-skeleton-fillet"> <text
											class="text-666 fw-bold">保&nbsp;&nbsp;&nbsp;障：</text>
										<text class='text-333 tui-skeleton-fillet' v-for="(item,index) in guaranteeList"
											:key="index">{{item.name }} · </text>
									</view>
									<view class='iconfont icon-jiantou'></view>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 商品评价 -->
					<view class="borderPad" id="past1">
						<view class='userEvaluation tui-skeleton-rect'>
							<view class='title acea-row row-between-wrapper'
								:style="replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':''">
								<view>用户评价<i>({{replyCount}})</i></view>
								<navigator class='praise' hover-class='none'
									:url='"/pages/goods/goods_comment_list/index?productId="+id'>
									<i>好评</i>&nbsp;<text class='font_color px-12'>{{replyChance || 0}}%</text>
									<text class='iconfont icon-jiantou'></text>
								</navigator>
							</view>
							<block v-if="replyCount">
								<userEvaluation :reply="reply"></userEvaluation>
							</block>
						</view>
					</view>

					<!-- 店铺信息 -->
					<view class="borderPad">
						<view class="superior" if='merchantInfo'>
							<merHome :merchantInfo="merchantInfo" :merid="productInfo.merId" type="home" :isShowTypeId="isShowTypeId"></merHome>
						</view>
						<view class="slider-banner banner">
							<view class="list acea-row row-middle">
								<view class="item" v-for="(val,indexw) in merchantInfo.proList" :key="indexw"
									@click="goDetail(val)">
									<view class="pictrue">
										<easy-loadimage mode="widthFix" :image-src="val.image" ></easy-loadimage>
									</view>
									<view class="name line1">{{val.name}}</view>
									<view class="money theme_price">¥{{val.price}}</view>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 产品详情 -->
					<view class='product-intro' id="past2">
						<view class='title'>
							<image src="../static/images/xzuo.png"></image>
							<span class="sp">产品详情</span>
							<image src="../static/images/xyou.png"></image>
						</view>
						<view class='conter borderPad'>
							<jyf-parser :html="description" ref="article" :tag-style="tagStyle"></jyf-parser>
						</view>
					</view>
					<view class='footer-height'></view>
				</scroll-view>
			</view>
			<!-- 底部 -->
			<view class='footer acea-row row-between-wrapper'>
				<!-- #ifdef MP -->
				<button hover-class='none' class='item tui-skeleton-rect' @click="kefuClick"
					v-if="chatConfig.telephone_service_switch === 'true'">
					<view class='iconfont icon-kefu'></view>
					<view>客服</view>
				</button>
				<button open-type="contact" hover-class='none' class='item tui-skeleton-rect' v-else>
					<view class='iconfont icon-kefu'></view>
					<view>客服</view>
				</button>
				<!-- #endif -->
				<!-- #ifndef MP -->
				<view class="item tui-skeleton-rect" @click="kefuClick">
					<view class="iconfont icon-kefu"></view>
					<view>客服</view>
				</view>
				<!-- #endif -->
				<block v-if="type === 'normal'">
					<view @click="setCollect" class='item tui-skeleton-rect'>
						<view class='iconfont icon-shoucang1' v-if="userCollect"></view>
						<view class='iconfont icon-shoucang' v-else></view>
						<view>收藏</view>
					</view>
					<navigator open-type='switchTab' class="animated item tui-skeleton-rect"
						:class="animated==true?'bounceIn':''" url='/pages/order_addcart/order_addcart'
						hover-class="none">
						<view class='iconfont icon-gouwuche1'>
							<text v-if="Math.floor(CartCount)>0" class='num bg_color'>{{CartCount}}</text>
						</view>
						<view>购物车</view>
					</navigator>
					<view class="bnt acea-row" v-if="attr.productSelect.stock <= 0">
						<form report-submit="true"><button class="joinCart bnts bg-color-hui tui-skeleton-rect"
								form-type="submit">加入购物车</button></form>
						<form report-submit="true"><button class="bnts bg-color-hui" form-type="submit">已售罄</button>
						</form>
					</view>
					<view class="bnt acea-row tui-skeleton-rect" v-else>
						<form @submit="joinCart" report-submit="true"><button class="joinCart bnts"
								form-type="submit">加入购物车</button></form>
						<form @submit="goBuy" report-submit="true"><button class="buy bnts"
								form-type="submit">立即购买</button>
						</form>
					</view>
				</block>
				<view class="bnt bntVideo acea-row"
					v-if="attr.productSelect.stock <= 0 && type === 'video'">
					<form report-submit="true"><button class="bnts bg-color-hui" form-type="submit">已售罄</button>
					</form>
				</view>
				<view class="bnt bntVideo acea-row"
					v-if="attr.productSelect.stock > 0 && type === 'video'">
					<form @submit="goBuy" report-submit="true"><button class="buy bnts" form-type="submit">立即购买</button>
					</form>
				</view>
			</view>
			<!-- 组件 -->
			<glassesProductWindow v-if="isGlassesProduct"
				:merId="productInfo.merId"
				:attr="attr"
				@closeAttr="onMyEvent" 
				@changeAttr="ChangeAttr"
				@attrVal="attrVal"
				@setCustomData="setCustomData"
				@getImg="showImg"
				@joinCart="joinCart"
				@goBuy="goBuy">
			</glassesProductWindow>
			<productWindow v-else 
				id='product-window'
				:attr="attr"
				:isShow='1'
				:iSplus='1'
				@myevent="onMyEvent"
				@ChangeAttr="ChangeAttr"
				@ChangeCartNum="ChangeCartNum"
				@attrVal="attrVal"
				@iptCartNum="iptCartNum"
				@getImg="showImg">
			</productWindow>
			
			<couponListWindow :coupon='coupon' :typeNum="couponDeaultType[0].useType"
				@ChangCouponsClone="ChangCouponsClone" @ChangCoupons="ChangCoupons"
				@ChangCouponsUseState="ChangCouponsUseState" @tabCouponType="tabCouponType"></couponListWindow>

			<!-- 分享按钮 -->
			<view class="generate-posters" :class="posters ? 'on' : ''">
				<view class="generateCon acea-row row-middle">
					<!-- #ifndef MP -->
					<button class="item" hover-class="none" v-if="weixinStatus === true" @click="H5ShareBox = true">
						<view class="pictrue">
							<image src="../static/images/weixin.png"></image>
						</view>
						<view class="">分享给好友</view>
					</button>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<button class="item" open-type="share" hover-class="none">
						<view class="pictrue">
							<image src="../static/images/weixin.png"></image>
						</view>
						<view class="">分享给好友</view>
					</button>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<view class="item" @click="appShare('WXSceneSession')">
						<view class="iconfont icon-weixin3"></view>
						<view class="">微信好友</view>
					</view>
					<view class="item" @click="appShare('WXSenceTimeline')">
						<view class="iconfont icon-pengyouquan"></view>
						<view class="">微信朋友圈</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 || MP -->
					<view class="item" @click="getpreviewImage">
						<view class="pictrue">
							<image src="../static/images/changan.png"></image>
						</view>
						<view class="">预览发图</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef MP  -->
					<button class="item" hover-class="none" @click="savePosterPath">
						<view class="pictrue">
							<image src="../static/images/haibao.png"></image>
						</view>
						<view class="">保存海报</view>
					</button>
					<!-- #endif -->
				</view>
				<view class="generateClose acea-row row-center-wrapper" @click="posterImageClose">取消</view>
			</view>
			<!-- 查看规格商品图 -->
			<cus-previewImg ref="cusPreviewImg" :list="skuArr" @changeSwitch="changeSwitch" @shareFriend="listenerActionSheet"/>
		    
			<!-- 海报展示 -->
			<view class='poster-pop' v-if="canvasStatus">
				<image :src='imagePath'></image>
			</view>
			<view class="canvas" v-else>
				<canvas style="width:750px;height:1190px;" canvas-id="firstCanvas"></canvas>
				<canvas canvas-id="qrcode" :style="{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}" />
			</view>
			<!-- 发送给朋友图片 -->
			<view class="share-box" v-if="H5ShareBox">
				<image src="/static/images/share-info.png" @click="H5ShareBox = false"></image>
			</view>
			<!-- 保障服务弹窗 -->
			<tui-drawer mode="bottom" :visible="assureDrawer" @close="closeAssure">
				<view class="ensure">
					<view @click="closeAssure" class="title">保障服务<text class="iconfont icon-guanbi5"></text></view>
					<view class="list">
						<view class="item acea-row" v-for="(item,index) in guaranteeList" :key="index">
							<view class="pictrue">
								<text class="iconfont icon-gou2"></text>
							</view>
							<view class="text">
								<view class="name">{{item.name}}</view>
								<view>{{item.content}}</view>
							</view>
						</view>
					</view>
					<view class="bnt" @click="assureDrawer = false">完成</view>
				</view>
			</tui-drawer>

			<!-- 分享弹窗mask -->
			<view class="mask" v-if="posters" @click="closePosters"></view>
			<!-- 海报弹窗mask -->
			<view class="mask" v-if="canvasStatus"></view>
			<!-- 导航列表弹窗mask -->
			<view class="mask_transparent" v-if="currentPage" @touchmove="hideNav" @click="hideNav()"></view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'
	import store from '@/store';
	import {
		HTTP_H5_URL
	} from '@/config/app.js';
	import {
		spread
	} from "@/api/user";
	import {
		getProductDetail,
		collectAdd,
		collectCancel,
		postCartAdd,
		getReplyList,
		getReplyConfig,
		getProductGood,
		getReplyProduct,
		getCategoryList
	} from '@/api/product.js';
	import {
		getCoupons
	} from '@/api/api.js';
	import {
		merCustomerApi
	} from '@/api/merchant.js';
	import {
		getCartCounts
	} from '@/api/order.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		imageBase64
	} from "@/api/public";
	import productConSwiper from '../components/productConSwiper/index.vue';
	import couponListWindow from '@/components/couponListWindow';
	import productWindow from '@/components/productWindow';
	import glassesProductWindow from '@/components/glassesProductWindow';
	import userEvaluation from '@/components/userEvaluation';
	import cusPreviewImg from '@/components/cus-previewImg/cus-previewImg.vue'
	import merHome from '@/components/merHome/index.vue'
	import {
		silenceBindingSpread
	} from "@/utils";
	import parser from "@/components/jyf-parser/jyf-parser";
	import {
		computeUser
	} from "@/api/user.js";
	import tuiDrawer from '@/components/base/tui-drawer.vue';
	import tuiSkeleton from '@/components/base/tui-skeleton.vue';
	import easyLoadimage from '@/components/base/easy-loadimage.vue';
	// #ifdef MP
	import {
		base64src
	} from '@/utils/base64src.js'
	import {
		mpQrcode
	} from '@/api/api.js';
	// #endif
	let app = getApp();
	import {
		setThemeColor
	} from '@/utils/setTheme.js'
	import {
		Debounce
	} from '@/utils/validate.js'
	import {
		chatConfig
	} from '@/utils/consumerType.js'
	import { autoFollow } from '@/libs/merchant.js';
	export default {
		components: {
			productConSwiper,
			couponListWindow,
			productWindow,
			glassesProductWindow,
			userEvaluation,
			cusPreviewImg,
			merHome,
			"jyf-parser": parser,
			tuiDrawer,
			tuiSkeleton,
			easyLoadimage
		},
		data() {
			let that = this;
			return {
				showSkeleton: true, //骨架屏显示隐藏
				isNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
				//属性是否打开
				coupon: {
					coupon: false,
					type: 2,
					list: [],
					count: []
				},
				attrTxt: '请选择', //属性页面提示
				attrValue: '', //已选属性
				animated: false, //购物车动画
				id: 0, //商品id
				replyCount: 0, //总评论数量
				reply: [], //评论列表
				productInfo: {}, //商品详情
				productValue: [], //系统属性
				couponList: [], //优惠券
				cart_num: 1, //购买数量
				isOpen: false, //是否打开属性组件
				actionSheetHidden: true,
				storeImage: '', //海报产品图
				PromotionCode: '', //二维码图片
				posterbackgd: '/static/images/posterbackgd.png',
				sharePacket: {
					isState: true, //默认不显示
					touchstart: false
				}, //分销商详细
				circular: false,
				autoplay: false,
				interval: 3000,
				duration: 500,
				clientHeight: "",
				systemStore: {}, //门店信息
				good_list: [],
				replyChance: 0,
				CartCount: 0,
				isDown: true,
				posters: false,
				weixinStatus: false,
				attr: {
					cartAttr: false,
					productAttr: [],
					productSelect: {}
				},
				description: '',
				navActive: 0,
				H5ShareBox: false, //公众号分享图片
				activityH5: [],
				retunTop: true, //顶部返回
				navH: "",
				navList: [],
				opacity: 0,
				scrollY: 0,
				topArr: [],
				toView: '',
				height: 0,
				heightArr: [],
				lock: false,
				scrollTop: 0,
				tagStyle: {
					img: 'width:calc(100% - 48rpx);display:block;',
					table: 'width:calc(100% - 48rpx)',
					video: 'width:calc(100% - 48rpx)'
				},
				sliderImage: [],
				videoLink: '',
				qrcodeSize: 600,
				canvasStatus: false, //是否显示海报
				imagePath: '', //海报路径
				imgTop: '',
				errT: '',
				homeTop: 20,
				navbarRight: 0,
				userCollect: false,
				options: null,
				returnShow: true, //判断顶部返回是否出现
				type: "", //视频号普通商品类型
				theme: app.globalData.theme,
				indicatorBg: '',
				shareStatus: true,
				skuArr: [],
				currentPage: false,
				selectSku: {},
				selectNavList: [{
						name: '首页',
						icon: 'icon-shouye8',
						url: '/pages/index/index',
						after: 'dialog_after'
					},
					{
						name: '搜索',
						icon: 'icon-sousuo6',
						url: '/pages/goods/goods_search/index',
						after: 'dialog_after'
					},
					{
						name: '购物车',
						icon: 'icon-gouwuche7',
						url: '/pages/order_addcart/order_addcart',
						after: 'dialog_after'
					},
					{
						name: '我的收藏',
						icon: 'icon-shoucang3',
						url: '/pages/users/user_goods_collection/index',
						after: 'dialog_after'
					},
					{
						name: '个人中心',
						icon: 'icon-gerenzhongxin1',
						url: '/pages/user/index'
					},
				],
				defaultCoupon: [],
				couponDeaultType: [{
					useType: 1
				}],
				guaranteeList: [],
				assureDrawer: false,
				merchantInfo: {},
				isShowTypeId: false,
				serviceConfig: {},
				glassesSubCategoryIdList: [],
				isGlassesProduct: false
			};
		},
		computed: mapGetters(['isLogin', 'uid', 'chatUrl', 'globalData', 'productCategoryTree']),
		watch: {
			globalData: {
				handler: function(newV, oldV) {
					this.$set(this, 'navH', this.globalData.navHeight);
				},
				deep: true
			},
			isLogin: {
				handler: function(newV, oldV) {
					let that = this;
					if (newV == true) {
						that.getCartCount();
						that.getCouponList();
						// #ifdef MP
						that.getQrcode();
						// #endif
						setTimeout(() => {
							that.defaultCoupon = that.coupon.list;
						}, 1000);
					}
				},
				deep: true
			},
			productInfo: {
				handler: function() {
					if (this.glassesSubCategoryIdList.length > 0){
						this.glassesSubCategoryIdList.forEach((item) => {
							if (this.productInfo.categoryId == item) {
								this.isGlassesProduct = true;
							}
						})
					}
					this.$nextTick(() => {});
				},
				immediate: true
			},
			productCategoryTree: {
				handler: function(categoryList) {
					if (categoryList) {
						let glassesSubCategoryIds = [];
						for (var i = 0; i < categoryList.length; i++) {
							if (categoryList[i].name.trim() == "眼镜") {
								let childList = categoryList[i].childList;
								for (var j = 0; j < childList.length; j++) {
									if (childList[j].name.trim() == "光学眼镜架") {
										let subChildList = childList[j].childList;
										for (var k = 0; k < subChildList.length; k++) {
											glassesSubCategoryIds.push(subChildList[k].id)
										}
									}
								}
							}
						}
						this.glassesSubCategoryIdList = glassesSubCategoryIds;
						if (this.productInfo && this.productInfo.categoryId){
							this.glassesSubCategoryIdList.forEach((item) => {
								if (this.productInfo.categoryId == item) {
									this.isGlassesProduct = true;
								}
							})
						}
					}
				},
				immediate: true
			}
		},
		mounted() {
			if (!this.productCategoryTree) {
				getCategoryList().then(res => {
					this.$store.commit('PRODUCTCATEGORYTREE', res.data)
				})
			}
		},
		onLoad(options) {
			this.options = options;
			let that = this;
			var pages = getCurrentPages();
			that.returnShow = pages.length === 1 ? false : true;
			if (pages.length <= 1) {
				that.retunTop = false
			}
			that.navH = this.globalData.navHeight;
			
			// #ifdef H5
			//computeUser();
			// #endif
			that.$set(that, 'theme', that.$Cache.get('theme')); //用户从分享卡片进入的场景下获取主题色配置
			uni.getSystemInfo({
				success: function(res) {
					that.height = res.windowHeight
					//res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；
					// #ifndef APP-PLUS || H5 || MP-ALIPAY
					that.navbarRight = res.windowWidth - uni.getMenuButtonBoundingClientRect().left;
					// #endif
				},
			});
			if (!options.scene && !options.id) {
				this.showSkeleton = false;
				this.$util.Tips({
					title: '缺少参数无法查看商品'
				}, {
					url: '/pages/index/index'
				});
				return;
			}
			
			if (options.spread) {
				app.globalData.spread = options.spread;
				silenceBindingSpread({spread: app.globalData.spread}, this.isLogin);
			}
			if (options.mid) {
				app.globalData.mid = options.mid;
				autoFollow(app.globalData.mid).then(() => {
					this.$store.commit('CURRENT_MERID', app.globalData.mid);
				}).catch(e => {});
			}

			if (options.hasOwnProperty('id') || options.scene) {
				if (options.scene) { 
					// 小程序扫码进入
					let value = this.$util.getUrlParams(decodeURIComponent(options.scene));
					let scene = this.$util.formatMpQrCodeData(value);
					this.id = scene.id;
					if (scene.spread) {
						app.globalData.spread = scene.spread;
						silenceBindingSpread({spread: app.globalData.spread}, this.isLogin);
					}
					if (scene.mid) {
						app.globalData.mid = scene.mid;
						autoFollow(app.globalData.mid).then(() => {
							this.$store.commit('CURRENT_MERID', app.globalData.mid);
						}).catch(e => {});
					}
				} else {
					this.id = options.id;
				}
				options.type == undefined || options.type == null ? that.type = 'normal' : that.type = options.type;
				that.$store.commit("PRODUCT_TYPE", that.type);
			}
			this.getGoodsDetails();
			if (this.isLogin) {
				this.getCouponList();
			}
			this.getProductReplyList();
			this.getProductReplyCount();

			this.indicatorBg = setThemeColor();	
			uni.$on('backgobuy', this.backgobuy);
		},
		onUnload() {
			uni.$off('backgobuy', this.backgobuy);
		},
		onReady() {
			this.isNodes++;
			this.$nextTick(function() {
				// #ifdef MP
				const menuButton = uni.getMenuButtonBoundingClientRect();
				const query = uni.createSelectorQuery().in(this);
				query
					.select('#home')
					.boundingClientRect(data => {
						this.homeTop = menuButton.top * 2 + menuButton.height - data.height;
					})
					.exec();
				// #endif
				// #ifdef APP-PLUS
				//this.homeTop = 0;
				// #endif
			});
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		},
		/**
		 * 用户点击右上角分享
		 */
		// #ifdef MP
		onShareAppMessage: function() {
			this.$set(this, 'actionSheetHidden', !this.actionSheetHidden);
			let spread = this.uid ? this.uid : 0;
			return {
				title: this.productInfo.name || '',
				imageUrl: this.productInfo.image || '',
				path: `/pages/goods/goods_details/index?id=${this.id}&spread=${spread}&mid=${this.productInfo.merId || ''}`
			}
		},
		// #endif
		methods: {
			virtualWear() {
				if (this.isLogin === false) {
					toLogin();
				} else {
					let skuList = [];
					for (let key in this.productValue) {
						if (this.productValue[key].wearImage) {
							skuList.push({
								sku: this.productValue[key].sku,
								wearImage: this.productValue[key].wearImage,
								spec: this.productValue[key].spec,
							});
						}
					}
					
					if (skuList.length > 0) {
						uni.navigateTo({
							url: '/pages/goods/glasses_virtual_wear/index?id=' +  this.id + '&skuList=' + JSON.stringify(skuList) + '&mid=' + this.productInfo.merId
						});
					} else {
						this.$util.Tips({
							title: '未找到试戴素材'
						});
					}
				}
			},
			
			backgobuy(sku) {
				let values = sku.split(',');
				for (let i = 0; i < values.length; i++) {
					this.$set(this.attr.productAttr[i], "index", values[i]);
				}
				this.ChangeAttr(sku);

				this.$set(this.attr, 'cartAttr', true);
				this.$set(this, 'isOpen', true);
			},

			// 获取客服配置信息
			getMerCustomer(id){
				merCustomerApi(id).then(res => {
					this.serviceConfig = res.data
				})
			},
			// 关闭保障服务弹窗
			closeAssure() {
				this.assureDrawer = false;
			},
			// 联系客服
			kefuClick() {
				chatConfig(this.serviceConfig)
			},
			goActivity: function(e) {
				let item = e;
				if (item.type === "1") {
					uni.navigateTo({
						url: `/pages/activity/goods_seckill_details/index?id=${item.id}`
					});
				} else if (item.type === "2") {
					uni.navigateTo({
						url: `/pages/activity/goods_bargain_details/index?id=${item.id}&startBargainUid=${this.uid}`
					});
				} else {
					uni.navigateTo({
						url: `/pages/activity/goods_combination_details/index?id=${item.id}`
					});
				}
			},
			/**
			 * 购物车手动填写
			 * 
			 */
			iptCartNum: function(e) {
				this.$set(this.attr.productSelect, 'cart_num', e ? e : 1);
			},
			// 后退
			returns: function() {
				uni.navigateBack()
			},
			showNav() {
				this.currentPage = !this.currentPage;
			},
			tap: function(index) {
				var id = "past" + index;
				var index = index;
				var that = this;
				this.$set(this, 'toView', id);
				this.$set(this, 'navActive', index);
				this.$set(this, 'lock', true);
				this.$set(this, 'scrollTop', index > 0 ? that.topArr[index] - (this.globalData.navHeight / 2) : that
					.topArr[index]);
			},
			scroll: function(e) {
				var that = this,
					scrollY = e.detail.scrollTop;
				var opacity = scrollY / 200;
				opacity = opacity > 1 ? 1 : opacity;
				that.$set(that, 'opacity', opacity);
				that.$set(that, 'scrollY', scrollY);
				if (that.lock) {
					that.$set(that, 'lock', false)
					return;
				}
				for (var i = 0; i < that.topArr.length; i++) {
					if (scrollY < that.topArr[i] - (this.globalData.navHeight / 2) + that.heightArr[i]) {
						that.$set(that, 'navActive', i)
						break
					}
				}
				that.$set(that.sharePacket, 'touchstart', true); //滑动屏幕时让分享气泡缩回
				uni.$emit('scroll');
			},
			/*
			 *去商品详情页 
			 */
			goDetail(item) {
				if (!item.activityH5) {
					uni.redirectTo({
						url: '/pages/goods/goods_details/index?id=' + item.id
					})
					return
				}
				if (item.activityH5.length == 0) {
					uni.redirectTo({
						url: '/pages/goods/goods_details/index?id=' + item.id
					})
					return
				}
				// 砍价
				if (item.activityH5 && item.activityH5.type == 2) {
					uni.redirectTo({
						url: `/pages/activity/goods_bargain_details/index?id=${item.activityH5.id}&bargain=${this.uid}`
					})
					return
				}
				// 拼团
				if (item.activityH5 && item.activityH5.type == 3) {
					uni.redirectTo({
						url: `/pages/activity/goods_combination_details/index?id=${item.activityH5.id}`
					})
					return
				}
				// 秒杀
				if (item.activityH5 && item.activityH5.type == 1) {
					uni.redirectTo({
						url: `/pages/activity/goods_seckill_details/index?id=${item.activityH5.id}`
					})
					return
				}
			},
			// 微信登录回调
			onLoadFun: function(e) {
				this.getCouponList();
				this.getCartCount();
			},
			ChangCouponsClone: function() {
				this.$set(this.coupon, 'coupon', false)
			},
			/**
			 * 购物车数量加和数量减
			 * 
			 */
			ChangeCartNum: function(changeValue) {
				//changeValue:是否 加|减
				//获取当前变动属性
				let productSelect = this.productValue[this.attrValue];
				//如果没有属性,赋值给商品默认库存
				if (productSelect === undefined && !this.attr.productAttr.length)
					productSelect = this.attr.productSelect;
				//无属性值即库存为0；不存在加减；
				if (productSelect === undefined) return;
				let stock = productSelect.stock || 0;
				let num = this.attr.productSelect;
				if (changeValue) {
					num.cart_num++;
					if (num.cart_num > stock) {
						this.$set(this.attr.productSelect, "cart_num", stock);
						this.$set(this, "cart_num", stock);
					}
				} else {
					num.cart_num--;
					if (num.cart_num < 1) {
						this.$set(this.attr.productSelect, "cart_num", 1);
						this.$set(this, "cart_num", 1);
					}
				}
			},
			attrVal(val) {
				this.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val
					.indexn]);
			},
			/**
			 * 属性变动赋值
			 * 
			 */
			ChangeAttr: function(res) {
				let productSelect = this.productValue[res];
				this.$set(this, "selectSku", productSelect);
				if (productSelect) {
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", productSelect.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', productSelect.otPrice);
					this.$set(this, "attrValue", res);
					this.$set(this, "attrTxt", "已选择");
				} else {
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", 0);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", this.productInfo.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', this.productInfo.otPrice);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				}
			},
			
			setCustomData: function(res) {
				this.$set(this.attr, "customData", res);
			},

			/**
			 * 领取完毕移除当前页面领取过的优惠券展示
			 */
			ChangCoupons: function(e) {
				let coupon = e;
				let couponList = this.$util.ArrayRemove(this.couponList, 'id', coupon.id);
				this.$set(this, 'couponList', couponList);
				this.getCouponList();
			},

			setClientHeight: function() {
				let that = this;
				if (!that.good_list.length) return;
				let view = uni.createSelectorQuery().in(this).select("#list0");
				view.fields({
					size: true,
				}, data => {
					that.$set(that, 'clientHeight', data.height + 20)
				}).exec();
			},
			/**
			 * 优品推荐
			 * 
			 */
			getGoods() {
				getProductGood().then(res => {
					let good_list = res.data.list || [];
					let count = Math.ceil(good_list.length / 6);
					let goodArray = new Array();
					for (let i = 0; i < count; i++) {
						let list = good_list.slice(i * 6, i * 6 + 6);
						if (list.length) goodArray.push({
							list: list
						});
					}
					this.$set(this, 'good_list', goodArray);
					let navList = ['商品', '评价',  '详情'];
					// if (goodArray.length) {
					// 	navList.splice(2, 0, '店铺')
					// }
					this.$set(this, 'navList', navList);
					this.$nextTick(() => {
						if (good_list.length) {
							// #ifndef APP-PLUS
							this.setClientHeight();
							// #endif
							// #ifdef APP-PLUS
							setTimeout(() => {
								this.setClientHeight();
							}, 1000)
							// #endif
						};
					})
				});
			},
			/**
			 * 获取产品详情
			 * 
			 */
			getGoodsDetails: function() {
				let that = this;
				getProductDetail(that.id, that.type).then(res => {
					let data = res.data;
					let productInfo = data.productInfo;
					// 字符串数组转数组；
					let arrayImg = productInfo.sliderImage;
					let sliderImage = JSON.parse(arrayImg);
					if (that.getFileType(sliderImage[0]) == 'video') {
						//判断轮播图第一张是否是视频，如果是，就赋值给videoLink，并且将其在轮播图中删除
						this.$set(this, 'videoLink', sliderImage[0]);
						sliderImage.splice(0, 1);
					}
					that.$set(that, 'sliderImage', sliderImage);
					that.$set(that, 'productInfo', productInfo);
					that.$set(that, 'merchantInfo', data.merchantInfo);
					that.$set(that, 'description', productInfo.content);
					that.$set(that, 'userCollect', data.userCollect);
					that.$set(that.attr, 'productAttr', data.productAttr);
					that.$set(that, 'productValue', data.productValue);
					that.$set(that, 'guaranteeList', data.guaranteeList ? data.guaranteeList : []);
					that.getMerCustomer(productInfo.merId)
					for (let key in data.productValue) {
						let obj = data.productValue[key];
						that.skuArr.push(obj)
					}
					this.$set(this, "selectSku", that.skuArr[0]);
					// that.$set(that.sharePacket, 'priceName', data.priceName);
					// that.$set(that.sharePacket, 'isState', (data.priceName != "0" && data.priceName!==null) ? false : true);
					// that.$set(that, 'activityH5', data.activityAllH5 ? data.activityAllH5 : []);
					uni.setNavigationBarTitle({
						title: productInfo.name.substring(0, 7) + "..."
					})

					let productAttr = this.attr.productAttr.map(item => {
						return {
							attrName: item.attrName,
							attrValues: item.attrValues.split(','),
							id: item.id,
							isDel: item.isDel,
							productId: item.productId,
							type: item.type
						}
					});
					this.$set(this.attr, 'productAttr', productAttr);

					var navList = ['商品', '评价','详情'];
					// if (this.merchantInfo) {
					// 	navList.splice(2, 0, '推荐')
					// }
					that.$set(that, 'navList', navList);
					if (that.isLogin) {
						that.getCartCount();
						//#ifdef H5
						that.make(that.uid);
						that.ShareInfo();
						this.getImageBase64(this.productInfo.image);
						// #endif
						// #ifdef MP
						that.getQrcode();
						// #endif
					};
					setTimeout(function() {
						that.infoScroll();
					}, 500);
					// #ifdef MP
					that.imgTop = data.productInfo.image
					// #endif
					// #ifndef H5
					that.downloadFilestoreImage();
					// #endif
					that.DefaultSelect();
					this.showSkeleton = false;
					setTimeout(() => {
						this.defaultCoupon = this.coupon.list;
					}, 1000);
					
					setTimeout(() => {
						if (!this.merchantInfo.isSwitch || !this.productInfo.isShow) {
							if (!this.merchantInfo.isSwitch) {
								this.$util.Tips({
									title: '该商品已经下架'
								}, {
									url: '/pages/index/index'
								});
							}
						}
					}, 1000);
				}).catch(err => {
					this.showSkeleton = false;

					//状态异常返回上级页面
					if (this.returnShow) {
						this.$util.Tips({
							title: err.toString()
						}, {
							tab: 3,
							url: 1
						});
					} else {
						this.$util.Tips({
							title: err.toString()
						}, {
							url: '/pages/index/index'
						});
					}
				})
			},
			getProductReplyList: function() {
				getReplyProduct(this.id).then(res => {
					this.reply = res.data.productReply ? [res.data.productReply] : [];
				})
			},
			getProductReplyCount: function() {
				let that = this;
				getReplyConfig(that.id).then(res => {
					that.$set(that, 'replyChance', res.data.replyChance * 100);
					that.$set(that, 'replyCount', res.data.sumCount);
				});
			},
			infoScroll: function() {
				var that = this,
					topArr = [],
					heightArr = [];
				for (var i = 0; i < that.navList.length; i++) { //productList
					//获取元素所在位置
					var query = uni.createSelectorQuery().in(this);
					var idView = "#past" + i;
					if ((!this.replyCount && !that.merchantInfo && i == 1) || (this.replyCount && !that.merchantInfo && i == 2) || (!this.replyCount && that.good_list.length && i == 2)) {
						idView = "#past" + 3;
					}
					if (!this.replyCount && that.merchantInfo && i == 1) {
						idView = "#past" + 2;
					}
					query.select(idView).boundingClientRect();
					query.exec(function(res) {
						var top = res[0].top;
						var height = res[0].height;
						topArr.push(top);
						heightArr.push(height);
						that.$set(that, 'topArr', topArr);
						that.$set(that, 'heightArr', heightArr);
					});
				};
			},
			/**
			 * 默认选中属性
			 * 
			 */
			DefaultSelect: function() {
				let productAttr = this.attr.productAttr;
				let value = [];
				for (let key in this.productValue) {
					if (this.productValue[key].stock > 0) {
						value = this.attr.productAttr.length ? key.split(",") : [];
						break;
					}
				}
				for (let i = 0; i < value.length; i++) {
					this.$set(productAttr[i], "index", value[i]);
				}

				//sort();排序函数:数字-英文-汉字；
				let productSelect = this.productValue[value.join(",")];
				if (productSelect && productAttr.length) {
					this.$set(this.attr.productSelect, "name", this.productInfo.name);
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", productSelect
					.vipPrice); //attr.productSelect.otPrice
					this.$set(this.attr.productSelect, 'otPrice', productSelect.otPrice);
					this.$set(this, "attrValue", value.join(","));
					this.$set(this, "attrTxt", "已选择");
				} else if (!productSelect && productAttr.length) {
					this.$set(this.attr.productSelect, "name", this.productInfo.name);
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", 0);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", this.productInfo.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', this.productInfo.otPrice);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				} else if (!productSelect && !productAttr.length) {
					this.$set(this.attr.productSelect, "name", this.productInfo.name);
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", this.productInfo.stock);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id || "");
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this.attr.productSelect, "vipPrice", this.productInfo.vipPrice);
					this.$set(this.attr.productSelect, 'otPrice', this.productInfo.otPrice);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				}
			},
			/**
			 * 获取优惠券
			 * 
			 */
			getCouponList() {
				let that = this,
					obj = {
						page: 1,
						limit: 20,
						productId: that.id,
						category: 0
					};
				getCoupons(obj).then(res => {
					that.$set(that.coupon, 'list', res.data.list);
				});
			},
			async getCouponType() {
				//在onLoad只调用一次，获取默认的类型作为打开优惠券列表的参数，不会随着切换变化
				let dataList = await getCoupons({
					productId: this.id
				});
				if (dataList.length) {
					this.couponDeaultType = dataList.data;
					this.$set(this.coupon, 'type', dataList);
				}

			},
			tabCouponType(type) {
				this.$set(this.coupon, 'type', type);
				this.getCouponList();
			},

			ChangCouponsUseState(index) {
				let that = this;
				that.coupon.list[index].isUse = true;
				that.$set(that.coupon, 'list', that.coupon.list);
				that.$set(that.coupon, 'coupon', false);
			},
			/** 
			 * 
			 * 
			 * 收藏商品
			 */
			setCollect: Debounce(function() {
				let that = this;
				if (this.isLogin === false) {
					toLogin();
				} else {
					if (this.userCollect) {
						collectCancel({ids: this.productInfo.id}).then(res => {
							that.$set(that, 'userCollect', !that.userCollect);
							that.$util.Tips({
								title: "取消收藏"
							});
						})
					} else {
						collectAdd(this.productInfo.id).then(res => {
							that.$set(that, 'userCollect', !that.userCollect);
							that.$util.Tips({
								title: "收藏成功"
							});
						})
					}
				}
			}),
			/**
			 * 打开属性插件
			 */
			selecAttr: function() {
				this.$set(this.attr, 'cartAttr', true);
				this.$set(this, 'isOpen', true);
			},
			/**
			 * 打开优惠券插件
			 */
			couponTap: function() {
				let that = this;
				if (that.isLogin === false) {
					toLogin();
				} else {
					that.getCouponList(); //打开弹框默认请求商品券
					that.$set(that.coupon, 'coupon', true);
				}
			},
			onMyEvent: function() {
				this.$set(this.attr, 'cartAttr', false);
				this.$set(this, 'isOpen', false);
			},
			/**
			 * 打开属性加入购物车
			 * 
			 */
			joinCart: Debounce(function(e) {
				//是否登录
				if (this.isLogin === false) {
					toLogin();
				} else {
					this.goCat(1);
				}
			}),
			/*
			 * 加入购物车
			 */
			goCat: function(num) {
				let that = this;
					
				if ((!that.attr.productSelect || that.attr.productSelect.stock === 0) && that.isOpen === true) {
					return that.$util.Tips({
						title: "产品库存不足，请选择其它"
					});
				}
				if (that.isGlassesProduct && that.isOpen === true) {
					if (!that.attr.customData || !that.attr.customData.lens) {
						return that.$util.Tips({
							title: "请按步骤选择验光单与镜片"
						});
					}
				}

				// 打开属性弹窗
				that.attr.cartAttr = !that.isOpen ? true : false;

				if (that.attr.cartAttr === true && that.isOpen === false) {
					return (that.isOpen = true);
				}
	
				if (num === 1) {
					let params = {
						productId: parseFloat(that.id),
						cartNum: parseFloat(that.attr.productSelect.cart_num),
						isNew: false,
						productAttrUnique: that.attr.productSelect !== undefined ?
							that.attr.productSelect.unique : that.productInfo.id,
						customData: that.attr.customData !== undefined ? JSON.stringify(that.attr.customData) : ""
					};
					postCartAdd(params).then(function(res) {
						that.isOpen = false;
						that.attr.cartAttr = false;

						that.$util.Tips({
							title: "添加购物车成功",
							success: () => {
								that.getCartCount(true);
							}
						});
					}).catch(res => {
						that.isOpen = false;
						return that.$util.Tips({
							title: res
						});
					});
				} else {
					this.getPreOrder();
				}
			},
			/**
			 * 获取购物车数量
			 * @param boolean 是否展示购物车动画和重置属性
			 */
			getCartCount: function(isAnima) {
				let that = this;
				const isLogin = that.isLogin;
				if (isLogin) {
					getCartCounts(true, 'total').then(res => {
						that.CartCount = res.data.count;
						//加入购物车后重置属性
						if (isAnima) {
							that.animated = true;
							setTimeout(function() {
								that.animated = false;
							}, 500);
						}
					});
				}
			},
			/**
			 * 立即购买
			 */
			goBuy: Debounce(function(e) {
				if (this.isLogin === false) {
					toLogin();
				} else {
					this.goCat(0);
				}
			}),
			/**
			 * 预下单
			 */
			getPreOrder: function() {
				if (this.attr.productSelect.cart_num < 1) {
					uni.showToast({title: '单次可购买商品数量范围为 1~99',icon: 'none'});
				} else {
					this.$Order.getPreOrder(this.type === 'normal' ? 'buyNow' : 'video', [{
						"attrValueId": parseFloat(this.attr.productSelect.unique),
						"productId": parseFloat(this.id),
						"productNum": parseFloat(this.attr.productSelect.cart_num),
						"customData": this.attr.customData !== undefined ? JSON.stringify(this.attr.customData) : ""
					}]);
				}
				this.isOpen = false;
			},
			/**
			 * 分享打开
			 * 
			 */
			listenerActionSheet: function() {
				if (this.isLogin === false) {
					toLogin();
				} else {
					// #ifdef H5
					if (this.$wechat.isWeixin() === true) {
						this.weixinStatus = true;
					}
					// #endif
					this.goPoster()
				}
			},
			closePosters: function() {
				this.posters = false;
				this.currentPage = false;
			},
			//隐藏海报
			posterImageClose: function() {
				this.canvasStatus = false
				this.posters = false;
			},
			//替换安全域名
			setDomain: function(url) {
				url = url ? url.toString() : '';
				//本地调试打开,生产请注销
				if (url.indexOf("https://") > -1) return url;
				else return url.replace('http://', 'https://');
			},
			//获取海报产品图（解决跨域问题，只适用于小程序）
			downloadFilestoreImage: function() {
				let that = this;
				uni.downloadFile({
					url: that.setDomain(that.productInfo.image),
					success: function(res) {
						that.storeImage = res.tempFilePath;
					},
					fail: function() {
						return that.$util.Tips({
							title: ''
						});
						that.storeImage = '';
					},
				});
			},
			// 小程序关闭分享弹窗；
			goFriend: function() {
				this.posters = false;
			},
			// 小程序二维码
			getQrcode() {
				let that = this;
				let data = {
					pid: that.uid,
					id: that.id,
					mid: this.productInfo.merId, // 当前门店
					path: 'pages/goods/goods_details/index'
				}

				mpQrcode(data).then(res => {
					base64src(res.data.code, Date.now(), res => {
						that.PromotionCode = res;
					});
				}).catch(err => {
					that.errT = err;
				});
			},
			// 生成二维码；
			make(uid) {
				let href = location.href.split('?')[0] + "?id=" + this.id + "&spread=" + this.uid + "&mid=" + this.productInfo.merId;
				uQRCode.make({
					canvasId: 'qrcode',
					text: href,
					size: this.qrcodeSize,
					margin: 10,
					success: res => {
						this.PromotionCode = res;
					},
					complete: () => {},
					fail: res => {
						this.$util.Tips({
							title: '海报二维码加载失败！'
						});
					}
				})
			},
			getImageBase64: function(images) {
				let that = this;
				imageBase64({
					url: images
				}).then(res => {
					that.imgTop = res.data.code;
				})
			},
			/**
			 * 获取产品分销二维码
			 * @param function successFn 下载完成回调
			 *
			 */
			downloadFilePromotionCode: function(successFn) {
				let that = this;
				getProductCode(that.id)
					.then(res => {
						uni.downloadFile({
							url: that.setDomain(res.data.code),
							success: function(res) {
								that.$set(that, 'isDown', false);
								if (typeof successFn == 'function') successFn && successFn(res
									.tempFilePath);
								else that.$set(that, 'PromotionCode', res.tempFilePath);
							},
							fail: function() {
								that.$set(that, 'isDown', false);
								that.$set(that, 'PromotionCode', '');
							}
						});
					})
					.catch(err => {
						that.$set(that, 'isDown', false);
						that.$set(that, 'PromotionCode', '');
					});
			},
			/**
			 * 生成海报
			 */
			goPoster: function() {
				let that = this;

				if (!that.PromotionCode) {
					uni.hideLoading();
					that.$util.Tips({
						title: '正在加载商品二维码'
					});
					return
				}
				if (!that.imgTop) {
					uni.hideLoading();
					that.$util.Tips({
						title: '无法生成商品海报！'
					});
					return
				}

				uni.showLoading({
					title: '海报生成中',
					mask: true
				});

				that.posters = true;
				let arrImagesUrlTop = '';

				uni.downloadFile({
					url: that.imgTop,
					success: (res) => {
						arrImagesUrlTop = res.tempFilePath;
						let arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];
						let name = that.productInfo.name;
						let price = that.productInfo.price;
						setTimeout(() => {
							that.$util.PosterCanvas(arrImages, name, price, that.productInfo
								.otPrice,
								function(tempFilePath) {
									that.imagePath = tempFilePath;
									that.canvasStatus = true;
									uni.hideLoading();
								});
						}, 500);
					},
					fail: () => {
						uni.hideLoading();
						that.$util.Tips({
							title: '商品图片加载失败'
						});
					}
				});
			},
			// 图片预览；
			getpreviewImage: function() {
				if (this.imagePath) {
					let photoList = [];
					photoList.push(this.imagePath)
					uni.previewImage({
						urls: photoList,
						current: this.imagePath
					});
				} else {
					this.$util.Tips({
						title: '您的海报尚未生成'
					});
				}
			},
			/*
			 * 保存到手机相册
			 */
			// #ifdef MP
			savePosterPath: function() {
				let that = this;
				uni.getSetting({
					success(res) {
						if (!res.authSetting['scope.writePhotosAlbum']) {
							uni.authorize({
								scope: 'scope.writePhotosAlbum',
								success() {
									uni.saveImageToPhotosAlbum({
										filePath: that.imagePath,
										success: function(res) {
											that.posterImageClose();
											that.$util.Tips({
												title: '保存成功',
												icon: 'success'
											});
										},
										fail: function(res) {
											that.$util.Tips({
												title: '保存失败'
											});
										}
									})
								}
							})
						} else {
							uni.saveImageToPhotosAlbum({
								filePath: that.imagePath,
								success: function(res) {
									that.posterImageClose();
									that.$util.Tips({
										title: '保存成功',
										icon: 'success'
									});
								},
								fail: function(res) {
									that.$util.Tips({
										title: '保存失败'
									});
								},
							})
						}
					}
				})
			},
			// #endif
			ShareInfo() {
				let data = this.productInfo;
				let href = location.href;
				if (this.$wechat.isWeixin()) {
					href = href.indexOf("?") === -1 ? href + "?spread=" + this.uid : href + "&spread=" + this.uid;
					let configAppMessage = {
						desc: data.storeInfo,
						title: data.name,
						link: href,
						imgUrl: data.image
					};
					this.$wechat.wechatEvevt([
						"updateAppMessageShareData",
						"updateTimelineShareData",
						"onMenuShareAppMessage",
						"onMenuShareTimeline"
					], configAppMessage).then(res => {
					}).catch(err => {
						console.log(err);
					})
				}
			},
			showShare(status) {
				let that = this;
				that.$set(that.sharePacket, 'touchstart', status);
			},
			hideNav() {
				this.currentPage = false;
			},
			//下拉导航页面跳转
			linkPage(url) {
				if (url == '/pages/index/index' || url == '/pages/order_addcart/order_addcart' || url ==
					'/pages/user/index') {
					uni.switchTab({
						url
					})
				} else {
					uni.navigateTo({
						url
					})
				}
				this.currentPage = false
			},
			//点击sku图片打开轮播图
			showImg(index) {
				this.$refs.cusPreviewImg.open(this.selectSku.sku)
			},
			//滑动轮播图选择商品
			changeSwitch(e) {
				let productSelect = this.skuArr[e];
				this.$set(this, 'selectSku', productSelect);
				var skuList = productSelect.sku.split(',');
				skuList.forEach((i, index) => {
					this.$set(this.attr.productAttr[index], 'index', skuList[index]);
				})
				if (productSelect) {
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "vipPrice", productSelect.vipPrice);
					this.$set(this, "attrTxt", "已选择");
					this.$set(this, "attrValue", productSelect.sku)
				}
			},
			getFileType(fileName) {
				// 后缀获取
				let suffix = '';
				// 获取类型结果
				let result = '';
				try {
					const flieArr = fileName.split('.');
					suffix = flieArr[flieArr.length - 1];
				} catch (err) {
					suffix = '';
				}
				// fileName无后缀返回 false
				if (!suffix) {
					return false;
				}
				suffix = suffix.toLocaleLowerCase();
				// 图片格式
				const imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif'];
				// 进行图片匹配
				result = imglist.find(item => item === suffix);
				if (result) {
					return 'image';
				}
				// 匹配 视频
				const videolist = ['mp4', 'm2v', 'mkv', 'rmvb', 'wmv', 'avi', 'flv', 'mov', 'm4v'];
				result = videolist.find(item => item === suffix);
				if (result) {
					return 'video';
				}
				// 其他 文件类型
				return 'other';
			},
			videoPause() {

			},
			// #ifdef APP-PLUS
			appShare(scene) {
				let that = this
				let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
				let curRoute = routes[routes.length - 1].$page.fullPath // 获取当前页面路由，也就是最后一个打开的页面路由
				uni.share({
					provider: "weixin",
					scene: scene,
					type: 0,
					href: `${HTTP_H5_URL}${curRoute}&spread=${that.uid}`,
					title: that.productInfo.name,
					summary: that.productInfo.storeInfo,
					imageUrl: that.productInfo.image,
					success: function(res) {
						that.posters = false;
					},
					fail: function(err) {
						uni.showToast({
							title: '分享失败',
							icon: 'none',
							duration: 2000
						})
						that.posters = false;
					}
				});
			},
			// #endif
		},
	}
</script>

<style scoped lang="scss">
	.superior {

		/deep/.name,
		/deep/.icon-jiantou {
			color: #333 !important;
		}

		/deep/.store {
			padding: 0 !important;
		}
		/deep/image,.easy-loadimage,image,uni-image {
			width: 86rpx;
			height: 86rpx;
			border-radius: 6px;
			opacity: 1
		}
	}

	.product-con {
		height: 100%;
	}

	.ensure {
		width: 100%;
		background-color: #fff;
		padding-bottom: 15rpx;
		padding-bottom: calc(15rpx+ constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);

		.title {
			font-size: 32rpx;
			color: #282828;
			text-align: center;
			margin: 38rpx 0 36rpx 0;
			position: relative;

			.iconfont {
				position: absolute;
				right: 30rpx;
				top: 0;
				font-size: 36rpx;
			}
		}

		.list {
			max-height: 750rpx;
			margin: 0 30rpx;
			overflow-x: hidden;
			overflow-y: auto;

			.item {
				margin-bottom: 52rpx;

				.pictrue {
					width: 36rpx;
					height: 36rpx;
					border-radius: 50%;
					margin-right: 30rpx;

					/deep/image,.easy-loadimage,image,uni-image {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}

				.text {
					width: 618rpx;
					color: #999999;
					font-size: 28rpx;

					.name {
						color: #333333;
						font-weight: bold;
						margin-bottom: 20rpx;
					}
				}
			}
		}

		.bnt {
			width: 690rpx;
			height: 86rpx;
			text-align: center;
			line-height: 86rpx;
			border-radius: 43rpx;
			@include main_bg_color(theme);
			font-size: 30rpx;
			color: #fff;
			margin: 0 auto;
		}
	}

	.ensure.on {
		transform: translate3d(0, 0, 0);
	}

	/deep/ .tui-drawer-container_bottom {
		border-radius: 16rpx 16rpx 0 0;
	}

	.product-con {
		height: 100%;
	}

	.x-money {
		font-size: 28rpx;
		font-weight: 700;
		@include price_color(theme);

	}

	.bg-color-hui {
		background: #bbb !important;
		border-radius: 0 25px 25px 0;
	}

	.select_nav {
		height: 60rpx !important;
		border-radius: 33rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0, 0, 0, 0.07);
		color: #000;
		position: fixed;
		font-size: 18px;
		line-height: 62rpx;
		z-index: 1000;
		left: 14rpx;
	}

	.px-20 {
		padding: 0 20rpx 0;
	}

	.nav_line {
		content: '';
		display: inline-block;
		width: 1px;
		height: 34rpx;
		background: #b3b3b3;
		position: absolute;
		left: 0;
		right: 0;
		margin: auto;
	}

	.bgwhite {
		background: #fff;
	}

	.input {
		display: flex;
		align-items: center;
		/* #ifdef MP */
		width: 300rpx;
		/* #endif */
		/* #ifndef MP */
		width: 460rpx;
		/* #endif */
		height: 58rpx;
		padding: 0 0 0 30rpx;
		border: 1px solid rgba(0, 0, 0, 0.07);
		border-radius: 33rpx;
		color: #666;
		font-size: 26rpx;
		position: fixed;
		left: 0;
		right: 0;
		margin: auto;
		background: rgba(255, 255, 255, 0.3);

		.iconfont {
			margin-right: 20rpx;
			font-size: 26rpx;
			color: #666666;
		}
	}

	.container_detail {
		/* #ifdef MP */
		margin-top: 32rpx;
		/* #endif */
	}

	.tab_nav {
		height: 60rpx;
		margin-left: 184rpx;
		position: fixed;
	}

	.right_select {
		width: 50rpx;
		height: 50rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 50%;
		position: fixed;
		right: 20rpx;
		text-align: center;
		line-height: 50rpx;
		/* #ifdef APP-PLUS*/
		top: 62rpx !important;
		/* #endif */
	}

	.dialog_nav {
		position: absolute;
		/* #ifdef MP */
		left: 14rpx;
		/* #endif */
		/* #ifdef H5 || APP-PLUS*/
		right: 14rpx;
		/* #endif */
		width: 240rpx;
		background: #FFFFFF;
		box-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);
		z-index: 310;
		border-radius: 14rpx;

		&::before {
			content: '';
			width: 0;
			height: 0;
			position: absolute;
			/* #ifdef MP */
			left: 0;
			right: 0;
			margin: auto;
			/* #endif */
			/* #ifdef H5 || APP-PLUS */
			right: 8px;
			/* #endif */
			top: -9px;
			border-bottom: 10px solid #F5F5F5;
			border-left: 10px solid transparent;
			/*transparent 表示透明*/
			border-right: 10px solid transparent;
		}
	}

	.dialog_nav_item {
		width: 100%;
		height: 84rpx;
		line-height: 84rpx;
		padding: 0 20rpx 0;
		box-sizing: border-box;
		border-bottom: #eee;
		font-size: 28rpx;
		color: #333;
		position: relative;

		.iconfont {
			font-size: 32rpx;
		}
	}

	.dialog_after {
		::after {
			content: '';
			position: absolute;
			width: 172rpx;
			height: 1px;
			background-color: #EEEEEE;
			bottom: 0;
			right: 0;
		}
	}

	.pl-20 {
		padding-left: 20rpx;
	}

	.activity {
		padding: 0 20rpx;
		@include coupons_border_color(theme);
		@include main_color(theme);
		font-size: 24rpx;
		line-height: 32rpx;
		position: relative;
		margin-left: 4rpx;
	}

	.product-con .wrapper .coupon .activity:before {
		content: ' ';
		position: absolute;
		width: 7rpx;
		height: 10rpx;
		border-radius: 0 7rpx 7rpx 0;
		@include coupons_border_color(theme);
		background-color: #fff !important;
		bottom: 50%;
		left: -3rpx;
		margin-bottom: -6rpx;
		@include white_left_border;
	}

	.product-con .wrapper .coupon .activity:after {
		content: ' ';
		position: absolute;
		width: 7rpx;
		height: 10rpx;
		border-radius: 7rpx 0 0 7rpx;
		@include coupons_border_color(theme);
		background-color: #fff;
		right: -3rpx;
		bottom: 50%;
		margin-bottom: -6rpx;
		// border-right-color: #fff;
		@include white_right_border;
	}

	.justify-center {
		justify-content: center;
	}

	.align-center {
		align-items: center;
	}

	.align-baseline {
		align-items: baseline;
	}

	.bg_color {
		@include main_bg_color(theme);
	}

	.vip_icon {
		width: 44rpx;
		height: 28rpx;
	}

	.pl-2 {
		padding-left: 20rpx;
	}

	.vip_money {
		background: #FFE7B9;
		border-radius: 4px;
		font-size: 22rpx;
		color: #333;
		line-height: 28rpx;
		text-align: center;
		padding: 0 6rpx;
		box-sizing: border-box;
		margin-left: -4rpx;
	}

	.theme_price {
		@include price_color(theme);
		margin-top: 5rpx;
	}

	.activityName {
		line-height: 44rpx;
	}

	.userEvaluation {
		i {
			display: inline-block;
		}
	}

	.bntVideo {
		width: auto !important;

		.buy {
			border-radius: 50rpx !important;
		}
	}

	.attribute {
		.line1 {
			width: 600rpx;
		}
	}

	.chat-btn {
		background-color: antiquewhite !important;
	}

	.activity_pin {
		width: auto;
		height: 44rpx;
		line-height: 44rpx;
		// background: linear-gradient(90deg, rgba(233, 51, 35, 1) 0%, rgba(250, 101, 20, 1) 100%);
		@include linear-gradient(theme);
		opacity: 1;
		border-radius: 22rpx;
		padding: 0 15rpx;
		// margin-left: 19rpx;
	}

	.activity_miao {
		width: auto;
		height: 44rpx;
		line-height: 44rpx;
		padding: 0 15rpx;
		// background: linear-gradient(90deg, rgba(250, 102, 24, 1) 0%, rgba(254, 161, 15, 1) 100%);
		@include linear-gradient(theme);
		opacity: 1;
		border-radius: 22rpx;
		margin-left: 19rpx;
	}

	.iconfonts {
		color: #fff !important;
		font-size: 28rpx;
	}

	.activity_title {
		font-size: 24rpx;
		color: #fff;
	}

	.activity_kan {
		width: auto;
		height: 44rpx;
		line-height: 44rpx;
		padding: 0 15rpx;
		@include linear-gradient(theme);
		opacity: 1;
		border-radius: 22rpx;
		margin-left: 19rpx;
	}

	.mask {
		z-index: 300 !important;
	}

	.head-bar {
		background: #fff;
	}

	.generate-posters {
		width: 100%;
		background-color: #fff;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 388;
		transform: translate3d(0, 100%, 0);
		transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
		border-top: 1rpx solid #eee;

		.generateCon {
			height: 220rpx;
		}

		.generateClose {
			height: 98rpx;
			font-size: 28rpx;
			color: #333333;
			border-top: 1px solid #eee;
			margin-bottom: 0rpx;
			margin-bottom: calc(0rpx+ constant(safe-area-inset-bottom) / 2);
			margin-bottom: calc(0rpx + env(safe-area-inset-bottom) / 2);
		}

		.item {
			.pictrue {
				width: 96rpx;
				height: 96rpx;
				border-radius: 50%;
				margin: 0 auto 6rpx auto;

				/deep/image,.easy-loadimage,image,uni-image {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}
		}
	}

	.generate-posters.on {
		transform: translate3d(0, 0, 0);
	}

	.generate-posters .item {
		flex: 1;
		text-align: center;
		font-size: 30rpx;
	}

	.generate-posters .item .iconfont {
		font-size: 80rpx;
		color: #5eae72;
	}

	.generate-posters .item .iconfont.icon-haibao {
		color: #5391f1;
	}

	.generate-posters .item .iconfont.icon-haowuquan1 {
		color: #ff954d;
	}

	.product-con .footer {
		position: fixed;
		bottom: 0;
		width: 100%;
		box-sizing: border-box;
		background-color: #fff;
		z-index: 277;
		border-top: 1rpx solid #f0f0f0;
		padding: 15rpx 30rpx;
		padding-bottom: calc(15rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);
	}

	.product-con .footer .item {
		font-size: 18rpx;
		color: #666;
	}

	.product-con .footer .item .iconfont {
		text-align: center;
		font-size: 40rpx;
	}

	.product-con .footer .item .iconfont.icon-shoucang1 {
		@include main_color(theme);
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 {
		font-size: 40rpx;
		position: relative;
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 .num {
		color: #fff;
		position: absolute;
		font-size: 18rpx;
		padding: 2rpx 8rpx 3rpx;
		border-radius: 200rpx;
		top: -10rpx;
		right: -10rpx;
	}

	.product-con .footer .bnt {
		width: 444rpx;
		height: 76rpx;
	}

	.product-con .footer .bnt .bnts {
		width: 222rpx;
		text-align: center;
		line-height: 76rpx;
		color: #fff;
		font-size: 28rpx;
	}

	.product-con .footer .bnt .joinCart {
		border-radius: 50rpx 0 0 50rpx;
		@include left_color(theme);
	}

	.product-con .footer .bnt .buy {
		border-radius: 0 50rpx 50rpx 0;
		@include main_bg_color(theme);
	}

	.product-con .store-info {
		margin-top: 20rpx;
		background-color: #fff;
	}

	.product-con .store-info .title {
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #282828;
		height: 80rpx;
		line-height: 80rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.product-con .store-info .info {
		padding: 0 30rpx;
		height: 126rpx;
	}

	.product-con .store-info .info .picTxt {
		width: 615rpx;
	}

	.product-con .store-info .info .picTxt .pictrue {
		width: 76rpx;
		height: 76rpx;
	}

	.product-con .store-info .info .picTxt .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.product-con .store-info .info .picTxt .text {
		width: 522rpx;
	}

	.product-con .store-info .info .picTxt .text .name {
		font-size: 30rpx;
		color: #282828;
	}

	.product-con .store-info .info .picTxt .text .address {
		font-size: 24rpx;
		color: #666;
		margin-top: 3rpx;
	}

	.product-con .store-info .info .picTxt .text .address .iconfont {
		color: #707070;
		font-size: 18rpx;
		margin-left: 10rpx;
	}

	.product-con .store-info .info .picTxt .text .address .addressTxt {
		max-width: 480rpx;
	}

	.product-con .store-info .info .iconfont {
		font-size: 40rpx;
	}

	.product-con .superior {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 24rpx;
		padding-bottom: 4rpx;
		border-top-left-radius: 14rpx;
		border-top-right-radius: 14rpx;
		.title {
			height: 98rpx;
			image {
				width: 20rpx;
				height: 20rpx;
			}
			.titleTxt {
				margin: 0 10rpx;
				font-size: 30rpx;
				color: #333333;
			}
		}
	}
	.product-con .slider-banner {
		background-color: #fff;
		width: 100%;
		margin: 0 auto;
		position: relative;
		padding: 0 14rpx;
		padding-bottom: 24rpx;
		border-bottom-left-radius: 14rpx;
		border-bottom-right-radius: 14rpx;
		.list {
			width: 100%;
			.item {
				width: 33.33%;
				font-size: 26rpx;
				padding: 0 10rpx;
				padding-top: 20rpx;
				.pictrue {
					position: relative;
					height: 204rpx;
					border-radius: 6rpx;
					overflow: hidden;
					/deep/image,.easy-loadimage,image,uni-image {
						width: 100% !important;
						height: 100% !important;
						border-radius: 6rpx;
					}
				}
				.name {
					color: #282828;
					margin-top: 10rpx;
				}
			}
			.item:nth-of-type(3n) {
				margin-right: 0;
			}
		}
	}
	.product-con .footer-height {
		height: 126rpx;
		height: calc(126rpx + constant(safe-area-inset-bottom) / 2);
		height: calc(126rpx + env(safe-area-inset-bottom) / 2);
	}

	button {
		padding: 0;
		margin: 0;
		line-height: normal;
		background-color: #fff;
	}

	button::after {
		border: 0;
	}

	action-sheet-item {
		padding: 0;
		height: 240rpx;
		align-items: center;
		display: flex;
	}

	.contact {
		font-size: 16px;
		width: 50%;
		background-color: #fff;
		padding: 8rpx 0;
		border-radius: 0;
		margin: 0;
		line-height: 2;
	}

	.contact::after {
		border: none;
	}

	.action-sheet {
		font-size: 17px;
		line-height: 1.8;
		width: 50%;
		position: absolute;
		top: 0;
		right: 0;
		padding: 25rpx 0;
	}

	.canvas {
		position: fixed;
		z-index: -5;
		opacity: 0;
	}

	.poster-pop {
		position: fixed;
		width: 450rpx;
		height: 714rpx;
		top: 50%;
		left: 50%;
		transform: translateX(-50%);
		margin-top: -432rpx;
		z-index: 399;
	}

	.poster-pop image {
		width: 100%;
		height: 100%;
		display: block;
	}

	.poster-pop .close {
		width: 46rpx;
		height: 75rpx;
		position: fixed;
		right: 0;
		top: -73rpx;
		display: block;
	}

	.poster-pop .save-poster {
		background-color: #df2d0a;
		font-size: ：22rpx;
		color: #fff;
		text-align: center;
		height: 76rpx;
		line-height: 76rpx;
		width: 100%;
	}

	.poster-pop .keep {
		color: #fff;
		text-align: center;
		font-size: 25rpx;
		margin-top: 10rpx;
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
	}

	.pro-wrapper .iconn {
		background-image: url('data:image/png;base64,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');
		width: 100rpx;
		height: 100rpx;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		margin: 0 auto;
	}

	.pro-wrapper .iconn.iconn1 {
		background-image: url('data:image/png;base64,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');
	}

	.pictrue_log {
		width: 80upx;
		height: 40upx;
		border-radius: 10upx 0 12upx 0;
		line-height: 40upx;
		font-size: 24upx;
	}

	.pictrue_log_class {
		z-index: 3;
		background: -webkit-gradient(linear, left top, right top, from(rgba(246, 122, 56, 1)), to(rgba(241, 27, 9, 1)));
		background: linear-gradient(90deg, rgba(246, 122, 56, 1) 0%, rgba(241, 27, 9, 1) 100%);
		opacity: 1;
		position: absolute;
		top: 0;
		left: 0;
		color: #fff;
		text-align: center;

	}

	.tab_nav .header {
		/* #ifndef MP */
		width: 94%;
		/* #endif */
		/* #ifdef MP || APP-PLUS */
		width: 100%;
		/* #endif */
		height: 60rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #050505;
		margin: auto;
		background-color: #fff;
	}

	.icon-xiangzuo {
		/* #ifdef H5 */
		top: 20rpx !important;
		/* #endif */
	}

	.navbar .header .item {
		position: relative;
		margin: 0 25rpx;
	}

	.navbar .header .item.on:before {
		position: absolute;
		width: 60rpx;
		height: 5rpx;
		background-repeat: no-repeat;
		content: "";
		@include linear-gradient(theme);
		bottom: -10rpx;
		left: 46.5%;
		margin-left: -28rpx;
	}

	.navbar {
		position: fixed;
		// background-color: #fff;
		top: 0;
		left: 0;
		z-index: 99;
		width: 100%;
	}

	.navbar .navbarH {
		position: relative;
	}

	.navbar .navbarH .navbarCon {
		position: absolute;
		bottom: 0;
		height: 100rpx;
		width: 100%;
	}

	.h5_back {
		color: #000;
		position: fixed;
		left: 20rpx;
		font-size: 26rpx;
		text-align: center;
		width: 50rpx;
		height: 50rpx;
		line-height: 50rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 50%;
	}

	.share-box {
		z-index: 1000;
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.mask_transparent {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: transparent;
		z-index: 300;
	}

	.px-12 {
		padding-left: 12rpx;
		padding-right: 12rpx;
	}

	.font-44 {
		font-size: 44rpx;
	}

	.font_color {
		@include main_color(theme);
	}

	.attrImg {
		width: 66rpx;
		height: 66rpx;
		border-radius: 6rpx;
		display: block;
		margin-right: 14rpx;
	}

	.switchTxt {
		height: 60rpx;
		flex: 1;
		line-height: 60rpx;
		box-sizing: border-box;
		background: #EEEEEE;
		padding-right: 0 24rpx 0;
		border-radius: 8rpx;
		text-align: center;
	}
	.text-666 {
		color: #666;
	}
	.text-333 {
		color: #333 !important;
	}
	.icon-gou2 {
		@include main_color(theme)
	}
	.product-bg {
		/* #ifdef APP-PLUS */
		margin-top: 52rpx !important;
		/* #endif */
	}
	.share {
		align-items: center;
	}
	.share-btn {
		display: flex;
		font-size: 28rpx;
		align-items: center;
		justify-content: center;
		.share-txt {
			margin-right: 5rpx;
			@include second_color(theme);
		}
		.share-icon {
			margin: 0 !important;
			font-size: 32rpx !important;
			@include second_color(theme);
		}
	}

	.glasses-try-on {
		@include linear-gradient(theme);
		position: fixed;
		right: 0;
		z-index: 100;
		height: 60rpx;
		line-height: 60rpx;
		border-radius: 30rpx 0 0 30rpx;
		text-align: center;
		color: #fff;
		display: flex;
		padding: 0 10rpx 0 15rpx;
		font-weight: 400;
		
		.iconfont {
			height: 60rpx;
			line-height: 56rpx;
			font-size: 36rpx;
			padding-right: 5rpx;
		}
		.txt {
			height: 60rpx;
			line-height: 60rpx;
			font-size: 26rpx;
		}
	}
</style>