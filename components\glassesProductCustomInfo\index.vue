<template>
	<view>
		<view @click.stop="customClick" class="glasses" v-if="customData && (customData.lens || customData.optometry)">
			<view class="lens">{{customData.lens.name || customData.lens}}</view>
			<navigator class="optometry" v-if="customData.optometry" :url="'/pages/optometry/user_optometry_details/index?optometry=' + JSON.stringify(customData.optometry)">
				<view class="optometry-title">验光单信息</view>
				<view class="optometry-arrow iconfont icon-jiantou"></view>
			</navigator>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			customData: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
			};
		},
		methods: {
			customClick() {
			}
		}
	}
</script>

<style scoped lang="scss">
	.glasses {
		width: 396rpx;
		font-size: 28rpx;
		padding: 10rpx;
		@include main_color(theme);
		
		.lens {
			display: block;
			font-size: 26rpx;
			padding: 10rpx 0;
		}
		.optometry {
			width: max-content;
			align-items: center;
			display: flex;
			-webkit-box-lines: multiple;
			-moz-box-lines: multiple;
			-o-box-lines: multiple;
			flex-wrap: wrap;
			font-size: 26rpx;
			
			&-title {
				font-size: 26rpx;
				margin-right: 6rpx;
			}
			&-arrow {
				font-size: 26rpx;
				margin-top: 5rpx;
			}
		}
	}
</style>
