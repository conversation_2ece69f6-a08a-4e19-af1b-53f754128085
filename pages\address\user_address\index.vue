<template>
	<view :data-theme="theme">
		<form @submit="formSubmit" report-submit='true'>

			<view class='addAddress borderPad'>
				<view class='list borRadius14'>
					<view class='item acea-row row-between-wrapper' style="border: none;">

<!-- 			<view class='addAddress'>
	
				<view class='info-container borRadius14'>
					<view class='item acea-row row-between-wrapper'> -->

						<view class='name'>收货人</view>
						<input type='text' placeholder='请输入收货人姓名' placeholder-style="color:#ccc;" name='realName'
							:value="userAddress.realName" placeholder-class='placeholder' maxlength="20"></input>
					</view>

					<view class='item acea-row row-between-wrapper' style="border-bottom: none;">

						<view class='name'>手机号码</view>
						<input type='number' placeholder='请输入收货人手机号' placeholder-style="color:#ccc;" name="phone"
							:value='userAddress.phone' placeholder-class='placeholder' maxlength="11"></input>
					</view>
				</view>

				<!-- 地址信息容器 -->
				<view class='info-container borRadius14'>
					<view class='item acea-row row-between-wrapper relative'>
						<view class='name'>所在地区</view>
						<view class="address">
							<view class="region_count" @click="changeRegion">
								<text v-if="!addressInfo.length" style="color:#cdcdcd;padding-right: 60rpx;">省市区县/乡镇等</text>
								<view v-else class='abs_text'>{{addressText}}</view>
								<view class='iconfont icon-dizhi font_color abs_right' @tap="chooseLocation"></view>
							</view>
						</view>
					</view>
					<view class='item acea-row row-between-wrapper relative' style="border-bottom: none;">
						<view class='name'>详细地址</view>
						<input type='text' placeholder='街道/楼牌号等' placeholder-style="color:#ccc;" name='detail'
							placeholder-class='placeholder' v-model='userAddress.detail' maxlength="100"></input>
						
					</view>
				</view>

				<!-- 默认地址设置容器 -->
				<view class='default-container borRadius14' @click='ChangeIsDefault'>
					<view class='acea-row row-between-wrapper'>
						<view class='default-text'>设置默认收货地址</view>
						<switch :checked="userAddress.isDefault" color="#4CAF50" />
					</view>
					<view class='default-tip'>提醒：下单会优先使用该地址</view>
				</view>

				<!-- #ifdef MP -->
				<!-- <view class="wechatAddress" v-if="!id" @click="getWxAddress">导入微信地址</view> -->
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<!-- <view class="wechatAddress" v-if="this.$wechat.isWeixin() && !id" @click="getAddress">导入微信地址</view> -->
				<!-- #endif -->
			</view>
			<view class="page-footer" v-if="!display">
				<view class="button-wrapper">
					<button class="action-btn delete-btn" v-if="id" @click.stop="deleteAddress">删除</button>
					<button class="action-btn save-btn" form-type="submit" :class="{'full-width': !id}">保存</button>
				</view>
			</view>
		</form>
		<view v-show="showLoading" class="bg-fixed"></view>
		<areaWindow ref="areaWindow" :display="display" :address="addressInfo" @submit="OnChangeAddress"
			@changeClose="changeClose" ></areaWindow>
	</view>
</template>

<script>
	import {
		editAddress,
		addAddress,
		getAddressDetail,
		getCity,
		delAddress
	} from '@/api/user.js';
	import {
		getCityList
	} from "@/utils";
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import areaWindow from '../components/areaWindow';
	import {
		Debounce
	} from '@/utils/validate.js'
	
	import {
		addressWxImportApi
	} from '@/api/public.js';
	let app = getApp();
	export default {
		components: {
			areaWindow
		},
		data() {
			return {
				id: 0,
				isSelect: false, // 是否为选择地址模式
				userAddress: {
					isDefault: false
				},
				region: ['省', '市', '区'],
				valueRegion: [0, 0, 0, 0],
				district: [],
				multiArray: [],
				multiIndex: [0, 0, 0],
				cityId: 0,
				theme: app.globalData.theme,
				showLoading: false,
				display: false,
				addressInfo: [],
				addressData: []
			};
		},
		computed: {
			...mapGetters(['isLogin']),
			addressText() {
				return this.addressInfo.map(v => v.regionName).join('/');
			}
		},
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.getUserAddress();
					}
				},
				deep: true
			}
		},
		onLoad(options) {
			if (this.isLogin) {
				this.id = options.id || 0;
				// 支持两种参数：isSelect 和 type=select
				this.isSelect = options.isSelect || options.type === 'select' || false;
				uni.setNavigationBarTitle({
					title: options.id ? '修改地址' : '添加地址'
				})
				this.getUserAddress();
			} else {
				toLogin();
			}
		},
		methods: {
			// 处理地址选择逻辑（当处于选择模式时）
			handleAddressSelect: function(addressData) {
				if (!this.isSelect) return;
				
				// 构造标准化的地址数据
				const formattedAddress = {
					id: addressData.id,
					name: addressData.realName,
					phone: addressData.phone,
					province: addressData.province,
					city: addressData.city,
					district: addressData.district,
					detail: addressData.detail,
					isDefault: addressData.isDefault
				};
				
				// 1. 保存到本地缓存
				uni.setStorageSync('selectedAddress', formattedAddress);
				
				// 2. 发出全局事件（兼容旧版本）
				uni.$emit('checkaddress', addressData);
			},
			deleteAddress: function() {
				if (!this.id) return;
				uni.showModal({
					title: '提示',
					content: '确定删除该地址？',
					success: (res) => {
						if (res.confirm) {
							delAddress(this.id).then(() => {
								this.$util.Tips({
									title: "删除成功",
									icon: 'success'
								});
								setTimeout(() => {
									uni.navigateBack();
								}, 1000);
							}).catch(err => {
								this.$util.Tips({
									title: err
								});
							});
						}
					}
				});
			},
			// 关闭地址弹窗；
			changeClose: function() {
				this.display = false;
			},
			OnChangeAddress(address) {
				this.addressInfo = address;
			},
			changeRegion() {
				this.display = true;
			},
			getUserAddress: function() {
				if (!this.id) return false;
				let that = this;
				getAddressDetail(this.id).then(res => {
					if (res.data) {
						that.$set(that, 'userAddress', res.data);
						// 构建地址信息数组，处理可能缺少街道信息的情况
						that.addressInfo = [{
							regionName: res.data.province
						}, {
							regionName: res.data.city,
							regionId: res.data.cityId
						}, {
							regionName: res.data.district,
							regionId: res.data.districtId
						}];

						// 只有当街道信息存在时才添加到数组中
						if (res.data.street) {
							that.addressInfo.push({
								regionName: res.data.street
							});
						}
					}
				});
			},
			toggleTab(str) {
				this.$refs[str].show();
			},
			onConfirm(val) {
				this.region = val.checkArr[0] + '-' + val.checkArr[1] + '-' + val.checkArr[2];
			},

			chooseLocation: function() {
				uni.chooseLocation({
					success: (res) => {
						this.$set(this.userAddress, 'detail', res.name);
					}
				})
			},
			/**
			 * 导入微信地址保存
			 */
			addressWxImportAdd(res) {
				addressWxImportApi({
					userName: res.userName,
					telNumber: res.telNumber,
					countryName: res.countryName || res.countyName,
					provinceName: res.provinceName,
					nationalCode: res.nationalCode,
					cityName: res.cityName,
					streetName: res.streetName || res.addressStreetFourthStageName,
					detail: res.detailInfo
				}).then(r => {
					this.$util.Tips({
						title: "添加成功",
						icon: 'success'
					});
					setTimeout(() => {
						return uni.navigateBack({
							delta: 1,
						})
					}, 1000)
				}).catch(err => {
					this.$util.Tips({
						title: err
					});
				});
			},
			// 导入共享地址（小程序）
			getWxAddress: function() {
				let that = this;
				that.$util.addressWxImport().then(res=>{
					that.addressWxImportAdd(res);
				});
			},
			// 导入共享地址（微信）；
			getAddress() {
				let that = this;
				that.$wechat.openAddress().then(userInfo => {
					that.addressWxImportAdd(userInfo);
				});
			},
			/**
			 * 提交用户添加地址
			 * 
			 */
			formSubmit: Debounce(function(e) {
				let that = this,
					value = e.detail.value;
				if (!value.realName) return that.$util.Tips({
					title: '请填写收货人姓名'
				});
				if (!value.phone) return that.$util.Tips({
					title: '请填写收货人手机号'
				});
				if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(value.phone)) return that.$util.Tips({
					title: '请输入正确的手机号码'
				});
				if (!that.addressInfo.length) return that.$util.Tips({
					title: '请选择所在地区'
				});
				if (!value.detail) return that.$util.Tips({
					title: '请填写详细地址'
				});

				value.id = that.id;
				value.province = that.addressInfo[0].regionName;
				value.city = that.addressInfo[1].regionName;
				value.cityId = that.addressInfo[1].regionId;
				value.district = that.addressInfo[2].regionName;
				value.districtId = that.addressInfo[2].regionId;
				// 处理街道信息，部分地区可能只有3级选择
				// 接口要求street字段不能为空，没有街道信息时传空字符串
				value.street = that.addressInfo[3] ? that.addressInfo[3].regionName : '';
				value.isDefault = that.userAddress.isDefault;

				uni.showLoading({
					title: '保存中',
					mask: true
				})
				if (that.id) {
					editAddress(value).then(res => {
						that.$util.Tips({
							title: '修改成功',
							icon: 'success'
						});
						
						// 如果是选择模式，处理地址选择逻辑
						if (that.isSelect) {
							that.handleAddressSelect(value);
						}
						
						setTimeout(function() {
							return uni.navigateBack({
								delta: 1,
							})
						}, 1000);
					}).catch(err => {
						return that.$util.Tips({
							title: err
						});
					})
				} else {
					addAddress(value).then(res => {
						that.$util.Tips({
							title: '添加成功',
							icon: 'success'
						});
						
						// 如果是选择模式，处理地址选择逻辑
						if (that.isSelect) {
							// 使用返回的地址数据（包含新生成的ID）
							const newAddress = res.data || { ...value, id: res.id };
							that.handleAddressSelect(newAddress);
						}
						
						setTimeout(function() {
							return uni.navigateBack({
								delta: 1,
							})
						}, 1000);
					}).catch(err => {
						return that.$util.Tips({
							title: err
						});
					})
				}

			}),
			ChangeIsDefault: function(e) {
				this.$set(this.userAddress, 'isDefault', !this.userAddress.isDefault);
			}
		}
	}
</script>

<style scoped lang="scss">
	.bg-fixed {
		width: 100%;
		height: 750rpx;
		position: absolute;
		top: 0;
	}

	.addAddress {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 200rpx;
	}

	.bg_color {
		@include main_bg_color(theme);
	}

	.addAddress .info-container {
		background-color: #fff;
		padding: 0 30rpx;
		margin-bottom: 20rpx;
		border-radius: 14rpx;
		
		input {
			height: 40rpx;
		}
	}


	.addAddress .list .item {
		border-top: 1rpx solid #f5f5f5;
}
	.addAddress .info-container .item {

		height: 90rpx;
		line-height: 90rpx;
	}

	.addAddress .info-container .item:first-child {
		border-top: none;
	}

	.addAddress .info-container .item .name {
		width: 160rpx;
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}

	.addAddress .info-container .item .address {
		flex: 1;
		font-size: 30rpx;
		font-weight: 400;
	}

	.addAddress .info-container .item input {
		flex: 1;
		font-size: 30rpx;
		font-weight: 400;
		text-align: right;
	}

	.addAddress .info-container .item .placeholder {
		color: #ccc;
	}

	.addAddress .info-container .item picker .picker {
		width: 410rpx;
		font-size: 30rpx;
	}

	.addAddress .default-container {
		background-color: #fff;
		padding: 30rpx;
		margin-bottom: 40rpx;
		border-radius: 14rpx;
	}

	.addAddress .default-container .acea-row {
		height: auto;
		line-height: normal;
		align-items: center;
	}

	.addAddress .default-container .default-text {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}

	.addAddress .default-container .default-tip {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
		line-height: 1.4;
	}

	.addAddress .wechatAddress {
		width: 690rpx;
		height: 86rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 86rpx;
		margin: 40rpx auto 0;
		font-size: 32rpx;
		@include main_color(theme);
		@include coupons_border_color(theme);
	}

	.font_color {
		color: #BDFD5B!important;
	}

	.relative {
		position: relative;
	}

	.icon-dizhi {
		font-size: 44rpx;
		z-index: 100;
		color: #4CAF50;
	}
	
	.abs_text {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		height: 90rpx;
		line-height: 90rpx;
		text-overflow: ellipsis;
		overflow: hidden;
		color: #333;
		padding-right: 60rpx;
	}
	
	.abs_right {
		position: absolute;
		top: 0;
		right: 0;
	}

	.region_count {
		width: 100%;
		position: relative;
		text-align: right;
	}

	/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {
		@include main_bg_color(theme);
		@include coupons_border_color(theme);
		color: #fff !important
	}

	/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
		@include main_bg_color(theme);
		@include coupons_border_color(theme);
		color: #fff !important;
		margin-right: 0 !important;
	}

	.page-footer {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #ffffff;
		padding: 20rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		z-index: 100;
	}
	
	.button-wrapper {
		background-color: #ffffff;
		border-radius: 14rpx;
		padding: 20rpx;
		display: flex;
		gap: 20rpx;
	}
	
	.action-btn {
		flex: 1;
		height: 86rpx;
		line-height: 86rpx;
		border-radius: 15rpx;
		font-size: 32rpx;
		border: none;
		margin: 0;
		padding: 0;
	}
	
	.save-btn {
		background: #BDFD5B;
		color: #222222;
	}
	
	.save-btn.full-width {
		flex: auto;
		width: 100%;
	}
	
	.delete-btn {
		background: #f5f5f5;
		color: #333333;
	}
</style>
