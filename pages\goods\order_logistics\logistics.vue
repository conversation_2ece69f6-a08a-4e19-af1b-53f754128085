<template>
	<view class="logistics-page">
		<view class="logistics-header">
			<view class="header-item">
				<text class="label">承运公司：</text>
				<text class="value">顺丰速运</text>
			</view>
			<view class="header-item">
				<text class="label">运单编号：</text>
				<text class="value">SF231365092401</text>
			</view>
			<view class="header-item">
				<text class="label">预计送达：</text>
				<text class="value">2025.05.25</text>
			</view>
		</view>
		
		<view class="logistics-timeline">
			<view class="timeline-item" v-for="(item, index) in logisticsData" :key="index" :class="{ 'current': item.isCurrent, 'completed': item.isCompleted }">
				<view class="timeline-left">
					<view class="timeline-dot" :class="{ 'current': item.isCurrent, 'completed': item.isCompleted }">
						<view class="dot-inner" v-if="item.isCurrent"></view>
					</view>
					<view class="timeline-line" v-if="index < logisticsData.length - 1"></view>
				</view>
				<view class="timeline-content">
					<view class="timeline-title" :class="{ 'current': item.isCurrent }">{{ item.title }}</view>
					<view class="timeline-desc" v-if="item.description">{{ item.description }}</view>
					<view class="timeline-time" v-if="item.time">{{ item.time }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderNo: '',
				logisticsData: [
					{
						title: '已放入快递柜或驿站',
						isCompleted: false,
						isCurrent: false
					},
					{
						title: '派件中',
						description: '小店区顺丰分部张伟[16603405206]正在派件',
						time: '02-13 11:30',
						isCompleted: false,
						isCurrent: true
					},
					{
						title: '快件到达太原市',
						time: '02-13 11:30',
						isCompleted: true,
						isCurrent: false
					},
					{
						title: '快递中转',
						description: '离开【郑州中转站】下一站【太原中转站】',
						time: '02-13 11:30',
						isCompleted: true,
						isCurrent: false
					},
					{
						title: '已揽收',
						description: '快件已揽收',
						time: '02-13 11:30',
						isCompleted: true,
						isCurrent: false
					},
					{
						title: '已发货',
						time: '02-13 11:30',
						isCompleted: true,
						isCurrent: false
					}
				]
			};
		},
		onLoad(options) {
			if (options.orderNo) {
				this.orderNo = options.orderNo;
			}
		}
	}
</script>

<style scoped lang="scss">
	.logistics-page {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 0 30rpx 30rpx;
	}

	.logistics-header {
		background-color: #fff;
		padding: 40rpx 30rpx;
		margin-bottom: 20rpx;
		border-radius: 0;

		.header-item {
			display: flex;
			margin-bottom: 20rpx;
			font-size: 32rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.label {
				color: #666;
				min-width: 140rpx;
			}

			.value {
				color: #333;
				flex: 1;
			}
		}
	}

	.logistics-timeline {
		background-color: #fff;
		padding: 30rpx;

		.timeline-item {
			display: flex;
			margin-bottom: 40rpx;

			&:last-child {
				margin-bottom: 0;

				.timeline-line {
					display: none;
				}
			}

			.timeline-left {
				position: relative;
				margin-right: 30rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				.timeline-dot {
					width: 24rpx;
					height: 24rpx;
					border-radius: 50%;
					background-color: #ddd;
					border: 4rpx solid #ddd;
					position: relative;
					z-index: 2;

					&.completed {
						background-color: #ddd;
						border-color: #ddd;
					}

					&.current {
						background-color: #fff;
						border-color: #52c41a;
						width: 32rpx;
						height: 32rpx;

						.dot-inner {
							width: 12rpx;
							height: 12rpx;
							border-radius: 50%;
							background-color: #52c41a;
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
						}
					}
				}

				.timeline-line {
					width: 2rpx;
					background-color: #eee;
					flex: 1;
					margin-top: 10rpx;
				}
			}

			.timeline-content {
				flex: 1;
				padding-top: 0;

				.timeline-title {
					font-size: 32rpx;
					color: #999;
					margin-bottom: 8rpx;
					font-weight: normal;

					&.current {
						color: #333;
						font-weight: 500;
					}
				}

				.timeline-desc {
					font-size: 28rpx;
					color: #666;
					line-height: 1.4;
					margin-bottom: 8rpx;
				}

				.timeline-time {
					font-size: 28rpx;
					color: #999;
				}
			}

			&.current {
				.timeline-content {
					.timeline-title {
						color: #333;
					}
				}
			}
		}
	}
</style> 