<template>
	<!-- :data-theme="theme" -->
	<view >
		<view class="ChangePassword">
			<view class="list borRadius14 ">
				<block v-if="isNew">
					<view class="item">
						<view class="item-lable">
							手机号
						</view>
						<input disabled='true' :value="userInfo.phone" ></input>
					</view>
					<view class="item acea-row row-between-wrapper">
						<view class="item-lable">
							验证码
						</view>
						<input type='number' placeholder='填写验证码' placeholder-class='placeholder' class="codeIput"
							v-model="captcha" maxlength="6"></input>
						<button class="code" :class="disabled === true ? 'on' : ''" :disabled='disabled' @click="code" v-if="captcha == ''" style="margin-left: 12rpx;">
							{{ text }}
						</button>
					</view>
				</block>
				<block v-else>
					<view class="item" >
						<view class="item-lable">
							新手机号
						</view>
						<input type='number' placeholder='请输入新手机号' placeholder-class='placeholder' v-model="phone"
							maxlength="11"></input>
					</view>
					<view class="item acea-row row-between-wrapper" style="border-bottom:0rpx;">
						<view class="item-lable">
							验证码
						</view>
						<input type='number' placeholder='填写验证码' placeholder-class='placeholder' class="codeIput"
							v-model="bindingCaptcha" maxlength="6"></input>
						<button class="code" :class="disabled === true ? 'on' : ''" :disabled='disabled' @click="code" v-if="bindingCaptcha == ''">
							{{ text }}
						</button>
					</view>
				</block>
			</view>
			
			<button form-type="submit" v-if="isNew" class="confirmBnt bg_color" @click="next">下一步</button>
			<button form-type="submit" v-if="!isNew" class="confirmBnt bg_color" @click="editPwd">保存</button>
		</view>
		<Verify @success="success" :captchaType="'blockPuzzle'" :imgSize="{ width: '330px', height: '155px' }"
			ref="verify"></Verify>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import sendVerifyCode from "@/mixins/SendVerifyCode";
	import {
		registerVerify,
		bindingPhone,
		verifyCode,
		bindingVerify,
		bindingPhoneCode
	} from '@/api/api.js';
	import {
		toLogin
	} from '@/libs/login.js';
	import {
		mapGetters
	} from "vuex";
	import {
		setThemeColor
	} from '@/utils/setTheme.js'
	import {
		Debounce
	} from '@/utils/validate.js'
	import Verify from '../components/verifition/verify.vue';
	const app = getApp();
	export default {
		components: {
			Verify
		},
		mixins: [sendVerifyCode],
		data() {
			return {
				phone: '',
				captcha: '',
				isAuto: false, //没有授权的不会自动授权
				isShowAuth: false, //是否隐藏授权
				key: '',
				isNew: true,
				timer: '',
				text: '获取验证码',
				nums: 60,
				theme: app.globalData.theme,
				bgColor: '',
				bindingCaptcha: ''
			};
		},
		mounted() {
			// this.timer = setInterval(this.getTimes, 1000);
		},
		computed: mapGetters(['isLogin', 'userInfo']),
		onLoad() {
			let that = this;
			that.bgColor = setThemeColor();
			// uni.setNavigationBarColor({
			// 	frontColor: '#ffffff',
			// 	backgroundColor: that.bgColor,
			// });
			if (this.isLogin) {
				// verifyCode().then(res=>{
				// 	this.$set(this, 'key', res.data.key)
				// });
			} else {
				toLogin();
			}
		},
		methods: {
			getTimes() {
				this.nums = this.nums - 1;
				// this.text = "剩余 " + this.nums + "s";
				this.text = "" + this.nums + "s";
				if (this.nums < 0) {
					clearInterval(this.timer);
				}
				// this.text = "剩余 " + this.nums + "s";
				this.text = "" + this.nums + "s";
				// if (this.text < "剩余 " + 0 + "s") {
					if (this.text < "" + 0 + "s") {
					this.disabled = false;
					this.text = "重新获取";
				}
			},
			onLoadFun: function() {},
			// 授权关闭
			authColse: function(e) {
				this.isShowAuth = e
			},
			next() {
				if (!this.captcha) return this.$util.Tips({
					title: '请填写验证码'
				});
				this.isNew = false;
				clearInterval(this.timer);
				this.disabled = false;
				this.text = "获取验证码";
			},
			editPwd: Debounce(function(e) {
				let that = this;
				if (!that.phone) return that.$util.Tips({
					title: '请填写手机号码！'
				});
				if (!(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))) return that.$util.Tips({
					title: '请输入正确的手机号码！'
				});
				if (!that.bindingCaptcha) return that.$util.Tips({
					title: '请填写验证码'
				});
				uni.showModal({
					title: '是否更换绑定账号',
					// #ifdef H5
					title: '是否更换绑定账号,更换之后需重新登录',
					// #endif
					confirmText: '绑定',
					success(res) {
						uni.showLoading({
							title: '加载中',
							mask: true
						});
						if (res.confirm) {
							bindingPhone({
								phone: that.phone,
								captcha: that.bindingCaptcha
							}).then(res => {
								uni.hideLoading();
								// #ifdef H5
								return that.$store.dispatch('LOGOUT');
								// #endif
								// return that.$util.Tips({
								// 	title: res.message,
								// 	icon: 'success'
								// }, {
								// 	tab: 5,
								// 	url: '/pages/users/user_info/index'
								// });
							}).catch(err => {
								uni.hideLoading();
								return that.$util.Tips({
									title: err
								});
							})
						} else if (res.cancel) {
							return that.$util.Tips({
								title: '您已取消更换绑定！'
							}, {
								tab: 5,
								url: '/pages/users/user_info/index'
							});
						}
					}
				});
			}),
			success(data) {
				this.$refs.verify.hide();
				this.codeSend();
			},
			codeSend() {
				let that = this;
				bindingPhoneCode({
					phone: that.phone,
					captcha: that.captcha
				}).then(res => {
					that.$util.Tips({
						title: res.message
					});
				
					that.timer = setInterval(that.getTimes, 1000);
					that.disabled = true;
					uni.hideLoading();
				}).catch(err => {
					return that.$util.Tips({
						title: err
					});
					uni.hideLoading();
				});
			},
			/**
			 * 发送验证码
			 * 
			 */
			code: Debounce(function() {
				this.nums = 60;
				uni.showLoading({
					title: '加载中',
					mask: true
				});
				let that = this;
				if (that.isNew) {
					registerVerify().then(res => {
						that.$util.Tips({
							title: res.message
						});
						that.timer = setInterval(that.getTimes, 1000);
						that.disabled = true;
						uni.hideLoading();
					}).catch(err => {
						return that.$util.Tips({
							title: err
						});
						uni.hideLoading();
					});
				} else {
					if (!that.phone) return that.$util.Tips({
						title: '请填写手机号码！'
					});
					if (!(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))) return that.$util.Tips({
						title: '请输入正确的手机号码！'
					});
					that.$refs.verify.show();
				}

			})
		}
	}
</script>

<style lang="scss" scoped>
	.shading {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;

		/* #ifdef APP-VUE */
		margin-top: 50rpx;
		/* #endif */
		/* #ifndef APP-VUE */

		margin-top: 200rpx;
		/* #endif */


		image {
			width: 180rpx;
			height: 180rpx;
		}
	}

	page {
		background-color: #fff !important;
	}

	.ChangePassword {
		// padding-top: 100rpx;
		padding: 0 24rpx;
		
	}

	.ChangePassword .phone {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		margin-top: 55rpx;
	}

	.ChangePassword .list {
		// width: 580rpx;
		// margin: 0 auto;
		margin-top: 30rpx;
		padding: 24rpx;
		    background-color: #fff;
			// border-radius: 14rpx;
	}

	.ChangePassword .list .item {
		width: 100%;
		padding: 26rpx 0;
		// height: 110rpx;
		border-bottom: 2rpx solid #f0f0f0;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.ChangePassword .list .item input {
		// width: 100%;
		flex: 1;
		text-align: right;
		height: 100%;
		font-size: 32rpx;
	}

	.ChangePassword .list .item .placeholder {
		color: #b9b9bc;
	}

	.ChangePassword .list .item input.codeIput {
		width: 340rpx;
	}

	.ChangePassword .list .item .code {
		font-size: 32rpx;
		@include main_color(theme);
		background-color: #fff;
		color: #FF2222;
	}

	.ChangePassword .list .item .code.on {
		color: #b9b9bc !important;
	}

	.ChangePassword .confirmBnt {
		width: 600rpx;
		height: 88rpx;
		background: #bdfd5b;
		border-radius: 16rpx;
		font-size: 32rpx;
		// width: 580rpx;
		// height: 90rpx;
		// border-radius: 45rpx;
		color: #fff;
		margin: 100rpx auto 0 auto;
		text-align: center;
		line-height: 88rpx;
		font-weight: 600;
		text-align: center;
		color: #222222;
	}

	.bg_color {
		@include main_bg_color(theme);
	}
	.item-lable{
		font-size: 30rpx;
		
		font-weight: 600;
		text-align: left;
		color: #222222;
		
	}
</style>
