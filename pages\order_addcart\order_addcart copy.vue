<template>
	<view :data-theme="theme">
		<view class="cart_nav" :style='"height:"+navH+"rpx;"'>
			<view class='navbarCon acea-row'>
				<!-- #ifdef MP -->
				<view class="select_nav flex justify-center align-center" id="home" :style="{ top: homeTop + 'rpx' }">
					<text class="iconfont icon-fanhui2 px-20" @tap="returns"></text>
					<text class="iconfont icon-gengduo5 px-20" @tap.stop="showNav"></text>
					<text class="nav_line"></text>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<view id="home" class="home acea-row row-center-wrapper iconfont icon-shouye4 h5_back"
					:style="{ top: homeTop + 'rpx' }" @tap="returns">
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 || APP-PLUS -->
				<view class="nav_title" :style="{ top: homeTop + 'rpx' }">购物车</view>
				<!-- #endif -->
				<!-- #ifdef H5 || APP-PLUS -->
				<view class="right_select" :style="{ top: homeTop + 'rpx' }" @tap="showNav">
					<text class="iconfont icon-gengduo2"></text>
				</view>
				<!-- #endif -->
			</view>
		</view>
		<view class="dialog_nav" :style='"top:"+navH+"rpx;"' v-show="currentPage">
			<view class="dialog_nav_item" v-for="(item,index) in selectNavList" :key="index"
				@click="linkPage(item.url)">
				<text class="iconfont" :class="item.icon"></text>
				<text class="pl-20">{{item.name}}</text>
			</view>
		</view>
		<view class='shoppingCart copy-data' :style='"top:"+navH+"rpx;"' @touchstart="touchStart">
			<view class='labelNav'>
				<view
					v-if="(cartList.valid.length === 0 && cartList.invalid.length === 0) || (cartList.valid.length > 0)"
					class='acea-row row-between-wrapper'>
					<view>共 <text class='num'>{{cartCount}}</text>件商品</view>
					<view v-if="cartList.valid.length > 0 || cartList.invalid.length > 0"
						class='administrate acea-row row-center-wrapper' @click='manage'>{{ footerswitch ? '管理' : '取消'}}
					</view>
				</view>
			</view>

			<!-- 公告模块 -->
			<view class="notice-module" v-if="noticeText">
				<view class="notice-content acea-row row-middle">
					<text class="iconfont icon-laba notice-icon"></text>
					<view class="notice-text">{{noticeText}}</view>
				</view>
			</view>

			<!-- 优惠券模块 -->
			<view class="coupon-module" v-if="couponList.length > 0">
				<view class="coupon-content acea-row row-between-wrapper">
					<view class="coupon-left acea-row row-middle">
						<text class="coupon-label">优惠券</text>
						<view class="coupon-list acea-row">
							<view
								v-for="(coupon, index) in couponList.slice(0, 2)"
								:key="index"
								class="coupon-item"
								:class="[coupon.type, coupon.isReceived ? 'received' : '']"
								@click="receiveCoupon(coupon)"
							>
								{{coupon.text}}
							</view>
						</view>
					</view>
					<view class="coupon-right acea-row row-middle" @click="goToCouponCenter">
						<text class="more-text">领券</text>
						<text class="iconfont icon-jiantou more-arrow"></text>
					</view>
				</view>
			</view>

			<view class="fixheight"></view>
			<view class="borRadius14 cartBox">
				<view v-if="cartList.valid.length > 0 || cartList.invalid.length > 0" class="borderPad">
					<view class='list'>
						<view v-for="(itemn,index) in cartList.valid" :key="index" class="mb24 borRadius14"
							style="overflow: hidden;">
							<view class="store-title nav">
								<view class="checkbox" @click="storeAllCheck(itemn,index)">
									<text v-if="!itemn.allCheck" class="iconfont icon-weixuan"></text>
									<text v-else class="iconfont icon-xuanzhong11"></text>
								</view>
								<navigator :url="'/pages/merchant/home/<USER>'+itemn.merId " class="info acea-row">
									<text class="iconfont icon-shangjiadingdan"></text>
									<view class="name">{{itemn.merName}}</view>
									<text class="iconfont icon-jiantou"></text>
								</navigator>
							</view>
							<block v-for="(item,indexn) in itemn.cartInfoList" :key="indexn">
								<view class='item acea-row row-between-wrapper'>
									<view class="checkbox" @click.stop="goodsCheck(item,indexn)">
										<block v-if="!footerswitch">
											<text v-if="!item.check" class="iconfont icon-weixuan"></text>
											<text v-else class="iconfont icon-xuanzhong11"></text>
										</block>
										<block v-else>
											<text v-if="!item.check && item.stock>0"
												class="iconfont icon-weixuan"></text>
											<text v-if="item.check && item.stock>0"
												class="iconfont icon-xuanzhong11"></text>
											<view v-if="!item.check && (item.stock===0 || !item.stock)" class="noCheck">
											</view>
										</block>

									</view>
									<navigator :url='"/pages/goods/goods_details/index?id="+item.productId' hover-class='none'
										class='picTxt acea-row row-between-wrapper'>
										<view class='pictrue'>
											<easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
										</view>
										<view class='text'>
											<view class='line1' :class="item.attrStatus?'':'reColor'">{{item.proName}}
											</view>
											<view class='infor line1' v-if="item.sku">规格：{{item.sku}}</view>
											<view class='money mt-28' v-if="item.attrStatus">
												<text v-if="item.customData && item.customData.price > 0">
													￥{{item.vipPrice ? item.vipPrice : item.price | priceAdd(item.customData.price)}}
												</text>
												<text v-else>
													￥{{item.vipPrice ? item.vipPrice : item.price}}
												</text>
											</view>
											<view class="reElection acea-row row-between-wrapper" v-else>
												<view class="title">请重新选择商品规格</view>
												<view class="reBnt cart-color acea-row row-center-wrapper"
													@click.stop="reElection(item)">重选</view>
											</view>
										</view>
										<glassesProductCustomInfo v-if="item.customData && (item.customData.lens || item.customData.optometry)" :customData="item.customData"></glassesProductCustomInfo>
										<view class='carnum acea-row row-center-wrapper' v-if="item.attrStatus">
											<view class="reduce" :class="item.numSub ? 'on' : ''"
												@click.stop='subCart(item)'>-</view>
											<view class='num'>{{item.cartNum}}</view>
											<view class="plus" :class="item.numAdd ? 'on' : ''"
												@click.stop='addCart(item)'>+</view>
										</view>
									</navigator>
								</view>
							</block>
						</view>
					</view>
					<view v-if="cartList.invalid.length > 0" class='invalidGoods borRadius14 mb24'
						:style="cartList.valid.length===0 && cartList.invalid.length > 0 ? 'position: relative;z-index: 111;':'position: static;'">
						<view class='goodsNav acea-row row-between-wrapper'>
							<view v-if="cartList.invalid.length > 1 || cartList.valid.length > 0" @click='goodsOpen'>
								<text class='iconfont'
									:class='goodsHidden==true?"icon-xiangxia":"icon-xiangshang"'></text>失效商品
							</view>
							<view v-else>
								失效商品
							</view>
							<view class='del' @click='unsetCart'><text class='iconfont icon-shanchu1'></text>清空</view>
						</view>
						<view class='goodsList' :hidden='goodsHidden'>
							<block v-for="(itemn,indexn) in cartList.invalid" :key='indexn'>
								<view v-for="(item,index) in itemn.cartInfoList" :key='index'>
									<view class='item acea-row row-between-wrapper'>
										<view class='invalid'>{{$t(`page.goodsAddcart.failure`)}}</view>
										<view class='picTxt acea-row row-between-wrapper'>
											<view class='pictrue'>
												<easy-loadimage mode="widthFix" :image-src="item.image">
												</easy-loadimage>
											</view>
											<view class='text acea-row row-column-between'>
												<view class='line1 name'>{{item.proName}}</view>
												<view class='infor line1' v-if="item.sku">
													{{$t(`page.goodsAddcart.attribute`)}}：{{item.sku}}</view>
												<view class='acea-row row-between-wrapper'>
													<view class='end'>{{$t(`page.goodsAddcart.fallDesc`)}}</view>
												</view>
											</view>
										</view>
									</view>
								</view>
							</block>
						</view>
					</view>
					<view class='loadingicon acea-row row-center-wrapper'>
						<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
					</view>
				</view>
				<view class='noCart noCommodity'
					v-if="(cartList.valid.length == 0 && cartList.invalid.length == 0 && !loading && canShow) || !isLogin">
					<view class='pictrue'>
						<image src='@/static/images/noShopper.png'></image>
					</view>
					<text class="text">暂无商品~</text>
					<recommend></recommend>
				</view>
				<view v-if="cartList.valid.length > 0 || cartList.invalid.length > 0" style='height: 120rpx;'></view>
			</view>
		</view>
		<view class='footer acea-row row-between-wrapper' v-if="cartList.valid.length > 0">
			<view>
				<view class="allcheckbox" @click.stop="checkboxAllChange">
					<text v-if="!isAllSelect" class="iconfont icon-weixuan"></text>
					<text v-else class="iconfont icon-xuanzhong11"></text>
					全选 ({{cartCount}})
				</view>
			</view>
			<view class='money acea-row row-middle' v-if="footerswitch==true">
				<text class='price-color'>￥{{selectCountPrice}}</text>
				<form @submit="subOrder" report-submit='true'>
					<button class='placeOrder bg_color' formType="submit">立即下单</button>
				</form>
			</view>
			<view class='button acea-row row-middle' v-else>
				<form @submit="subCollect" report-submit='true'>
					<button class='btn_cart_color' formType="submit">收藏</button>
				</form>
				<form @submit="subDel" report-submit='true'>
					<button class='bnt' formType="submit">删除</button>
				</form>
			</view>
		</view>
		<productWindow :attr="attr" :isShow='1' :iSplus='1' :iScart='1' @myevent="onMyEvent" @ChangeAttr="ChangeAttr"
			@ChangeCartNum="ChangeCartNum" @attrVal="attrVal" @iptCartNum="iptCartNum" @goCat="reGoCat"
			id='product-window'></productWindow>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import recommend from "@/components/base/recommend.vue";
	import easyLoadimage from '@/components/base/easy-loadimage.vue';
	// #ifdef APP-PLUS
	let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
	// #endif
	// #ifndef APP-PLUS
	let sysHeight = 0
	// #endif
	import {
		getCartList,
		getCartCounts,
		changeCartNum,
		cartDel,
		getResetCart,
		getCartNoticeList,
		getCouponList
	} from '@/api/order.js';
	import {
		getProductHot,
		cartToCollect,
		getProductDetail
	} from '@/api/product.js';
	import {
		getShare
	} from '@/api/public.js';
	import {
		mapGetters
	} from "vuex";
	import productWindow from '@/components/productWindow';
	import glassesProductCustomInfo from '@/components/glassesProductCustomInfo';
	import {
		Debounce
	} from '@/utils/validate.js'
	import animationType from '@/utils/animationType.js'
	let app = getApp();
	export default {
		components: {
			productWindow,
			glassesProductCustomInfo,
			recommend,
			easyLoadimage
		},
		data() {
			return {
				categoryId: 0,
				cartCount: 0,
				goodsHidden: false,
				footerswitch: true,
				hostProduct: [],
				cartList: {
					valid: [],
					invalid: []
				},
				// 公告数据
				noticeList: [],
				noticeText: '',
				noticePage: 1,
				noticeLimit: 10,
				noticeLoading: false,
				// 优惠券数据
				couponList: [],
				couponPage: 1,
				couponLimit: 10,
				couponLoading: false,
				isAllSelect: false, //全选
				selectValue: [], //选中的数据
				selectCountPrice: 0.00,
				isAuto: false, //没有授权的不会自动授权
				isShowAuth: false, //是否隐藏授权
				hotScroll: false,
				hotPage: 1,
				hotLimit: 10,
				loading: false,
				loadend: false,
				loadTitle: '', //提示语
				page: 1,
				limit: 20,
				loadingInvalid: false,
				loadendInvalid: false,
				loadTitleInvalid: '', //提示语
				limitInvalid: 20,
				attr: {
					cartAttr: false,
					productAttr: [],
					productSelect: {}
				},
				productValue: [], //系统属性
				productInfo: {},
				attrValue: '', //已选属性
				attrTxt: '请选择', //属性页面提示
				cartId: 0,
				product_id: 0,
				sysHeight: sysHeight,
				canShow: false,
				configApi: {}, //分享类容配置
				theme: app.globalData.theme,
				navH: "",
				homeTop: 20,
				currentPage: false,
				type: "", //视频号普通商品类型
				selectNavList: [{
						name: '首页',
						icon: 'icon-shouye8',
						url: '/pages/index/index'
					},
					{
						name: '搜索',
						icon: 'icon-sousuo6',
						url: '/pages/goods/goods_search/index'
					},
					{
						name: '我的收藏',
						icon: 'icon-shoucang3',
						url: '/pages/users/user_goods_collection/index'
					},
					{
						name: '个人中心',
						icon: 'icon-gerenzhongxin1',
						url: '/pages/user/index'
					},
				]
			};
		},

		computed: mapGetters(['isLogin', 'productType', 'globalData']),
		onLoad: function(options) {
			let that = this;
			// #ifdef MP
			that.navH = this.globalData.navHeight;
			// #endif
			// #ifndef MP
			that.navH = 96;
			// #endif
			// #ifdef H5
			//that.shareApi();
			// #endif
		},
		onReady() {
			this.$nextTick(function() {
				// #ifdef MP
				const menuButton = uni.getMenuButtonBoundingClientRect();
				const query = uni.createSelectorQuery().in(this);
				query
					.select('#home')
					.boundingClientRect(data => {
						this.homeTop = menuButton.top * 2 + menuButton.height - data.height;
					})
					.exec();
				// #endif
			});
		},
		onShow: function() {
			this.canShow = false
			if (this.isLogin == true) {
				this.hotPage = 1;
				this.hostProduct = [],
					this.hotScroll = false,
					this.loadend = false;
				this.page = 1;
				this.cartList.valid = [];
				this.getCartList();
				this.loadendInvalid = false;
				this.cartList.invalid = [];
				this.getInvalidList();
				//this.getCartNum();
				this.footerswitch = true;
				this.hotScroll = false;
				this.hotPage = 1;
				this.hotLimit = 10;
				this.cartList = {
						valid: [],
						invalid: []
					},
					this.isAllSelect = false; //全选
				this.selectValue = []; //选中的数据
				this.selectCountPrice = 0.00;
				this.cartCount = 0;
				this.isShowAuth = false;
			};
			// 获取购物车公告列表
			this.getCartNoticeList();
			// 获取优惠券列表
			this.getCouponList();
			uni.showTabBar();
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		},
		methods: {
			// 授权关闭
			authColse: function(e) {
				this.isShowAuth = e;
			},
			// 领取优惠券
			receiveCoupon: function(coupon) {
				let that = this;

				// 检查是否已领取
				if (coupon.isReceived) {
					that.$util.Tips({
						title: '已领取过该优惠券',
						icon: 'none'
					});
					return;
				}

				// 检查是否限量且已领完
				if (coupon.isLimited && coupon.lastTotal <= 0) {
					that.$util.Tips({
						title: '优惠券已领完',
						icon: 'none'
					});
					return;
				}

				console.log('领取优惠券:', coupon);
				// TODO: 调用领取优惠券接口
				// receiveCouponApi(coupon.id).then(res => {
				//     if (res.code === 0) {
				//         that.$util.Tips({
				//             title: '领取成功',
				//             icon: 'success'
				//         });
				//         // 更新优惠券状态
				//         coupon.isReceived = true;
				//         // 重新获取优惠券列表
				//         that.getCouponList();
				//     } else {
				//         that.$util.Tips({
				//             title: res.message || '领取失败',
				//             icon: 'none'
				//         });
				//     }
				// }).catch(err => {
				//     that.$util.Tips({
				//         title: '领取失败',
				//         icon: 'none'
				//     });
				// });

				// 临时模拟领取成功
				that.$util.Tips({
					title: '领取成功',
					icon: 'success'
				});
				coupon.isReceived = true;
			},
			// 跳转到优惠券中心
			goToCouponCenter: function() {
				console.log('跳转到优惠券中心');
				uni.navigateTo({
					url: '/pages/coupon/coupon_center/index'
				});
			},
			// 获取购物车公告列表
			getCartNoticeList: function() {
				let that = this;
				if (that.noticeLoading) return;

				that.noticeLoading = true;
				let data = {
					page: that.noticePage,
					limit: that.noticeLimit
				};

				getCartNoticeList(data).then(res => {
					that.noticeLoading = false;
					if (res.code === 200 && res.data && res.data.list) {
						let noticeList = res.data.list;
						if (noticeList.length > 0) {
							// 取第一条公告作为显示内容
							let firstNotice = noticeList[0];
							if (firstNotice.customData) {
								try {
									let customData = JSON.parse(firstNotice.customData);
									that.noticeText = customData.content || customData.title || '暂无公告内容';
								} catch (e) {
									that.noticeText = firstNotice.customData;
								}
							} else {
								that.noticeText = '暂无公告内容';
							}
							that.noticeList = noticeList;
						} else {
							that.noticeText = '';
							that.noticeList = [];
						}
					} else {
						that.noticeText = '';
						that.noticeList = [];
					}
				}).catch(err => {
					that.noticeLoading = false;
					console.error('获取公告列表失败:', err);
					that.noticeText = '';
					that.noticeList = [];
				});
			},
			// 获取优惠券列表
			getCouponList: function() {
				let that = this;
				if (that.couponLoading) return;

				that.couponLoading = true;
				let data = {
					category: 3, // 平台券
					 merId:0,
					page: that.couponPage,
					limit: that.couponLimit
				};

				getCouponList(data).then(res => {
					that.couponLoading = false;
					if (res.code === 200 && res.data && res.data.list) {
						let couponList = res.data.list;
						// 处理优惠券数据，格式化显示文本
						that.couponList = couponList.map(coupon => {
							let text = '';
							if (coupon.couponType === 1) {
								// 满减券
								if (coupon.minPrice > 0) {
									text = `满${coupon.minPrice}减${coupon.money}`;
								} else {
									text = `立减${coupon.money}元`;
								}
							} else if (coupon.couponType === 2) {
								// 折扣券
								if (coupon.minPrice > 0) {
									text = `满${coupon.minPrice}享${coupon.discount}折`;
								} else {
									text = `${coupon.discount}折券`;
								}
							} else {
								text = coupon.name || '优惠券';
							}

							return {
								...coupon,
								text: text,
								type: `type${coupon.couponType}`,
								isReceived: coupon.isUse || false
							};
						});
					} else {
						that.couponList = [];
					}
				}).catch(err => {
					that.couponLoading = false;
					console.error('获取优惠券列表失败:', err);
					that.couponList = [];
				});
			},
			// 修改购物车
			reGoCat: function() {
				let that = this,
					productSelect = that.productValue[this.attrValue];
				//如果有属性,没有选择,提示用户选择
				if (
					that.attr.productAttr.length &&
					productSelect === undefined
				)
					return that.$util.Tips({
						title: "产品库存不足，请选择其它"
					});

				let q = {
					id: that.cartId,
					productId: that.product_id,
					num: that.attr.productSelect.cart_num,
					unique: that.attr.productSelect !== undefined ?
						that.attr.productSelect.unique : that.productInfo.id
				};
				getResetCart(q)
					.then(function(res) {
						that.attr.cartAttr = false;
						that.$util.Tips({
							title: "添加购物车成功",
							success: () => {
								that.loadend = false;
								that.page = 1;
								that.cartList.valid = [];
								that.getCartList();
								that.getCartNum();
							}
						});
					})
					.catch(res => {
						return that.$util.Tips({
							title: res
						});
					});
			},
			onMyEvent: function() {
				this.$set(this.attr, 'cartAttr', false);
			},
			reElection: function(item) {
				this.getGoodsDetails(item)
			},
			/**
			 * 获取产品详情
			 * 
			 */
			getGoodsDetails: function(item) {
				uni.showLoading({
					title: '加载中',
					mask: true
				});
				let that = this;
				that.cartId = item.id;
				that.product_id = item.productId;
				getProductDetail(item.productId, this.productType).then(res => {
					uni.hideLoading();
					that.attr.cartAttr = true;
					let productInfo = res.data.productInfo;
					that.$set(that, 'productInfo', productInfo);
					// that.$set(that.attr, 'productAttr', res.data.productAttr);
					that.$set(that, 'productValue', res.data.productValue);
					let productAttr = res.data.productAttr.map(item => {
						return {
							attrName: item.attrName,
							attrValues: item.attrValues.split(','),
							id: item.id,
							isDel: item.isDel,
							productId: item.productId,
							type: item.type
						}
					});
					this.$set(that.attr, 'productAttr', productAttr);
					that.DefaultSelect();
				}).catch(err => {
					uni.hideLoading();
				})
			},
			/**
			 * 属性变动赋值
			 * 
			 */
			ChangeAttr: function(res) {
				let productSelect = this.productValue[res];
				if (productSelect && productSelect.stock > 0) {
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this, "attrValue", res);
					this.$set(this, "attrTxt", "已选择");
				} else {
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", 0);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id);
					this.$set(this.attr.productSelect, "cart_num", 0);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				}
			},
			/**
			 * 默认选中属性
			 * 
			 */
			DefaultSelect: function() {
				let productAttr = this.attr.productAttr;
				let value = [];
				for (let key in this.productValue) {
					if (this.productValue[key].stock > 0) {
						value = this.attr.productAttr.length ? key.split(",") : [];
						break;
					}
				}
				for (let i = 0; i < productAttr.length; i++) {
					this.$set(productAttr[i], "index", value[i]);
				}
				//sort();排序函数:数字-英文-汉字；
				let productSelect = this.productValue[value.sort().join(",")];
				if (productSelect && productAttr.length) {
					this.$set(
						this.attr.productSelect,
						"storeName",
						this.productInfo.storeName
					);
					this.$set(this.attr.productSelect, "image", productSelect.image);
					this.$set(this.attr.productSelect, "price", productSelect.price);
					this.$set(this.attr.productSelect, "stock", productSelect.stock);
					this.$set(this.attr.productSelect, "unique", productSelect.id);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this, "attrValue", value.sort().join(","));
					this.$set(this, "attrTxt", "已选择");
				} else if (!productSelect && productAttr.length) {
					this.$set(
						this.attr.productSelect,
						"storeName",
						this.productInfo.storeName
					);
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", 0);
					this.$set(this.attr.productSelect, "unique", this.productInfo.id);
					this.$set(this.attr.productSelect, "cart_num", 0);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				} else if (!productSelect && !productAttr.length) {
					this.$set(
						this.attr.productSelect,
						"storeName",
						this.productInfo.storeName
					);
					this.$set(this.attr.productSelect, "image", this.productInfo.image);
					this.$set(this.attr.productSelect, "price", this.productInfo.price);
					this.$set(this.attr.productSelect, "stock", this.productInfo.stock);
					this.$set(
						this.attr.productSelect,
						"unique",
						this.productInfo.id || ""
					);
					this.$set(this.attr.productSelect, "cart_num", 1);
					this.$set(this, "attrValue", "");
					this.$set(this, "attrTxt", "请选择");
				}
			},
			attrVal(val) {
				this.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attrValues[val
					.indexn]);
			},
			/**
			 * 购物车数量加和数量减
			 * 
			 */
			ChangeCartNum: function(changeValue) {
				//changeValue:是否 加|减
				//获取当前变动属性
				let productSelect = this.productValue[this.attrValue];
				//如果没有属性,赋值给商品默认库存
				if (productSelect === undefined && !this.attr.productAttr.length)
					productSelect = this.attr.productSelect;
				//无属性值即库存为0；不存在加减；
				if (productSelect === undefined) return;
				let stock = productSelect.stock || 0;
				let num = this.attr.productSelect;
				if (changeValue) {
					num.cart_num++;
					if (num.cart_num > stock) {
						this.$set(this.attr.productSelect, "cart_num", stock ? stock : 1);
						this.$set(this, "cart_num", stock ? stock : 1);
					}
				} else {
					num.cart_num--;
					if (num.cart_num < 1) {
						this.$set(this.attr.productSelect, "cart_num", 1);
						this.$set(this, "cart_num", 1);
					}
				}
			},
			/**
			 * 购物车手动填写
			 * 
			 */
			iptCartNum: function(e) {
				this.$set(this.attr.productSelect, 'cart_num', e);
			},
			subDel: Debounce(function(event) {
				let selectValue = []
				this.cartList.valid.forEach(el => {
					el.cartInfoList.forEach(goods => {
						if (goods.check) {
							selectValue.push(goods.id)
						}
					})
				})
				if (selectValue.length > 0)
					cartDel(selectValue).then(res => {
						this.loadend = false;
						this.cartList.valid = [];
						this.getCartList();
						this.getCartNum();
					});
				else
					return this.$util.Tips({
						title: '请选择产品'
					});
			}),
			getSelectValueProductId: function() {
				let that = this;
				let validList = that.cartList.valid;
				let selectValue = that.selectValue;
				let productId = [];
				if (selectValue.length > 0) {
					for (let index in validList) {
						if (that.inArray(validList[index].id, selectValue)) {
							productId.push(validList[index].productId);
						}
					}
				};
				return productId;
			},
			subCollect: function(event) {
				let that = this;
				let type_id = []
				this.cartList.valid.forEach(el => {
					el.cartInfoList.forEach(goods => {
						if (goods.check) {
							type_id.push(goods.id)
						}
					})
				})
				if (type_id.length > 0) {
					cartToCollect(type_id).then(res => {
						that.$util.Tips({
							title: '收藏成功',
							icon: 'success'
						});
						this.cartList.valid = [];
						this.getCartList();
					}).catch(err => {
						return that.$util.Tips({
							title: err
						});
					});
				} else {
					return that.$util.Tips({
						title: '请选择产品'
					});
				}
			},
			// 立即下单
			subOrder: Debounce(function(event) {
				uni.showLoading({
					title: '加载中...'
				});
				this.selectValue = [];
				this.cartList.valid.forEach(el => {
					el.cartInfoList.forEach(goods => {
						if (goods.check) {
							this.selectValue.push(goods.id)
						}
					})
				})
				if (this.selectValue.length > 0) {
					this.getPreOrder();
				} else {
					uni.hideLoading();
					return that.$util.Tips({
						title: '请选择产品'
					});
				}
			}),
			/**
			 * 预下单
			 */
			getPreOrder: function() {
				let shoppingCartId = this.selectValue.map(item => {
					return {
						"shoppingCartId": Number(item)
					}
				})
				uni.hideLoading();
				this.$Order.getPreOrder("shoppingCart", shoppingCartId);
			},
			inArray: function(search, array) {
				for (let i in array) {
					if (array[i] == search) {
						return true;
					}
				}
				return false;
			},
			switchSelect: function() {
				let that = this;
				let validList = that.cartList.valid;
				let selectValue = that.selectValue;
				let selectCountPrice = 0.00;
				if (selectValue.length < 1) {
					that.selectCountPrice = selectCountPrice;
				} else {
					for (let index in validList) {
						if (that.inArray(validList[index].id, selectValue)) {
							if (validList[index].customData && validList[index].customData.price > 0) {
								selectCountPrice = that.$util.$h.Add(selectCountPrice, that.$util.$h.Mul(validList[index]
									.cartNum, that.$util.$h.Add(validList[index].vipPrice ? validList[index].vipPrice : validList[index].price, validList[index].customData.price)))
							} else {
								selectCountPrice = that.$util.$h.Add(selectCountPrice, that.$util.$h.Mul(validList[index]
									.cartNum, validList[index].vipPrice ? validList[index].vipPrice : validList[index].price))
							}
						}
					}
					that.selectCountPrice = selectCountPrice;
				}
			},
			checkboxAllChange() {
				this.isAllSelect = !this.isAllSelect
				this.cartAllCheck('cartCheck')
			},
			setAllSelectValue: function(status) {
				let that = this;
				let selectValue = [];
				let valid = that.cartList.valid;
				if (valid.length > 0) {
					let newValid = valid.map(item => {
						if (status) {
							if (that.footerswitch) {
								if (item.attrStatus) {
									item.checked = true;
									selectValue.push(item.id);
								} else {
									item.checked = false;
								}
							} else {
								item.checked = true;
								selectValue.push(item.id);
							}
							that.isAllSelect = true;
						} else {
							item.checked = false;
							that.isAllSelect = false;
						}
						return item;
					});
					that.$set(that.cartList, 'valid', newValid);
					that.selectValue = selectValue;
					that.switchSelect();
				}
			},
			checkboxChange: function(event) {
				let that = this;
				let value = event.detail.value;
				let valid = that.cartList.valid;
				let arr1 = [];
				let arr2 = [];
				let arr3 = [];
				let newValid = valid.map(item => {
					if (that.inArray(item.id, value)) {
						if (that.footerswitch) {
							if (item.attrStatus) {
								item.checked = true;
								arr1.push(item);
							} else {
								item.checked = false;
							}
						} else {
							item.checked = true;
							arr1.push(item);
						}
					} else {
						item.checked = false;
						arr2.push(item);
					}
					return item;
				});
				if (that.footerswitch) {
					arr3 = arr2.filter(item => !item.attrStatus);
				}
				that.$set(that.cartList, 'valid', newValid);
				// let newArr = that.cartList.valid.filter(item => item.attrStatus);
				that.isAllSelect = newValid.length === arr1.length + arr3.length;
				that.selectValue = value;
				that.switchSelect();
			},
			inArray: function(search, array) {
				for (let i in array) {
					if (array[i] == search) {
						return true;
					}
				}
				return false;
			},
			/**
			 * 购物车手动填写
			 * 
			 */
			iptCartNum: function(index) {
				let item = this.cartList.valid[index];
				if (item.cartNum) {
					this.setCartNum(item.id, item.cartNum);
				}
				this.switchSelect();
			},
			blurInput: function(index) {
				let item = this.cartList.valid[index];
				if (!item.cartNum) {
					item.cartNum = 1;
					this.$set(this.cartList, 'valid', this.cartList.valid)
				}
			},
			subCart: Debounce(function(item) {
				let that = this;
				let status = false;
				if (item.cartNum < 1) status = true;
				if (item.cartNum <= 1) {
					item.cartNum = 1;
					item.numSub = true;
					status = true;
				} else {
					item.cartNum = Number(item.cartNum) - 1;
					if (false == status) {
						that.setCartNum(item.id, item.cartNum, function(data) {
							item.numSub = false;
							item.numAdd = false;
							if (item.cartNum <= 1) {
								item.numSub = true;
							}
							//item.cartNum = Number(item.cartNum) - 1
							//this.cartTotalCount = Number(this.cartTotalCount) - 1;
							that.cartAllCheck('goodsCheck')
							// that.cartList.valid[index] = item;
							// that.switchSelect();
							// that.getCartNum();
						});
					}
				}

			}),
			addCart: Debounce(function(item) {
				let that = this;
				item.cartNum = Number(item.cartNum) + 1;
				if (item.cartNum < item.stock) {
					item.numAdd = false;
					item.numSub = false;
					that.setCartNum(item.id, item.cartNum, function(data) {
						that.cartAllCheck('goodsCheck')
					})
				} else if (item.cartNum === item.stock) {
					item.numAdd = true;
					item.numSub = false;
					that.setCartNum(item.id, item.cartNum, function(data) {
						that.cartAllCheck('goodsCheck')
					})
				} else {
					item.cartNum = item.stock;
					item.numAdd = true;
					item.numSub = false;
				}
			}),
			setCartNum(cartId, cartNum, successCallback) {
				let that = this;
				changeCartNum(cartId, cartNum).then(res => {
					successCallback && successCallback(res.data);
				}).catch(err => {
					return that.$util.Tips({
						title: err
					});
				});
			},
			getCartNum: function() {
				let that = this;
				getCartCounts(true, 'sum').then(res => {
					that.cartCount = res.data.count;
				});
			},
			// 商铺全选
			storeAllCheck(item, index) {
				// 店铺取消
				if (item.allCheck) {
					item.allCheck = false
					item.cartInfoList.forEach((el, index) => {
						el.check = false
					})
				} else {
					item.allCheck = true
					item.cartInfoList.forEach((el, index) => {
						if (!this.footerswitch) {
							el.check = true
						} else {
							if (parseFloat(el.stock) > 0) el.check = true
						}
					})
				}
				this.cartAllCheck('goodsCheck');
			},
			// 商品选中
			goodsCheck(goods) {
				if (!this.footerswitch) {
					goods.check = !goods.check
					this.cartAllCheck('goodsCheck')
				} else {
					if (parseFloat(goods.stock) > 0) {
						goods.check = !goods.check
						this.cartAllCheck('goodsCheck')
					}
				}
			},
			// 全选判断
			cartAllCheck(type) {
				let allArr = [];
				let totalMoney = 0
				let totalNum = 0
				this.cartList.valid.forEach((el, index) => {
					if (type == 'goodsCheck') {
						if (this.footerswitch) {
							let tempStock = el.cartInfoList.filter(goods => {
								return goods.stock > 0
							})
							let tempArr = el.cartInfoList.filter(goods => {
								return goods.check == true
							})
							if (tempStock.length == tempArr.length) {
								el.allCheck = true
								allArr.push(el)
							} else {
								el.allCheck = false
							}
						} else {
							let tempArr = el.cartInfoList.filter(goods => {
								return goods.check == true
							})
							if (el.cartInfoList.length == tempArr.length) {
								el.allCheck = true
								allArr.push(el)
							} else {
								el.allCheck = false
							}
						}

					} else {
						el.cartInfoList.forEach((goods) => {
							if (this.footerswitch) {
								goods.check = this.isAllSelect && parseFloat(goods.stock) > 0
							} else {
								goods.check = this.isAllSelect
							}
						})
						el.allCheck = this.isAllSelect
						if (el.allCheck) allArr.push(el)
					}
					// 总金额 //总数
					el.cartInfoList.forEach(e => {
						// if(this.footerswitch){
						// 	if (e.check && e.stock > 0) {
						// 		totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(e.price, e
						// 			.cartNum))
						// 		totalNum += e.cartNum 
						// 	}	
						// }
						if (e.check && e.stock > 0 && this.footerswitch) {
							if (e.customData && e.customData.price > 0) {
								totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(this.$util.$h.Add(e.price, e.customData.price), e
									.cartNum))
							} else {
								totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(e.price, e
									.cartNum))
							}
							totalNum += e.cartNum

						} else if (e.check && !this.footerswitch) {
							if (e.customData && e.customData.price > 0) {
								totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(this.$util.$h.Add(e.price, e.customData.price), e
									.cartNum))
							} else {
								totalMoney = this.$util.$h.Add(totalMoney, this.$util.$h.Mul(e.price, e
									.cartNum))
							}
							totalNum += e.cartNum
						}
					})
				})
				this.cartCount = totalNum
				this.selectCountPrice = totalMoney
				// 全选
				this.isAllSelect = allArr.length == this.cartList.valid.length ? true : false
			},
			getCartList() {
				if (this.loading) return false;
				let data = {
					isValid: true
				}
				this.loading = true;
				this.loadTitle = ''
				getCartList(data).then(res => {
					let valid = res.data;
					valid.forEach((mer) => {
						mer.cartInfoList.forEach((product) => {
							let customData = product.customData;
							if (customData) {
								try {
									product.customData = JSON.parse(customData);
								} catch (e) {
									product.customData = customData;
								}
							}
						});
					});
					this.getCheckGoods(valid);
					this.$set(this.cartList, 'valid', valid);
					this.checkboxAllChange()
					this.loading = false;
					this.loadTitle = '我也是有底线的';
					if (this.cartList.valid) this.canShow = true;
					uni.hideLoading();
				});
			},
			// 判断商品的初始状态是全部选中的
			getCheckGoods(valid) {
				let totalNum = 0;
				valid.forEach((item, index) => {
					item.allCheck = true
					item.cartInfoList.forEach((goods, j) => {
						if (this.footerswitch && this.isAllSelect) {
							goods.check = true
							totalNum += goods.cartNum
						} else {
							if (parseFloat(goods.stock) === 0) {
								goods.check = false
							} else {
								goods.check = true
								totalNum += goods.cartNum
							}
						}

						if (goods.cartNum == 1) {
							goods.numSub = true;
						} else {
							goods.numSub = false;
						}
						if (goods.cartNum == goods.stock) {
							goods.numAdd = true;
						} else {
							goods.numAdd = false;
						}
					})
				})
				this.cartCount = totalNum;
			},
			getInvalidList: function() {
				let that = this;
				if (this.loadingInvalid) return false;
				let data = {
					isValid: false
				}
				this.loadingInvalid = true;
				getCartList(data).then(res => {
					let invalidList = res.data;
					that.$set(that.cartList, 'invalid', invalidList);
					that.loadingInvalid = false;
				}).catch(res => {
					that.loadingInvalid = false;
				})

			},
			goodsOpen: function() {
				this.goodsHidden = !this.goodsHidden;
			},
			manage: function() {
				if (this.isAllSelect) {
					this.getCheckGoods(this.cartList.valid);
				}

				this.footerswitch = !this.footerswitch;
			},
			unsetCart: function() {
				let that = this,
					ids = [];
				this.cartList.invalid.forEach((el, index) => {
					el.cartInfoList.forEach(e => {
						ids.push(e.id);
					})
				})
				cartDel(ids).then(res => {
					that.$util.Tips({
						title: '清除成功'
					});
					that.$set(that.cartList, 'invalid', []);
				}).catch(res => {

				});
			},
			shareApi: function() {
				getShare().then(res => {
					this.$set(this, 'configApi', res.data);
					// #ifdef H5
					//this.setOpenShare(res.data);
					// #endif
				})
			},
			// 微信分享；
			setOpenShare: function(data) {
				let that = this;
				if (that.$wechat.isWeixin()) {
					let configAppMessage = {
						desc: data.synopsis,
						title: data.title,
						link: location.href,
						imgUrl: data.img
					};
					that.$wechat.wechatEvevt(["updateAppMessageShareData", "updateTimelineShareData"],
						configAppMessage);
				}
			},
			returns: function() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			showNav() {
				this.currentPage = !this.currentPage;
			},
			//下拉导航页面跳转
			linkPage(url) {
				if (url == '/pages/index/index' || url == '/pages/user/index') {
					uni.switchTab({
						url
					})
				} else {
					uni.navigateTo({
						animationType: animationType.type,
						animationDuration: animationType.duration,
						url
					})
				}
				this.currentPage = false
			},
			touchStart() {
				this.currentPage = false;
			}
		},
		onReachBottom() {
			let that = this;
			if (that.loadend) {
				that.getInvalidList();
			}
		}
	}
</script>

<style scoped lang="scss">
	.fixheight {
		width: 100%;
		height: 152rpx;
		@include main_bg_color(theme);
		position: fixed;
	}

	.icon-jiantou {
		font-size: 20rpx;
	}

	.noCheck {
		width: 32rpx;
		height: 32rpx;
		border-radius: 50%;
		overflow: hidden;
		border: 1px solid #999999;
		background-color: #eee;
	}

	.invalidClas {
		position: relative;
		z-index: 111;
		top: -120rpx;
	}

	.invalidClasNO {
		position: static;
		margin-top: 15px;
	}

	.store-title {
		display: flex;
		align-items: center;
		width: 100%;
		padding: 0 30rpx;
		height: 85rpx;
		border-bottom: 1px solid #f0f0f0;

		.info {
			margin-left: 28rpx;
			align-items: center;
		}

		.name {
			margin-left: 8rpx;
		}
	}

	.cartBox {
		position: relative;
		top: 64rpx;
	}

	.cart_nav {
		position: fixed;
		@include main_bg_color(theme);
		top: 0;
		left: 0;
		z-index: 99;
		width: 100%;
	}

	.px-20 {
		padding: 0 20rpx 0;
	}

	.pl-20 {
		padding-left: 20rpx;
	}

	.justify-center {
		justify-content: center;
	}

	.align-center {
		align-items: center;
	}

	.navbarCon {
		position: absolute;
		bottom: 0;
		height: 100rpx;
		width: 100%;
	}

	.h5_back {
		color: #fff;
		position: fixed;
		left: 20rpx;
		font-size: 32rpx;
		text-align: center;
		line-height: 58rpx;
	}

	.select_nav {
		width: 170rpx !important;
		height: 60rpx !important;
		border-radius: 33rpx;
		border: 1px solid rgba(255, 255, 255, .5);
		color: #fff;
		position: fixed;
		font-size: 18px;
		line-height: 58rpx;
		z-index: 1000;
		left: 14rpx;
	}

	.px-20 {
		padding: 0 20rpx 0;
	}

	.nav_line {
		content: '';
		display: inline-block;
		width: 1px;
		height: 34rpx;
		background: #fff;
		position: absolute;
		left: 0;
		right: 0;
		margin: auto;
	}

	.container_detail {
		/* #ifdef MP */
		margin-top: 32rpx;
		/* #endif */
	}

	.tab_nav {
		width: 100%;
		height: 48px;
		padding: 0 30rpx 0;
	}

	.nav_title {
		width: 200rpx;
		height: 58rpx;
		line-height: 58rpx;
		color: #fff;
		font-size: 36rpx;
		position: fixed;
		text-align: center;
		left: 0;
		right: 0;
		margin: auto;
	}

	.right_select {
		position: fixed;
		right: 20rpx;
		color: #fff;
		text-align: center;
		line-height: 58rpx;
	}

	.dialog_nav {
		position: fixed;
		/* #ifdef MP */
		left: 14rpx;
		/* #endif */
		/* #ifdef H5 || APP-PLUS*/
		right: 14rpx;
		/* #endif */
		width: 240rpx;
		background: #FFFFFF;
		box-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);
		z-index: 999;
		border-radius: 14rpx;

		&::before {
			content: '';
			width: 0;
			height: 0;
			position: absolute;
			/* #ifdef MP */
			left: 0;
			right: 0;
			margin: auto;
			/* #endif */
			/* #ifdef H5 || APP-PLUS */
			right: 8px;
			/* #endif */
			top: -9px;
			border-bottom: 10px solid #fff;
			border-left: 10px solid transparent;
			/*transparent 表示透明*/
			border-right: 10px solid transparent;
		}
	}

	.dialog_nav_item {
		width: 100%;
		height: 84rpx;
		line-height: 84rpx;
		padding: 0 20rpx 0;
		box-sizing: border-box;
		border-bottom: #eee;
		font-size: 28rpx;
		color: #333;
		position: relative;

		.iconfont {
			font-size: 32rpx;
		}

		&::after {
			content: '';
			position: absolute;
			width: 86px;
			height: 1px;
			background-color: #EEEEEE;
			bottom: 0;
			right: 0;
		}
	}

	.pl-20 {
		padding-left: 20rpx;
	}

	.px-20 {
		padding: 0 20rpx 0;
	}

	.justify-center {
		justify-content: center;
	}

	.align-center {
		align-items: center;
	}

	.shoppingCart {
		/* #ifdef H5 */
		// padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		/* #endif */
		position: absolute;
		width: 100%;
		margin-bottom: 280rpx;
	}

	.shoppingCart .labelNav {
		height: 62rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		color: #fff;
		width: 100%;
		box-sizing: border-box;
		@include main_bg_color(theme);
		z-index: 5;
		position: relative;
	}

	.shoppingCart .labelNav .item .iconfont {
		font-size: 26rpx;
		margin-right: 10rpx;
	}

	.shoppingCart .nav {
		// width: 92%;
		height: 90rpx;
		background-color: #fff;
		padding: 0 24rpx;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		font-size: 28rpx;
		color: #282828;
		// margin: -90rpx auto 0;
		z-index: 6;
		border-top-left-radius: 14rpx;
		border-top-right-radius: 14rpx;
	}

	.shoppingCart .nav .num {
		margin-left: 12rpx;
	}

	.shoppingCart .nav .administrate {
		font-size: 28rpx;
		color: #333333;
	}

	.shoppingCart .noCart {
		// margin-top: 171rpx;
		background-color: #fff;
		padding-top: 0.1rpx;
	}

	.shoppingCart .noCart .pictrue {
		width: 414rpx;
		height: 305;
		margin: 30rpx auto 30rpx auto;
	}

	.shoppingCart .noCart .pictrue image {
		width: 100%;
		height: 100%;
	}

	.shoppingCart .list {
		width: 100%;
		// margin-top: 178rpx;
		/* #ifdef MP */
		// margin-bottom:120rpx;
		/* #endif */
		/* #ifndef MP */
		// margin-bottom:240rpx;
		/* #endif */
		overflow: hidden;
		border-bottom-left-radius: 14rpx;
		border-bottom-right-radius: 14rpx;
	}


	.shoppingCart .list .item {
		padding: 24rpx;
		background-color: #fff;
	}

	.shoppingCart .list .item .picTxt {
		width: 582rpx;
		position: relative;
	}

	.shoppingCart .list .item .picTxt .pictrue {
		width: 160rpx;
		height: 160rpx;
	}

	.shoppingCart .list .item .picTxt .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.shoppingCart .list .item .picTxt .text {
		width: 396rpx;
		font-size: 28rpx;
		color: #282828;
	}

	.shoppingCart .list .item .picTxt .text .reColor {
		color: #999;
	}

	.shoppingCart .list .item .picTxt .text .reElection {
		margin-top: 20rpx;
	}

	.shoppingCart .list .item .picTxt .text .reElection .title {
		font-size: 24rpx;
		color: #666;
	}

	.shoppingCart .list .item .picTxt .text .reElection .reBnt {
		width: 120rpx;
		height: 46rpx;
		border-radius: 23rpx;
		font-size: 26rpx;
		@include coupons_border_color(theme);
		@include main_color(theme);
	}

	.shoppingCart .list .item .picTxt .text .infor {
		font-size: 24rpx;
		color: #999999;
		margin-top: 16rpx;
	}

	.money {
		font-size: 32rpx;
		font-weight: 600;
		position: relative;
		@include price_color(theme);
		
		.plus {
			color: #282828;
			padding: 0 10rpx;
			font-size: 40rpx;
			font-weight: 320;
			position: relative;
			top: -2rpx;
		}

		.price-color {
			@include price_color(theme);
		}
	}

	.mt-28 {
		margin-top: 28rpx;
	}

	.bg_color {
		@include main_bg_color(theme);
	}

	.font_color,
	.icon-xuanzhong11 {
		@include main_color(theme);
		font-size: 36rpx;
	}

	.shoppingCart .list .item .picTxt .carnum {
		height: 47rpx;
		position: absolute;
		bottom: 0;
		right: 0;
	}

	.shoppingCart .list .item .picTxt .carnum view {
		border: 1rpx solid #a4a4a4;
		width: 66rpx;
		text-align: center;
		height: 100%;
		line-height: 44rpx;
		font-size: 28rpx;
		color: #a4a4a4;
	}

	.shoppingCart .list .item .picTxt .carnum .reduce {
		border-right: 0;
		border-radius: 3rpx 0 0 3rpx;
		border-radius: 22rpx 0rpx 0rpx 22rpx;
		font-size: 34rpx;
		line-height: 38rpx;
	}

	.shoppingCart .list .item .picTxt .carnum .on {
		border-color: #e3e3e3;
		color: #dedede;
	}

	.shoppingCart .list .item .picTxt .carnum .plus {
		border-left: 0;
		border-radius: 0 3rpx 3rpx 0;
		border-radius: 0rpx 22rpx 22rpx 0rpx;
		font-size: 34rpx;
		line-height: 38rpx;
	}

	.shoppingCart .list .item .picTxt .carnum .num {
		color: #282828;
	}

	.shoppingCart .invalidGoods {
		background-color: #fff;
		// margin-top: 30rpx;
		/* #ifdef MP */
		// margin-top: 140rpx;
		/* #endif */

	}

	.shoppingCart .invalidGoods .goodsNav {
		width: 100%;
		height: 90rpx;
		padding: 0 24rpx;
		box-sizing: border-box;
		font-size: 28rpx;
		color: #333333;
	}

	.shoppingCart .invalidGoods .goodsNav .iconfont {
		color: #424242;
		font-size: 28rpx;
		margin-right: 17rpx;
	}

	.shoppingCart .invalidGoods .goodsNav .del {
		font-size: 26rpx;
		color: #333;
	}

	.shoppingCart .invalidGoods .goodsNav .del .icon-shanchu1 {
		color: #333;
		font-size: 33rpx;
		vertical-align: -2rpx;
		margin-right: 8rpx;
	}

	.shoppingCart .invalidGoods .goodsList .item {
		padding: 24rpx;
	}

	.shoppingCart .invalidGoods .goodsList .picTxt {
		width: 576rpx;
	}

	.shoppingCart .invalidGoods .goodsList .item .invalid {
		font-size: 22rpx;
		color: #CCCCCC;
		height: 36rpx;
		border-radius: 3rpx;
		text-align: center;
		line-height: 36rpx;
	}

	.shoppingCart .invalidGoods .goodsList .item .pictrue {
		width: 160rpx;
		height: 160rpx;
	}

	.shoppingCart .invalidGoods .goodsList .item .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 6rpx;
	}

	.shoppingCart .invalidGoods .goodsList .item .text {
		width: 396rpx;
		font-size: 28rpx;
		color: #999;
		height: 140rpx;
	}

	.shoppingCart .invalidGoods .goodsList .item .text .name {
		width: 100%;
	}

	.shoppingCart .invalidGoods .goodsList .item .text .infor {
		font-size: 24rpx;
	}

	.shoppingCart .invalidGoods .goodsList .item .text .end {
		font-size: 26rpx;
		color: #bbb;
	}

	.footer {
		z-index: 999;
		width: 100%;
		height: 100rpx;
		background-color: #fff;
		position: fixed;
		padding: 0 24rpx;
		box-sizing: border-box;
		border-top: 1rpx solid #eee;
		bottom: var(--window-bottom);

	}

	.footer .checkAll {
		font-size: 28rpx;
		color: #282828;
		margin-left: 14rpx;
	}

	.footer .money {
		font-size: 30rpx;

		.font-color {
			font-weight: 600;
		}
	}

	.footer .placeOrder {
		color: #fff;
		font-size: 30rpx;
		width: 226rpx;
		height: 70rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 70rpx;
		margin-left: 22rpx;
	}

	.footer .button .bnt {
		font-size: 28rpx;
		color: #999;
		border-radius: 50rpx;
		border: 1px solid #999;
		width: 160rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
	}

	.btn_cart_color {
		font-size: 28rpx;
		border-radius: 25px;
		width: 160rpx;
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
		@include coupons_border_color(theme);
		@include main_color(theme);
	}

	.footer .button form~form {
		margin-left: 17rpx;
	}

	.uni-p-b-96 {
		height: 96rpx;
	}

	/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {
		@include main_bg_color(theme);
		border: none !important;
		color: #fff !important
	}

	/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
		@include main_bg_color(theme);
		border: none !important;
		color: #fff !important;
		margin-right: 0 !important;
	}

	.allcheckbox .icon-xuanzhong11 {
		margin-right: 16rpx;
	}

	/* 公告模块样式 */
	.notice-module {
		margin: 20rpx 24rpx;
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		margin-top: 20rpx;
	}

	.notice-content {
		padding: 20rpx 24rpx;
		border: 2rpx solid #FF2222;
		border-radius: 12rpx;
		background: #fff;
	}

	.notice-icon {
		font-size: 28rpx;
		color: #FF2222;
		margin-right: 12rpx;
		flex-shrink: 0;
	}

	.notice-text {
		font-size: 26rpx;
		color: #FF2222;
		line-height: 1.4;
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 优惠券模块样式 */
	.coupon-module {
		margin: 0 24rpx 20rpx 24rpx;
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.coupon-content {
		padding: 24rpx;
		background: #fff;
		border-radius: 12rpx;
	}

	.coupon-left {
		flex: 1;
	}

	.coupon-label {
		font-size: 28rpx;
		color: #999999;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.coupon-list {
		flex: 1;
		margin: 0;
	}

	.coupon-item {
		padding: 8rpx 16rpx;
		margin-right: 16rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
		color: #FF2222;
		border: 2rpx solid #FF2222;
		background: #fff;
		white-space: nowrap;
	}

	.coupon-item:last-child {
		margin-right: 0;
	}

	.coupon-item.received {
		background: #f5f5f5;
		color: #999999;
		border-color: #e5e5e5;
	}



	.coupon-right {
		flex-shrink: 0;
	}

	.more-text {
		font-size: 28rpx;
		color: #FF2222;
		margin-right: 8rpx;
	}

	.more-arrow {
		font-size: 24rpx;
		color: #FF2222;
	}
</style>
