<template>
	<view class="logistics-company-select-page" :data-theme="theme">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px', height: (statusBarHeight + navHeight) + 'px' }">
			<view class="navbar-content" :style="{ height: navHeight + 'px' }">
				<view class="navbar-left" @click="goBack">
					<text class="iconfont icon-xiangzuo"></text>
				</view>
				<view class="navbar-title">选择物流公司</view>
				<view class="navbar-right"></view>
			</view>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<text class="iconfont icon-sousuo search-icon"></text>
				<input 
					type="text" 
					v-model="searchKeyword" 
					placeholder="搜索物流公司" 
					class="search-input"
					@input="onSearchInput"
				/>
				<text class="clear-icon" v-if="searchKeyword" @click="clearSearch">×</text>
			</view>
		</view>
		
		<!-- 物流公司列表 -->
		<view class="company-list">
			<view 
				class="company-item" 
				v-for="(company, index) in filteredCompanies" 
				:key="company.id"
				@click="selectCompany(company)"
			>
				<view class="company-info">
					<text class="company-name">{{ company.name }}</text>
					<text class="company-code" v-if="company.code">{{ company.code }}</text>
				</view>
				<text class="iconfont icon-xuanzhong" v-if="selectedCompanyId === company.id"></text>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredCompanies.length === 0">
			<text class="empty-text">没有找到相关物流公司</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				theme: 'default',
				statusBarHeight: 0,
				navHeight: 44,
				searchKeyword: '', // 搜索关键词
				companiesList: [], // 物流公司列表
				selectedCompanyId: '', // 当前选中的物流公司ID
			};
		},
		computed: {
			// 过滤后的物流公司列表
			filteredCompanies() {
				if (!this.searchKeyword.trim()) {
					return this.companiesList;
				}
				
				const keyword = this.searchKeyword.toLowerCase();
				return this.companiesList.filter(company => 
					company.name.toLowerCase().includes(keyword) ||
					(company.code && company.code.toLowerCase().includes(keyword))
				);
			}
		},
		onLoad(options) {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
			
			// 获取胶囊按钮信息以适配导航栏
			// #ifdef MP-WEIXIN
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.navHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;
			// #endif
			
			// #ifndef MP-WEIXIN
			this.navHeight = 44;
			// #endif
			
			// 获取传递过来的物流公司数据
			if (options.companies) {
				try {
					this.companiesList = JSON.parse(decodeURIComponent(options.companies));
					console.log('接收到的物流公司列表：', this.companiesList);
				} catch (e) {
					console.error('解析物流公司数据失败：', e);
					uni.showToast({
						title: '数据解析失败',
						icon: 'none'
					});
				}
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 搜索输入
			onSearchInput() {
				// 实时搜索，这里可以添加防抖逻辑
				console.log('搜索关键词：', this.searchKeyword);
			},
			
			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
			},
			
			// 选择物流公司
			selectCompany(company) {
				console.log('选择物流公司：', company);
				
				this.selectedCompanyId = company.id;
				
				// 将选择的物流公司数据存储到本地存储
				try {
					uni.setStorageSync('selectedLogisticsCompany', JSON.stringify(company));
					
					// 返回上一页
					uni.navigateBack();
				} catch (e) {
					console.error('存储物流公司数据失败：', e);
					uni.showToast({
						title: '选择失败，请重试',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.logistics-company-select-page {
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.custom-navbar {
		background-color: #fff;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		border-bottom: 1rpx solid #eee;
		
		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			
			.navbar-left {
				width: 80rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				
				.iconfont {
					font-size: 36rpx;
					color: #333;
				}
			}
			
			.navbar-title {
				flex: 1;
				text-align: center;
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
			}
			
			.navbar-right {
				width: 80rpx;
			}
		}
	}
	
	.search-container {
		position: fixed;
		top: 88rpx; // 导航栏高度
		left: 0;
		right: 0;
		z-index: 998;
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #eee;
		
		.search-box {
			display: flex;
			align-items: center;
			background-color: #f5f5f5;
			border-radius: 50rpx;
			padding: 0 30rpx;
			height: 70rpx;
			
			.search-icon {
				font-size: 28rpx;
				color: #999;
				margin-right: 20rpx;
			}
			
			.search-input {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				
				&::placeholder {
					color: #999;
				}
			}
			
			.clear-icon {
				font-size: 40rpx;
				color: #999;
				margin-left: 20rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
	
	.company-list {
		margin-top: 178rpx; // 导航栏 + 搜索框高度
		padding: 0 30rpx;
		
		.company-item {
			background-color: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			.company-info {
				flex: 1;
				
				.company-name {
					font-size: 30rpx;
					color: #333;
					font-weight: 500;
					display: block;
					margin-bottom: 10rpx;
				}
				
				.company-code {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.iconfont {
				font-size: 32rpx;
				color: #BDFD5B;
			}
		}
	}
	
	.empty-state {
		margin-top: 200rpx;
		text-align: center;
		padding: 100rpx 30rpx;
		
		.empty-text {
			font-size: 28rpx;
			color: #999;
		}
	}
</style>
