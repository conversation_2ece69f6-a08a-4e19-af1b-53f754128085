<template>
	<view :data-theme="theme">
		<view class='collectionGoods' v-if="collectProductList.length">
			<view class="borderPad navbox">
				<view class='nav acea-row row-between-wrapper'>
					<view>当前共 <text class='num font_color'>{{ totals }}</text>件商品</view>
					<view class='administrate acea-row row-center-wrapper' @click='manage'>{{ footerswitch ? '管理' : '取消'}}
					</view>
				</view>
			</view>
			<view class="list">
				<checkbox-group @change="checkboxChange" class="centent">
					<view v-for="(item,index) in collectProductList" :key="index" class='item'>
						<checkbox :value="item.productId.toString()" :checked="item.checked" v-if="!footerswitch" />
						<navigator :url='"/pages/goods/goods_details/index?id="+item.productId' hover-class='none'
							class="acea-row">
							<view class='pictrue'>
								<image :src="item.image"></image>
							</view>
							<view class='text'>
								<view class='name'>{{item.name}}</view>
								<view class='money'>￥{{item.price}}</view>
							</view>
						</navigator>
					</view>
				</checkbox-group>
			</view>
			<view class='loadingicon acea-row row-center-wrapper' :class="!footerswitch ? 'footer-height-on' : 'footer-height'">
				<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
			</view>
			<view v-if="!footerswitch" class='footer acea-row row-between-wrapper'>
				<view>
					<checkbox-group @change="checkboxAllChange">
						<checkbox value="all" :checked="!!isAllSelect" />
						<text class='checkAll'>全选</text>
					</checkbox-group>
				</view>
				<view class='button acea-row row-middle'>
					<form @submit="delCollectionAll" report-submit='true'>
						<button class='bnt cart-color' formType="submit">取消收藏</button>
					</form>
				</view>
			</view>
		</view>
		<view class='noCommodity' v-else-if="!collectProductList.length && page > 1">
			<view class='pictrue'>
				<image src='/pages/aastatictoT/static/images/noCollect.png'></image>
				<view class="default_txt">收藏列表为空哦~</view>
			</view>
			<recommend></recommend>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getCollectUserList,
		collectCancel
	} from '@/api/product.js';
	import {
		mapGetters
	} from "vuex";
	import {
		toLogin
	} from '@/libs/login.js';
	import recommend from "@/components/base/recommend.vue";
	let app = getApp();
	export default {
		components: {
			recommend
		},
		data() {
			return {
				footerswitch: true,
				loadTitle: '加载更多',
				loading: false,
				loadend: false,
				collectProductList: [],
				limit: 20,
				page: 1,
				isAllSelect: false,
				selectValue: [],
				totals: 0,
				theme:app.globalData.theme,
			};
		},
		computed: mapGetters(['isLogin']),
		onLoad() {
			let that = this;
			if (this.isLogin) {
				this.loadend = false;
				this.page = 1;
				this.collectProductList = [];
				this.get_user_collect_product();
			} else {
				toLogin();
			}
		},
		onShow() {
			this.loadend = false;
			this.page = 1;
			this.collectProductList = [];
			this.get_user_collect_product();
		},
		methods: {
			manage: function() {
				this.footerswitch = !this.footerswitch;
			},

			checkboxChange: function(event) {
				var items = this.collectProductList,
					values = event.detail.value;
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if (values.includes(item.productId.toString())) {
						this.$set(item, 'checked', true)
					} else {
						this.$set(item, 'checked', false)
					}
				}
				this.selectValue = values.toString();
				this.isAllSelect = items.length === values.length;
			},
			checkboxAllChange: function(event) {
				let value = event.detail.value;
				if (value.length > 0) {
					this.setAllSelectValue(1)
				} else {
					this.setAllSelectValue(0)
				}
			},
			setAllSelectValue: function(status) {
				let selectValue = [];
				if (this.collectProductList.length > 0) {
					this.collectProductList.map(item => {
						if (status) {
							this.$set(item, 'checked', true)
							selectValue.push(item.productId);
							this.isAllSelect = true;
						} else {
							this.$set(item, 'checked', false)
							this.isAllSelect = false;
						}
					});
					this.selectValue = selectValue.toString();
				}
			},
			/**
			 * 获取收藏产品
			 */
			get_user_collect_product: function() {
				let that = this;
				if (this.loading) return;
				if (this.loadend) return;
				that.selectValue = [];
				that.isAllSelect = false;
				that.loading = true;
				that.loadTitle = "";
				getCollectUserList({
					page: that.page,
					limit: that.limit
				}).then(res => {
					res.data.list.map(item => {
						that.$set(item, 'right', 0);
					});
					that.totals = res.data.total;
					let collectProductList = res.data.list;
					let loadend = collectProductList.length < that.limit;

					that.collectProductList = that.$util.SplitArray(collectProductList, that.collectProductList);
					that.$set(that, 'collectProductList', that.collectProductList);

					that.loadend = loadend;
					that.page = that.page + 1;
					that.loading = false;
					that.loadTitle = loadend ? '我也是有底线的' : '加载更多';
				}).catch(err => {
					that.loading = false;
					that.loadTitle = "加载更多";
				});
			},
			/**
			 * 取消收藏
			 */
			delCollection: function(id, index) {
				this.selectValue = id;
				this.del({
					ids: this.selectValue.toString()
				});
			},
			delCollectionAll: function() {
				if (!this.selectValue || this.selectValue.length == 0) return this.$util.Tips({
					title: '请选择商品'
				});
				this.del({
					ids: this.selectValue
				});
			},
			del: function(data) {
				collectCancel(data).then(res => {
					this.$util.Tips({
						title: '取消收藏成功',
						icon: 'success'
					});
					this.collectProductList = [];
					this.loadend = false;
					this.page = 1;
					this.get_user_collect_product();
				}).catch(err => {
					return this.$util.Tips({
						title: err
					})
				});
			}
		},
		onReachBottom() {
			this.get_user_collect_product();
		}
	}
</script>

<style scoped lang="scss">
	.money{
		font-size: 26rpx;
		@include price_color(theme);
	}
	.order-item {
		width: 100%;
		display: flex;
		position: relative;
		align-items: right;
		flex-direction: row;
	}

	.remove {
		width: 120rpx;
		height: 40rpx;
		@include main_bg_color(theme);
		color: #fff;
		position: absolute;
		bottom: 30rpx;
		right: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 24rpx;
	}

	.collectionGoods {
        .navbox{
			width: 100%;
			height: 90rpx;
		}
		.nav {
			width: 93.5%;
			height: 90rpx;
			background-color: #fff;
			padding: 0 24rpx;
			-webkit-box-sizing: border-box;
			box-sizing: border-box;
			font-size: 28rpx;
			color: #282828;
			position: fixed;
			z-index: 5;
			top: 30rpx;
			border-bottom: 1px solid #EEEEEE;
			border-top-left-radius: 14rpx;
			border-top-right-radius: 14rpx;
		}

		.list {
			padding: 24rpx;
			padding-bottom: 40rpx;
			
			.text {
				flex: 1;
				margin-right: 20rpx;
				
				.name {
					display: -webkit-box;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
					overflow: hidden;
					margin-bottom: 10rpx;
				}
			}
		}

		.centent {
			background-color: #fff;
			border-bottom-left-radius: 14rpx;
			border-bottom-right-radius: 14rpx;
			padding: 24rpx 0;
		}
	}

	.collectionGoods .item {
		background-color: #fff;
		margin-left: 20rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		
		checkbox {
			margin: 0 20rpx;
		}

		.pictrue {
			width: 130rpx;
			height: 130rpx;
			margin-right: 20rpx;
			
			image {
				width: 100%;
				height: 100%;
				border-radius: 14rpx;
			}
		}
	}

	.collectionGoods .item:nth-last-child(1){
		margin-bottom: 0;
	}

	.footer-height {
		padding-bottom: 25rpx;
		padding-bottom: calc(25rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(25rpx + env(safe-area-inset-bottom) / 2);
	}
	.footer-height-on {
		padding-bottom: 165rpx;
		padding-bottom: calc(165rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(165rpx + env(safe-area-inset-bottom) / 2);
	}
	.footer {
		z-index: 9;
		width: 100%;
		background-color: #fff;
		position: fixed;
		box-sizing: border-box;
		border-top: 1rpx solid #eee;
		border-bottom: 1px solid #EEEEEE;
		bottom: 0rpx;
		padding: 15rpx 30rpx;
		padding-bottom: calc(15rpx + constant(safe-area-inset-bottom) / 2);
		padding-bottom: calc(15rpx + env(safe-area-inset-bottom) / 2);

		.checkAll {
			font-size: 28rpx;
			color: #282828;
			margin-left: 16rpx;
		}

		.button .bnt {
			font-size: 28rpx;
			color: #999;
			border-radius: 30rpx;
			border: 1px solid #999;
			width: 160rpx;
			height: 60rpx;
			text-align: center;
			line-height: 60rpx;
		}
	}
	.font_color{
		@include main_color(theme);
	}
	/deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {
		@include main_bg_color(theme);
		@include coupons_border_color(theme);
		color: #fff!important
	}
	
	/deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
		@include main_bg_color(theme);
		@include coupons_border_color(theme);
		color: #fff!important;
		margin-right: 0 !important;
	}
	
	.noCommodity {
		padding-top: 1rpx;
		border-top: 0;
		
		.pictrue {
			width: 414rpx;
			height: 305rpx;
			margin: 100rpx auto 60rpx auto;
		}

		.default_txt {
			font-size: 26rpx;
			color: #999;
			text-align: center;
			margin-top: -80rpx;
		}
	}

	.m-30 {
		margin: 30rpx 0;
	}
</style>
