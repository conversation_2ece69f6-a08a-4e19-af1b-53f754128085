<template>
	<view :data-theme="theme">
		<view class="container">
			<view class="comment-content">
				<view class="comment-summary">
					<view class="praise-rate">
						评价({{replyData.sumCount }})
					</view>
				</view>
				<view class="comment-list">
					<block v-if="reply && reply.length > 0">
						<userEvaluation :reply="reply"></userEvaluation>
					</block>
					<view v-else class="no-comment">
						暂无评论
					</view>
				</view>
			</view>
			<view class='loadingicon acea-row row-center-wrapper' v-if="!loadend || loading">
				<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import {
		getReplyList,
		getReplyConfig
	} from '@/api/product.js';
	import userEvaluation from '@/components/userEvaluation';
	let app = getApp();
	export default {
		components: {
			userEvaluation
		},
		data() {
			return {
				replyData: {},
				product_id: 0,
				reply: [],
				type: 0,
				loading: false,
				loadend: false,
				loadTitle: '加载更多',
				page: 1,
				limit: 20,
				theme:app.globalData.theme,
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			let that = this;
			if (!options.productId) return that.$util.Tips({
				title: '缺少参数'
			}, {
				tab: 3,
				url: 1
			});
			that.productId = options.productId;
		},
		onShow: function() {
			this.getProductReplyCount();
			this.getProductReplyList();
		},
		methods: {
			/**
			 * 获取评论统计数据
			 * 
			 */
			getProductReplyCount: function() {
				let that = this;
				getReplyConfig(that.productId).then(res => {
					that.$set(that, 'replyData', res.data);
				});
			},
			/**
			 * 分页获取评论
			 */
			getProductReplyList: function() {
				let that = this;
				if (that.loadend) return;
				if (that.loading) return;
				that.loading = true;
				that.loadTitle = '';
				getReplyList(that.productId, {
					page: that.page,
					limit: that.limit,
					type: that.type,
				}).then(res => {
					let list = res.data.list,
						loadend = list.length < that.limit;
					that.reply = that.$util.SplitArray(list, that.reply);
					that.$set(that, 'reply', that.reply);
					that.loading = false;
					that.loadend = loadend;
					if (that.reply.length) {
						that.loadTitle = loadend ? "我是有底线的~" : "加载更多";
					}
					that.page = that.page + 1;
				}).catch(() => {
					that.loading = false,
						that.loadTitle = '加载更多'
				});
			},

		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			this.getProductReplyList();
		},
	}
</script>

<style lang="scss">
	page {
		background-color: #fff;
		height: 100%;
	}
	.container {
		height: 100%;
		padding-bottom: 10rpx;
		padding-bottom: calc(10rpx + constant(safe-area-inset-bottom) / 3);
		padding-bottom: calc(10rpx + env(safe-area-inset-bottom) / 3);
	}

	.font_color{
		@include main_color(theme);
	}

	.comment-content {
		padding: 30rpx;

		.comment-summary {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;

			.praise-rate {
				font-size: 28rpx;
				color: #333;
			}

			.view-all {
				font-size: 26rpx;
				color: #666;
				display: flex;
				align-items: center;

				.iconfont {
					font-size: 20rpx;
					margin-left: 8rpx;
				}
			}
		}

		.comment-list {
			.no-comment {
				text-align: center;
				color: #999;
				font-size: 28rpx;
				padding: 60rpx 0;
			}
		}
	}

	.loadingicon {
		padding: 20rpx 0;
		font-size: 26rpx;
		color: #999;

		.loading {
			margin-right: 10rpx;
		}
	}
</style>
