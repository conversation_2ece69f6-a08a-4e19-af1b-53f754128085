<template>
  <view class="order-settlement">
    <!-- 收货地址 -->
    <view class="address-section" @click="selectAddress">
      <view class="address-header" v-if="!selectedAddress">
        <text class="address-title">添加收货地址</text>
        <uni-icons type="right" size="18" color="#999999"></uni-icons>
      </view>
      <view class="address-info" v-else>
        <view class="address-detail">
          <view class="address-name-phone">
            <text class="name">{{ selectedAddress.name }}</text>
            <text class="phone">{{ selectedAddress.phone }}</text>
          </view>
          <view class="address-full">{{ selectedAddress.province }}{{ selectedAddress.city }}{{ selectedAddress.district }}{{ selectedAddress.detail }}</view>
        </view>
        <uni-icons type="right" size="18" color="#999999"></uni-icons>
      </view>
    </view>

    <!-- 配送方式和商品信息 -->
    <view class="delivery-product-section">
      <!-- 配送方式 -->
      <view class="delivery-header">
        <text class="platform-name">乐腾生态平台</text>
        <view class="delivery-type">
          <text class="delivery-label">快递</text>
        </view>
      </view>
      
      <!-- 商品信息 -->
      <view class="product-item">
        <!-- 上部分：图片 + 商品信息 -->
        <view class="product-upper">
          <image :src="productInfo.image" class="product-image"></image>
          <view class="product-right">
            <!-- 右侧上部分：标题 -->
            <view class="product-title">{{ productInfo.title }}</view>
            <!-- 右侧下部分：规格价格 + 数量 -->
            <view class="product-bottom">
              <view class="product-left-info">
                <view class="product-spec">{{ productInfo.spec }}</view>
                <view class="product-price">¥{{ productInfo.price }}</view>
              </view>
              <view class="product-quantity">x{{ productInfo.quantity }}</view>
            </view>
          </view>
        </view>
        
        <!-- 下部分：商品描述
        <view class="product-description" v-if="productInfo.description">
          {{ productInfo.description }}
        </view> -->
        
        <!-- 眼镜定制信息 -->
        <glassesProductCustomInfo v-if="customData && (customData.lens || customData.optometry)" :customData="customData"></glassesProductCustomInfo>
      </view>
      
      <!-- 总计积分 -->
      <view class="total-points">
        <text class="total-label">合计：</text>
        <text class="total-value">{{ productInfo.totalPoints }}积分</text>
      </view>
    </view>

    <!-- 价格明细 -->
    <view class="price-detail-section">
      <view class="price-header">
        <text class="price-title">价格明细</text>
      </view>
      
      <view class="price-item">
        <text class="price-label">商品金额</text>
        <text class="price-value">{{ productInfo.totalPoints }}积分</text>
      </view>
      
      <view class="price-item">
        <text class="price-label">运费</text>
        <text class="price-value">¥0</text>
      </view>
      
      <view class="price-item total-item">
        <text class="price-label">合计</text>
        <text class="price-value">{{ productInfo.totalPoints }}积分</text>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="payment-info">
        <text class="payment-label">实付款：</text>
        <text class="payment-amount">{{ productInfo.totalPoints }}积分</text>
      </view>
      <button class="submit-btn" @click="submitOrder">提交订单</button>
    </view>
  </view>
</template>

<script>
import { getIntegralDetail, createIntegralOrder } from '@/api/user.js';
import glassesProductCustomInfo from '@/components/glassesProductCustomInfo';

export default {
  components: {
    glassesProductCustomInfo
  },
  data() {
    return {
      productId: '',
      productInfo: {
        image: '',
        title: '',
        spec: '',
        description: '',
        price: 0,
        quantity: 1,
        totalPoints: 0
      },
      selectedAddress: null,
      selectedSku: {},
      selectedAttrs: {},
      customData: {},
      addressId: null,  // 添加地址ID字段
      attrValueId: null,  // 添加属性值ID字段
      integralProductId: null,  // 积分商品ID
      productId: null,  // 具体商品ID（规格商品ID）
      optometryId: null,  // 验光单ID
      storeId: 1  // 店铺ID，默认为1
    }
  },
  onLoad(options) {
    console.log('积分订单页面接收参数:', options);
    
    // 接收从商品详情页传过来的参数
    if (options.productId) {
      this.integralProductId = options.productId;  // 积分商品ID
    }
    
    if (options.integralPrice) {
      this.productInfo.totalPoints = parseInt(options.integralPrice);
      this.productInfo.price = parseInt(options.integralPrice);
    }
    
    // 接收规格信息
    if (options.sku) {
      try {
        this.selectedSku = JSON.parse(decodeURIComponent(options.sku));
        // 从SKU中获取attrValueId和productId
        if (this.selectedSku && this.selectedSku.id) {
          this.attrValueId = this.selectedSku.id;
        }
        // 从SKU中获取具体商品ID（规格商品ID）
        if (this.selectedSku && this.selectedSku.productId) {
          this.productId = this.selectedSku.productId;
        }
      } catch (e) {
        console.error('解析SKU数据失败:', e);
      }
    }
    
    if (options.quantity) {
      this.productInfo.quantity = parseInt(options.quantity) || 1;
    }
    
    if (options.selectedAttrs) {
      try {
        this.selectedAttrs = JSON.parse(decodeURIComponent(options.selectedAttrs));
        // 生成规格描述
        this.productInfo.spec = Object.values(this.selectedAttrs).join('，');
      } catch (e) {
        console.error('解析选中属性失败:', e);
      }
    }
    
    if (options.customData) {
      try {
        this.customData = JSON.parse(decodeURIComponent(options.customData));
        // 从customData中获取验光单ID
        if (this.customData && this.customData.optometry && this.customData.optometry.id) {
          this.optometryId = this.customData.optometry.id;
        }
      } catch (e) {
        console.error('解析自定义数据失败:', e);
      }
    }
    
    // 加载商品基本信息
    this.loadProductInfo();
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '订单结算'
    });
  },
  
  onShow() {
    // 检查是否有选中的地址信息（从地址选择页面返回）
    const selectedAddressData = uni.getStorageSync('selectedAddress');
    if (selectedAddressData) {
      this.selectedAddress = selectedAddressData;
      this.addressId = selectedAddressData.id; // 保存地址ID
      // 清除缓存的地址信息
      uni.removeStorageSync('selectedAddress');
      console.log('获取到选中的地址:', this.selectedAddress);
      console.log('地址ID:', this.addressId);
    }
  },
  methods: {
    // 选择收货地址
    selectAddress() {
      uni.navigateTo({
        url: '/pages/address/user_address_list/index?type=select'
      });
    },
    
    // 加载商品信息
    async loadProductInfo() {
      if (!this.integralProductId) return;
      
      try {
        const res = await getIntegralDetail(this.integralProductId);
        if (res.code === 200 && res.data) {
          const data = res.data;
          
          // 更新商品基本信息
          this.productInfo.title = data.name || '';
          this.productInfo.image = data.image || '';
          this.productInfo.description = data.intro || '';
          
          // 如果没有规格信息，使用基本商品信息
          if (!this.selectedAttrs || Object.keys(this.selectedAttrs).length === 0) {
            this.productInfo.spec = '默认规格';
          }
          
          // 如果没有attrValueId，使用积分商品ID
          if (!this.attrValueId) {
            this.attrValueId = this.integralProductId;
          }
          
          // 如果没有具体商品ID，从商品数据中获取
          if (!this.productId && data.product) {
            this.productId = data.product.id;
          }
          
                      console.log('加载的商品信息:', data);
            console.log('integralProductId:', this.integralProductId);
            console.log('productId:', this.productId);
            console.log('attrValueId:', this.attrValueId);
            console.log('optometryId:', this.optometryId);
            console.log('customData:', this.customData);
        }
      } catch (error) {
        console.error('获取商品信息失败:', error);
      }
    },
    
    // 提交订单
    submitOrder() {
      // 检查是否选择了收货地址
      if (!this.selectedAddress || !this.addressId) {
        uni.showToast({
          title: '请选择收货地址',
          icon: 'none'
        });
        return;
      }
      
      uni.showModal({
        title: '确认提交',
        content: `确定要提交订单吗？将消耗${this.productInfo.totalPoints}积分`,
        success: (res) => {
          if (res.confirm) {
            this.performSubmitOrder();
          }
        }
      });
    },
    
        // 执行提交订单
    async performSubmitOrder() {
      // 构建积分订单数据
      const orderData = {
        integralProductId: parseFloat(this.integralProductId),  // 积分商品ID
        productId: parseFloat(this.productId),                  // 具体商品ID（规格商品ID）
        productNum: parseFloat(this.productInfo.quantity),      // 商品数量
        attrValueId: parseFloat(this.attrValueId),             // 属性值ID
        customData: this.customData ? JSON.stringify(this.customData) : "",  // 自定义数据
        optometryId: this.optometryId || null,                 // 验光单ID
        addressId: this.addressId,                             // 收货地址ID
        storeId: this.storeId,                                 // 店铺ID
        shippingType: 3,
        totalIntegral: this.productInfo.totalPoints            // 总积分
      };
      
      console.log('积分订单数据:', orderData);
      
      try {
        uni.showLoading({
          title: '提交中...'
        });
        
        // 调用积分订单创建接口
        const res = await createIntegralOrder(orderData);
        
        uni.hideLoading();
        
        if (res.code === 200) {
          uni.showToast({
            title: '订单提交成功',
            icon: 'success'
          });
          
          console.log('订单创建成功:', res.data);
          
          // 跳转到积分兑换记录页面
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/users/user_integral_exchange/index'
            });
          }, 1500);
        } else {
          uni.showToast({
            title: res.message || '订单提交失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('提交订单失败:', error);
        uni.showToast({
          title: error.message || '网络请求失败',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.order-settlement {
  background-color: #F5F5F5;
  min-height: 100vh;
  padding-bottom: 120upx;
}

/* 收货地址模块 */
.address-section {
  background: #FFFFFF;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  
  .address-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    
    .address-title {
      font-size: 28rpx;
      color: #FF2222;
    }
  }
  
  .address-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    
    .address-detail {
      flex: 1;
      
      .address-name-phone {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        
        .name {
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          margin-right: 20rpx;
        }
        
        .phone {
          font-size: 26rpx;
          color: #666666;
        }
      }
      
      .address-full {
        font-size: 26rpx;
        color: #666666;
        line-height: 36rpx;
      }
    }
  }
}

/* 配送方式和商品信息模块 */
.delivery-product-section {
  background: #FFFFFF;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .delivery-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 30rpx;
    border-bottom: 1rpx solid #F5F5F5;
    margin-bottom: 30rpx;
    
    .platform-name {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }
    
    .delivery-type {
      .delivery-label {
        font-size: 24rpx;
        color: #FFFFFF;
        background: #5DADE2;
        padding: 8rpx 16rpx;
        border-radius: 12rpx;
      }
    }
  }
  
  .product-item {
    margin-bottom: 30rpx;
    
    /* 上部分：图片 + 商品信息 */
    .product-upper {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20rpx;
      
      .product-image {
        width: 175rpx;
        height: 175rpx;
        border-radius: 12rpx;
        margin-right: 20rpx;
        flex-shrink: 0;
      }
      
      .product-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 120rpx;
        
        /* 右侧上部分：标题 */
        .product-title {
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
          margin-bottom: 15rpx;
          font-weight: 500;
          flex: 1;
        }
        
        /* 右侧下部分：规格价格 + 数量 */
        .product-bottom {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          
          .product-left-info {
            display: flex;
            flex-direction: column;
            
            .product-spec {
              font-size: 24rpx;
              color: #666666;
              margin-bottom: 15rpx;
            }
            
            .product-price {
              font-size: 28rpx;
              color: #333333;
              font-weight: 500;
            }
          }
          
          .product-quantity {
            font-size: 24rpx;
            color: #666666;
          }
        }
      }
    }
    
    /* 下部分：商品描述 */
    .product-description {
      background: #F5F5F5;
      border-radius: 12rpx;
      padding: 20rpx;
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
      margin-left: 0;
    }
  }
  
  .total-points {
    text-align: right;
    border-top: 1rpx solid #F5F5F5;
    padding-top: 20rpx;
    
    .total-label {
      font-size: 28rpx;
      color: #333333;
    }
    
    .total-value {
      font-size: 32rpx;
      color: #FF2222;
      font-weight: bold;
      margin-left: 8rpx;
    }
  }
}

/* 价格明细模块 */
.price-detail-section {
  background: #FFFFFF;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  
  .price-header {
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #F5F5F5;
    
    .price-title {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }
  }
  
  .price-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    
    .price-label {
      font-size: 28rpx;
      color: #333333;
    }
    
    .price-value {
      font-size: 28rpx;
      color: #333333;
    }
    
    &.total-item {
      border-top: 1rpx solid #F5F5F5;
      
      .price-label,
      .price-value {
        font-weight: 500;
        color: #FF2222;
      }
    }
  }
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #E5E5E5;
  
  .payment-info {
    flex: 1;
    
    .payment-label {
      font-size: 28rpx;
      color: #333333;
    }
    
    .payment-amount {
      font-size: 32rpx;
      color: #FF2222;
      font-weight: bold;
      margin-left: 8rpx;
    }
  }
  
  .submit-btn {
    width: 240rpx;
    height: 80rpx;
    background: #BDFD5B;
    color: #222222;
    border: none;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20rpx;
  }
}

/* 积分订单页面中 glassesProductCustomInfo 组件的 lens 样式定制 */
/deep/ .glasses  {
  width: 100%!important;
}
/deep/ .glasses .lens {
  background-color: #f5f5f5;
  color: #999999!important;
  width: 100%;
  border-radius: 12rpx!important;
  padding: 8rpx 12rpx!important;
  text-align: start;
  font-size: 24rpx!important;
  margin-bottom: 20rpx!important;
}
</style> 