<template>
	<view class="select-refund-page">
		<!-- 店铺信息和商品列表在同一个白色背景中 -->
		<view class="content-container">
			<!-- 商店信息 -->
			<view class="merchant-info">
				<text class="merchant-name">{{ merchantInfo.merName || '商店' }}</text>
				<text class="delivery-tag">{{ getDeliveryTag() }}</text>
			</view>
			
			<!-- 商品列表 -->
			<view class="products-list">
				<view class="product-item" v-for="(product, index) in productList" :key="index">
					<!-- 商品主要信息：选择框 + 图片 + 信息 + 数量 -->
					<view class="product-main">
						<!-- 左侧选择框（独占一列） -->
						<view class="product-checkbox" @click="toggleProduct(index)">
							<view class="checkbox" :class="{ 'checked': product.selected }">
								<text class="iconfont icon-duihao" v-if="product.selected"></text>
							</view>
						</view>
						
						<!-- 商品图片 -->
						<view class="product-image">
							<image :src="product.image" mode="aspectFill"></image>
						</view>
						
						<!-- 商品信息 -->
						<view class="product-info">
							<view class="product-name">{{ product.name }}</view>
							<view class="product-spec">{{ product.spec }}</view>
							<view class="product-price">¥{{ product.price }}</view>
						</view>
						
						<!-- 商品数量 -->
						<view class="product-quantity">x{{ product.quantity }}</view>
					</view>
					
					<!-- 商品描述 -->
					<view class="product-description" v-if="product.description">
						{{ product.description }}
					</view>
					
					<!-- 验光单信息（如果有） -->
					<view v-if="product.hasOptometry" class="optometry-section" @click="viewOptometryInfo">
						<text>查看验光单信息</text>
						<text class="iconfont icon-xiangyou"></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-btn">
			<view class="next-btn" :class="{ 'disabled': !hasSelectedProducts }" @click="goNext">下一步</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderNo: '',
				aftersaleType: '',
				reason: '',
				merchantInfo: {
					merName: '',
					shippingType: 1
				},
				productList: []
			};
		},
		computed: {
			hasSelectedProducts() {
				return this.productList.some(product => product.selected);
			}
		},
		onLoad(options) {
			if (options.orderNo) {
				this.orderNo = options.orderNo;
			}
			if (options.aftersaleType) {
				this.aftersaleType = options.aftersaleType;
			}
			if (options.reason) {
				this.reason = decodeURIComponent(options.reason);
			}
			if (options.merchantData) {
				try {
					const merchantData = JSON.parse(decodeURIComponent(options.merchantData));
					this.merchantInfo = {
						merName: merchantData.merName,
						shippingType: merchantData.shippingType
					};
					// 转换商品数据格式
					this.productList = merchantData.orderInfoList.map((product, index) => ({
						id: product.productId || index,
						name: product.productName,
						spec: product.sku,
						price: product.price,
						quantity: product.payNum,
						image: product.image,
						description: '', // 订单详情中没有description字段
						hasOptometry: product.customData && (product.customData.lens || product.customData.optometry),
						selected: index === 0, // 默认选中第一个商品
						// 保留原始数据用于后续处理
						originalData: product
					}));
				} catch (e) {
					console.error('解析商品数据失败:', e);
					uni.showToast({
						title: '数据解析失败',
						icon: 'none'
					});
				}
			}
		},
		methods: {
			/**
			 * 获取配送方式标签文本
			 */
			getDeliveryTag() {
				switch (this.merchantInfo.shippingType) {
					case 1:
						return '快递';
					case 2:
						return '自提';
					case 3:
						return '配送';
					default:
						return '配送';
				}
			},
			toggleProduct(index) {
				this.productList[index].selected = !this.productList[index].selected;
			},
			viewOptometryInfo() {
				console.log('查看验光单信息');
				uni.showToast({
					title: '查看验光单信息',
					icon: 'none'
				});
			},
			goNext() {
				if (!this.hasSelectedProducts) {
					uni.showToast({
						title: '请选择要退货的商品',
						icon: 'none'
					});
					return;
				}
				
				const selectedProducts = this.productList.filter(product => product.selected);
				console.log('选中的商品:', selectedProducts);
				console.log('售后类型:', this.aftersaleType);
				console.log('售后原因:', this.reason);
				
				// 跳转到申请售后页面，传递选中的商品和售后信息
				const params = {
					orderNo: this.orderNo,
					products: encodeURIComponent(JSON.stringify(selectedProducts)),
					type: this.aftersaleType,
					reason: this.reason
				};
				
				uni.navigateTo({
					url: `/pages/goods/refund_apply/index?${Object.keys(params).map(key => `${key}=${params[key]}`).join('&')}`
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.select-refund-page {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
		padding: 0 30rpx 120rpx;
	}
	
	.content-container {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-top: 20rpx;
	}
	
	.merchant-info {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		
		.merchant-name {
			font-size: 30rpx;
			color: #333;
			font-weight: 600;
			margin-right: 15rpx;
		}
		
		.delivery-tag {
			background-color: #409EFF;
			color: #fff;
			font-size: 22rpx;
			padding: 6rpx 12rpx;
			border-radius: 6rpx;
		}
	}
	
	.products-list {
		/* 移除背景色和padding，因为已经在父容器中设置 */
	}
	
	.product-item {
		&:not(:last-child) {
			margin-bottom: 30rpx;
			padding-bottom: 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
		}
		
		.product-main {
			display: flex;
			align-items: flex-start;
			margin-bottom: 20rpx;
			
			.product-checkbox {
				margin-right: 20rpx;
				align-self: center;
				
				.checkbox {
					width: 40rpx;
					height: 40rpx;
					border: 2rpx solid #ddd;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					
					&.checked {
						background-color: #52c41a;
						border-color: #52c41a;
						
						.iconfont {
							color: #fff;
							font-size: 24rpx;
						}
					}
				}
			}
			
			.product-image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 12rpx;
				overflow: hidden;
				margin-right: 20rpx;
				flex-shrink: 0;
				
				image {
					width: 100%;
					height: 100%;
				}
			}
			
			.product-info {
				flex: 1;
				height: 160rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				
				.product-name {
					font-size: 28rpx;
					color: #333;
					font-weight: 600;
					line-height: 1.4;
				}
				
				.product-spec {
					font-size: 26rpx;
					color: #666;
				}
				
				.product-price {
					font-size: 32rpx;
					color: #FF2222;
					font-weight: 600;
				}
			}
			
			.product-quantity {
				align-self: center;
				font-size: 26rpx;
				color: #222222;
				margin-left: 20rpx;
			}
		}
		
		.product-description {
			background-color: #f5f5f5;
			border-radius: 12rpx;
			padding: 20rpx;
			font-size: 24rpx;
			color: #999999;
			line-height: 1.5;
			margin-bottom: 20rpx;
			margin-left: 60rpx; // 与选择框对齐（40rpx选择框宽度 + 20rpx间距）
		}
		
		.optometry-section {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 20rpx;
			font-size: 26rpx;
			color: #222222;
			margin-left: 60rpx; // 与选择框对齐（40rpx选择框宽度 + 20rpx间距）
			
			.iconfont {
				color: #999999;
				font-size: 24rpx;
			}
		}
	}
	
	.bottom-btn {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		padding: 30rpx;
		border-top: 1rpx solid #f5f5f5;
		
		.next-btn {
			width: 100%;
			height: 80rpx;
			background-color: #BDFD5B;
			color: #333;
			font-size: 32rpx;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 20rpx;
			
			&.disabled {
				background-color: #ddd;
				color: #999;
			}
		}
	}
</style> 